#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级电子教材PDF解析器
专门用于解析复杂的电子教材PDF，识别章节、标题、图片、表格等结构
供Java程序调用，输出结构化JSON数据
"""

import sys
import json
import re
import PyPDF2
import pdfplumber
from typing import List, Dict, Any, Optional, Tuple
import traceback
import argparse


class TextbookStructureAnalyzer:
    """电子教材结构分析器"""
    
    def __init__(self):
        # 章节标题模式
        self.chapter_patterns = [
            r'^第[一二三四五六七八九十\d]+章\s*(.+)$',
            r'^第[一二三四五六七八九十\d]+节\s*(.+)$',
            r'^第[一二三四五六七八九十\d]+部分\s*(.+)$',
            r'^第[一二三四五六七八九十\d]+讲\s*(.+)$',
        ]
        
        # 标题模式（按优先级排序）
        self.heading_patterns = [
            (r'^(\d+\.\d+\.\d+)\s+(.+)$', 3),  # 1.1.1 三级标题
            (r'^(\d+\.\d+)\s+(.+)$', 2),       # 1.1 二级标题
            (r'^(\d+\.)\s+(.+)$', 1),          # 1. 一级标题
            (r'^([一二三四五六七八九十]+)[、．]\s*(.+)$', 2),  # 一、二级标题
            (r'^[(（]([一二三四五六七八九十\d]+)[)）]\s*(.+)$', 3),  # (1) 三级标题
            (r'^([A-Z])[\.、]\s*(.+)$', 3),    # A. 三级标题
        ]
        
        # 图片引用模式
        self.image_patterns = [
            r'图\s*(\d+[-\d]*)\s*(.+)',
            r'表\s*(\d+[-\d]*)\s*(.+)',
            r'附图\s*(\d+[-\d]*)\s*(.+)',
        ]
        
        # 列表模式
        self.list_patterns = [
            (r'^[(（](\d+)[)）]\s*(.+)$', 'ordered'),
            (r'^(\d+)[\.、]\s*(.+)$', 'ordered'),
            (r'^[•·▪▫◦‣⁃]\s*(.+)$', 'bullet'),
            (r'^[-*+]\s*(.+)$', 'bullet'),
        ]
        
        # 特殊框模式
        self.special_box_patterns = [
            r'^(带你走进.+)$',
            r'^(知识拓展.+)$',
            r'^(案例分析.+)$',
            r'^(思考题.+)$',
            r'^(注意.+)$',
            r'^(提示.+)$',
        ]
    
    def identify_element_type(self, text: str, line_number: int = 0) -> Tuple[str, Dict[str, Any]]:
        """识别文本元素类型"""
        text_stripped = text.strip()
        
        if not text_stripped:
            return 'empty', {}
        
        # 1. 检查是否是页码
        if re.match(r'^\d+\s*$', text_stripped) and len(text_stripped) <= 3:
            return 'page_number', {'number': int(text_stripped)}
        
        # 2. 检查是否是章节标题
        for pattern in self.chapter_patterns:
            match = re.match(pattern, text_stripped)
            if match:
                return 'chapter_header', {
                    'title': match.group(1) if match.groups() else text_stripped,
                    'full_text': text_stripped
                }
        
        # 3. 检查是否是标题
        for pattern, level in self.heading_patterns:
            match = re.match(pattern, text_stripped)
            if match:
                groups = match.groups()
                if len(groups) >= 2:
                    return 'heading', {
                        'level': level,
                        'number': groups[0],
                        'text': groups[1],
                        'full_text': text_stripped
                    }
                else:
                    return 'heading', {
                        'level': level,
                        'text': groups[0] if groups else text_stripped,
                        'full_text': text_stripped
                    }
        
        # 4. 检查是否是图片/表格引用
        for pattern in self.image_patterns:
            match = re.match(pattern, text_stripped)
            if match:
                return 'image_reference', {
                    'type': 'image' if text_stripped.startswith('图') else 'table',
                    'number': match.group(1),
                    'caption': match.group(2),
                    'text': text_stripped
                }
        
        # 5. 检查是否是列表项
        for pattern, list_type in self.list_patterns:
            match = re.match(pattern, text_stripped)
            if match:
                groups = match.groups()
                return 'list_item', {
                    'type': list_type,
                    'number': groups[0] if len(groups) > 1 else None,
                    'text': groups[-1],
                    'full_text': text_stripped
                }
        
        # 6. 检查是否是特殊框
        for pattern in self.special_box_patterns:
            if re.match(pattern, text_stripped):
                return 'special_box', {
                    'text': text_stripped,
                    'box_type': 'info'
                }
        
        # 7. 默认为段落
        # 检查是否可能是缩进段落
        indent_level = 0
        if text.startswith('  '):
            indent_level = 1
        elif text.startswith('    '):
            indent_level = 2
        
        return 'paragraph', {
            'text': text_stripped,
            'indent': indent_level,
            'is_long': len(text_stripped) > 100
        }


class TextbookPDFParser:
    """电子教材PDF解析器"""
    
    def __init__(self):
        self.analyzer = TextbookStructureAnalyzer()
    
    def extract_pdf_content(self, pdf_path: str) -> Tuple[str, List[Any]]:
        """提取PDF内容和表格"""
        text_content = ""
        tables = []
        
        try:
            # 优先使用pdfplumber
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
                    
                    # 提取表格
                    page_tables = page.extract_tables()
                    if page_tables:
                        tables.extend(page_tables)
                        
        except Exception as e:
            print(f"pdfplumber解析失败，尝试PyPDF2: {e}", file=sys.stderr)
            
            # fallback到PyPDF2
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text_content += page.extract_text() + "\n"
            except Exception as e2:
                raise Exception(f"PDF解析完全失败: {e2}")
        
        return text_content, tables
    
    def split_into_pages(self, text: str) -> List[str]:
        """将文本按页分割"""
        lines = text.split('\n')
        pages = []
        current_page = []
        
        for line in lines:
            line_stripped = line.strip()
            
            # 检查是否是页码（单独一行的数字）
            if re.match(r'^\d+\s*$', line_stripped) and len(line_stripped) <= 3:
                # 如果当前页有内容，保存当前页
                if current_page:
                    pages.append('\n'.join(current_page))
                    current_page = []
                # 页码作为新页的开始
                current_page.append(line)
            else:
                current_page.append(line)
        
        # 添加最后一页
        if current_page:
            pages.append('\n'.join(current_page))
        
        return pages
    
    def parse_page_content(self, page_text: str, page_number: int) -> Dict[str, Any]:
        """解析单页内容"""
        lines = page_text.split('\n')
        elements = []
        current_list = None
        current_list_type = None
        
        for i, line in enumerate(lines):
            if not line.strip():
                continue
            
            element_type, element_data = self.analyzer.identify_element_type(line, i)
            
            # 处理列表项的聚合
            if element_type == 'list_item':
                if current_list is None or current_list_type != element_data['type']:
                    # 开始新列表
                    if current_list is not None:
                        elements.append(current_list)
                    
                    current_list = {
                        'type': f"{element_data['type']}_list",
                        'items': []
                    }
                    current_list_type = element_data['type']
                
                current_list['items'].append({
                    'text': element_data['text'],
                    'number': element_data.get('number')
                })
            else:
                # 非列表项，结束当前列表
                if current_list is not None:
                    elements.append(current_list)
                    current_list = None
                    current_list_type = None
                
                # 添加当前元素
                if element_type != 'empty':
                    element_data['type'] = element_type
                    elements.append(element_data)
        
        # 处理未完成的列表
        if current_list is not None:
            elements.append(current_list)
        
        return {
            'page_number': page_number,
            'elements': elements
        }
    
    def parse_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """解析PDF文件"""
        try:
            # 1. 提取PDF内容
            text_content, tables = self.extract_pdf_content(pdf_path)
            
            # 2. 按页分割
            pages_text = self.split_into_pages(text_content)
            
            # 3. 解析每页内容
            pages_data = []
            for i, page_text in enumerate(pages_text):
                page_data = self.parse_page_content(page_text, i + 1)
                pages_data.append(page_data)
            
            # 4. 构建完整结果
            result = {
                'total_pages': len(pages_data),
                'pages': pages_data,
                'tables': tables,
                'metadata': {
                    'original_length': len(text_content),
                    'source_file': pdf_path
                }
            }
            
            return result
            
        except Exception as e:
            error_result = {
                'error': str(e),
                'traceback': traceback.format_exc(),
                'pages': [],
                'tables': []
            }
            return error_result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='电子教材PDF解析器')
    parser.add_argument('pdf_path', help='PDF文件路径')
    parser.add_argument('--output', '-o', help='输出JSON文件路径')
    parser.add_argument('--pretty', '-p', action='store_true', help='格式化输出JSON')

    args = parser.parse_args()

    try:
        # 创建解析器
        pdf_parser = TextbookPDFParser()

        # 解析PDF
        result = pdf_parser.parse_pdf(args.pdf_path)

        # 输出结果
        if args.pretty:
            json_output = json.dumps(result, ensure_ascii=False, indent=2)
        else:
            json_output = json.dumps(result, ensure_ascii=False)

        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(json_output)
            print(f"结果已保存到: {args.output}", file=sys.stderr)
        else:
            # 修复：使用sys.stdout.buffer.write来避免编码问题
            sys.stdout.buffer.write(json_output.encode('utf-8'))
            sys.stdout.buffer.flush()

        # 输出统计信息到stderr
        if 'error' not in result:
            print(f"解析完成: {result['total_pages']}页, {len(result['tables'])}个表格", file=sys.stderr)
        else:
            print(f"解析失败: {result['error']}", file=sys.stderr)
            sys.exit(1)

    except Exception as e:
        error_output = {
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        # 修复：使用sys.stdout.buffer.write来避免编码问题
        error_json = json.dumps(error_output, ensure_ascii=False)
        sys.stdout.buffer.write(error_json.encode('utf-8'))
        sys.stdout.buffer.flush()
        print(f"程序异常: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
