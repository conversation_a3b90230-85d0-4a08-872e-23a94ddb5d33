package cn.dutp.common.ai.common.ai.utils.baidu;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;

import static cn.dutp.common.ai.common.ai.utils.baidu.BaiduCommonUtil.HTTP_CLIENT;

public class TextToVideoUtil {
    public static String getResponse(String textToVideoUrl, String context, String token) throws IOException {
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, getRequestBody(context));
        Request request = new Request.Builder()
                .url(textToVideoUrl + token)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        response.message();
        String res = response.body().string();
        return res;
    }

    private static String getRequestBody(String textContent){

        // 创建 JSON 对象
        JSONObject json = new JSONObject();
        JSONObject source = new JSONObject();
        JSONObject struct = new JSONObject();

        // 设置结构体内容
        struct.put("type", "text");
        struct.put("text", textContent);

        // 将结构体添加到源中
        source.put("structs", new JSONArray().put(struct));

        // 设置配置内容
        JSONObject config = new JSONObject();
        config.put("productType", "video");
        config.put("duration", -1);
        config.put("resolution", new JSONArray().put(1280).put(720));

        // 将源和配置添加到主 JSON 对象
        json.put("source", source);
        json.put("config", config);
        return json.toString();
    }
}
