package cn.dutp.common.security.interceptor;

import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.constant.TokenConstants;
import cn.dutp.common.core.context.SecurityContextHolder;
import cn.dutp.common.core.utils.ServletUtils;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.security.auth.AuthUtil;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
@Slf4j
public class LocalInterceptor implements AsyncHandlerInterceptor
{
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        // 从请求头中获取语言信息
        String language = ServletUtils.getHeader(request, TokenConstants.LANGUAGE);
        // 使用自定义的字符串工具类判断语言信息是否非空
        if (StringUtils.isEmpty(language)) {
            language = TokenConstants.DEFAULT_LANGUAGE;
        }
        // 假设语言信息格式为"zh_CN"或"en_US"，这里通过下划线分割获取语言和地区信息
        String[] languageParts = language.split("_");
        // 确保分割后有两个部分
        if (languageParts.length == 2) {
            // 创建Locale对象
            Locale locale = new Locale(languageParts[0], languageParts[1]);
            // 设置当前线程的Locale
            LocaleContextHolder.setLocale(locale);
        } else {
            // 如果格式不正确，可以记录日志或进行其他处理
            log.warn("Invalid language format: {}", language);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception
    {
    }
}
