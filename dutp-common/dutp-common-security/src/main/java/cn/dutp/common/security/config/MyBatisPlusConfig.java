package cn.dutp.common.security.config;

import cn.dutp.common.security.handler.CustomMetaObjectHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * MyBatisPlus配置
 *
 * <AUTHOR>
 */
@Configuration
public class MyBatisPlusConfig
{
    @Bean
    @Primary
    public CustomMetaObjectHandler metaObjectHandler() {
        return new CustomMetaObjectHandler();
    }
}
