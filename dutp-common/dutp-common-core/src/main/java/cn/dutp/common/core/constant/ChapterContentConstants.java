package cn.dutp.common.core.constant;

/**
 * 编辑器mongodb存储常量
 *
 * <AUTHOR>
 */
public class ChapterContentConstants {

    /**
     * 正常版本存储key
     */
    public static final String BOOK_CHAPTER_CONTENT = "bookChapterContent";

    /**
     * 原始版本存储key
     */
    public static final String BOOK_CHAPTER_ORIGINAL_CONTENT = "bookChapterOriginalContent";

    /**
     * 版本历史存储key
     */
    public static final String BOOK_CHAPTER_VERSION_CONTENT = "bookChapterVersionContent";

    /**
     * 备份版本存储key
     */
    public static final String BOOK_CHAPTER_CONTENT_BACKUP = "bookChapterContentBackup";

}
