<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.dutp</groupId>
    <artifactId>dutp</artifactId>
    <version>1.0.0</version>

    <name>dutp</name>
    <url>http://www.dutp.vip</url>
    <description>厚仁微服务系统</description>

    <properties>
        <dutp.version>1.0.0</dutp.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <spring-framework.version>5.3.33</spring-framework.version>
        <spring-boot-admin.version>2.7.15</spring-boot-admin.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.0.0</pagehelper.boot.version>
        <druid.version>1.2.23</druid.version>
        <dynamic-ds.version>4.2.0</dynamic-ds.version>
        <commons.io.version>2.13.0</commons.io.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>2.0.43</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <springdoc.version>1.6.9</springdoc.version>
        <transmittable-thread-local.version>2.14.4</transmittable-thread-local.version>
        <mp.version>3.4.2</mp.version>
        <lombok.versin>1.18.12</lombok.versin>
        <hutool.version>5.5.8</hutool.version>
        <httpclient.version>4.5.12</httpclient.version>
        <aliyun.sms.version>3.0.0</aliyun.sms.version>
        <aliyun.oss.version>4.6.3</aliyun.oss.version>
        <easy-es.version>2.0.0</easy-es.version>
        <elasticsearch.version>7.14.0</elasticsearch.version>
        <websocket.version>3.3.4</websocket.version>
        <rocketmq.version>2.2.1</rocketmq.version>
        <ip2region.version>2.7.0</ip2region.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- iText核心库 -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>********</version>
            </dependency>

            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-encrypt</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- iText中文字体支持 -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>5.2.0</version>
            </dependency>
            <!-- 用于 EMF 解析和渲染 -->
            <dependency>
                <groupId>org.freehep</groupId>
                <artifactId>freehep-graphics2d</artifactId>
                <version>2.4</version>
            </dependency>
            <dependency>
                <groupId>org.freehep</groupId>
                <artifactId>freehep-graphicsio-emf</artifactId>
                <version>2.1.3</version>
            </dependency>
            <dependency>
                <groupId>org.freehep</groupId>
                <artifactId>freehep-io</artifactId>
                <version>2.0.5</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.15.4</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.29</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-codec</artifactId>
                <version>1.16</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-transcoder</artifactId>
                <version>1.16</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-all</artifactId>
                <version>1.16</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- webscoket -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${websocket.version}</version>
            </dependency>

            <!-- rocketmq -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.versin}</version>
            </dependency>
            <!--hutool-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>fluent-hc</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <!--mybatis-plus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mp.version}</version>
            </dependency>

            <!--SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- Springdoc webmvc 依赖配置 -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>ooxml-schemas</artifactId>
                <version>1.4</version>
            </dependency>
            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 阿里云sms -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.sms.version}</version>
            </dependency>

            <!-- 阿里云oss -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun.oss.version}</version>
            </dependency>

            <!--阿里云上传文件客户端，用的时候手动引入-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.8.0</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>sts20150401</artifactId>
                <version>1.1.4</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-core</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-swagger</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-security</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 数据脱敏 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-sensitive</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-datascope</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-datasource</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-seata</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-log</artifactId>
                <version>${dutp.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-redis</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!-- 邮件服务 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-mail</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!-- OSS服务 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-oss</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!-- elasticSearch -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-es</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!--sms-->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-sms</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!-- websocket -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-websocket</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!-- rocketmq -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-rocketmq</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!-- 系统接口 -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-api-system</artifactId>
                <version>${dutp.version}</version>
            </dependency>
            <!-- mongoDB -->
            <dependency>
                <groupId>cn.dutp</groupId>
                <artifactId>dutp-common-mongo</artifactId>
                <version>${dutp.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>dutp-auth</module>
        <module>dutp-gateway</module>
        <module>dutp-visual</module>
        <module>dutp-modules</module>
        <module>dutp-api</module>
        <module>dutp-common</module>
        <module>dutp-api/dutp-api-pojo</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
<!--                <filtering>true</filtering>-->
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <profiles>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--测试环境-->
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <!--生产环境-->
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>
</project>