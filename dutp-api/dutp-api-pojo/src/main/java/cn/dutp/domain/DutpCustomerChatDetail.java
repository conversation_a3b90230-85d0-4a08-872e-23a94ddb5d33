package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 客服聊天明细对象 dutp_customer_chat_detail
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@TableName("dutp_customer_chat_detail")
public class DutpCustomerChatDetail extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long detailId;

    /**
     * 对话ID
     */
    @Excel(name = "对话ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chatId;

    /**
     * 1客服2用户3状态
     */
    @Excel(name = "1客服2用户3状态")
    private Integer userType;

    /**
     * 用户名id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 对话内容
     */
    @Excel(name = "对话内容")
    private String chatContent;

    /**
     * 0待审核1审核通过2审核不通过
     */
    @Excel(name = "0待审核1审核通过2审核不通过")
    private Integer state;

    /**
     * 内容类型1文本2图片3文件
     */
    @Excel(name = "内容类型1文本2图片3文件")
    private Integer contentType;

    /**
     * 0未读1已读
     */
    @Excel(name = "0未读1已读")
    private Integer readFlag;

    @TableField(exist = false)
    private Integer chatStatus;

    /**
     * 客服人员
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long chatUserId;

    /**
     * 排序，1待开启会话2正在会话3会话关闭
     */
    @TableField(exist = false)
    private Integer sortValue;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("detailId", getDetailId())
                .append("chatId", getChatId())
                .append("userType", getUserType())
                .append("chatContent", getChatContent())
                .append("state", getState())
                .append("contentType", getContentType())
                .append("readFlag", getReadFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("chatStatus", getChatStatus())
                .append("sortValue", getSortValue())
                .toString();
    }
}
