package cn.dutp.shop.api.factory;

import cn.dutp.common.core.domain.R;
import cn.dutp.shop.api.RemoteDtbBookOrderService;
import cn.dutp.shop.api.domain.DtbBookOrderApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteDtbBookOrderFallbackFactory implements FallbackFactory<RemoteDtbBookOrderService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteDtbBookOrderFallbackFactory.class);

    @Override
    public RemoteDtbBookOrderService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteDtbBookOrderService()
        {
            @Override
            public R<Object> cancelDtbBookOrder(DtbBookOrderApi dtbBookOrderApi)
            {
                return R.fail("删除订单失败:" + throwable.getMessage());
            }
        };
    }
}
