package cn.dutp.system.api.factory;

import cn.dutp.common.core.domain.R;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.domain.DutpUserWithCode;
import cn.dutp.system.api.domain.SysRole;
import cn.dutp.system.api.domain.SysUser;
import cn.dutp.system.api.model.LoginDutpUser;
import cn.dutp.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> infoByTel(String userTel, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
            @Override
            public R<LoginDutpUser> infoDutpUserByTel(String userTel, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
            @Override
            public R<LoginDutpUser> getDutpUserInfo(String username, String source)
            {
                return R.fail("获取教师，学生，无身份者用户失败:" + throwable.getMessage());
            }
            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }
            @Override
            public R<Boolean> registerDutpUserUserInfo(DutpUser dutpUser, String source)
            {
                return R.fail("注册教师，学生，无身份者用户失败:" + throwable.getMessage());
            }
            @Override
            public R<Boolean> forgotPasswordEducation(DutpUserWithCode dutpUser, String source)
            {
                return R.fail("找回密码失败:" + throwable.getMessage());
            }
            @Override
            public R<Boolean> recordUserLogin(SysUser sysUser, String source)
            {
                return R.fail("记录用户登录信息失败:" + throwable.getMessage());
            }
            @Override
            public R<LoginUser> getEduUserInfo(String username, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> infoEduByTel(String userTel, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> listUserByRoleId(Long roleId, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> listUserByMenuId(Long menuId, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<DutpUser>> listUserByBookIdAndSchoolId(DutpUser user, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysRole>> eduRoleList() {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

        };
    }
}
