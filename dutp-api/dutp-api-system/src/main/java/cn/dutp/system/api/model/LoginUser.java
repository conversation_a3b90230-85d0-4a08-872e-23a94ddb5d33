package cn.dutp.system.api.model;

import java.io.Serializable;
import java.util.Set;

import cn.dutp.common.core.context.SecurityContextHolder;
import cn.dutp.system.api.domain.SysUser;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
public class LoginUser implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 用户名id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userid;

    /**
     * 用户名
     */
    private String username;

    /**
     * 登录时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long loginTime;

    /**
     * 过期时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 用户类型1，管理用户 2.教务 3.教师学生
     */
    private Integer userType;

    /**
     * 前端用户类型0读者1学生2教师
     */
    private Integer homeUserType;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 学校ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;


    /**
     * 用户所属岗位组
     */
    private String userPostGroup;

    /**
     * 用户信息
     */
    private SysUser sysUser;

    public Integer getHomeUserType() {
        return homeUserType;
    }

    public void setHomeUserType(Integer homeUserType) {
        this.homeUserType = homeUserType;
    }

    public Long getSchoolId() {
        return schoolId;
    }

    public void setSchoolId(Long schoolId) {
        this.schoolId = schoolId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getToken()
    {
        return token;
    }

    public void setToken(String token)
    {
        this.token = token;
    }

    public Long getUserid()
    {
        return userid;
    }

    public void setUserid(Long userid)
    {
        this.userid = userid;
    }

    public String getUsername()
    {
        return username;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public Long getLoginTime()
    {
        return loginTime;
    }

    public void setLoginTime(Long loginTime)
    {
        this.loginTime = loginTime;
    }

    public Long getExpireTime()
    {
        return expireTime;
    }

    public void setExpireTime(Long expireTime)
    {
        this.expireTime = expireTime;
    }

    public String getIpaddr()
    {
        return ipaddr;
    }

    public void setIpaddr(String ipaddr)
    {
        this.ipaddr = ipaddr;
    }

    public Set<String> getPermissions()
    {
        return permissions;
    }

    public void setPermissions(Set<String> permissions)
    {
        this.permissions = permissions;
    }

    public Set<String> getRoles()
    {
        return roles;
    }

    public void setRoles(Set<String> roles)
    {
        this.roles = roles;
    }

    public SysUser getSysUser()
    {
        return sysUser;
    }

    public void setSysUser(SysUser sysUser)
    {
        this.sysUser = sysUser;
    }

    public SysUser getUser() {
        return sysUser;
    }

    public String getUserPostGroup()
    {
        return userPostGroup;
    }

    public void setUserPostGroup(String userPostGroup)
    {
        this.userPostGroup = userPostGroup;
    }
}
