package cn.dutp.im;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 客服模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
@EnableDiscoveryClient
public class DutpImApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpImApplication.class, args);
        System.out.println("IM模块启动成功");
    }
}
