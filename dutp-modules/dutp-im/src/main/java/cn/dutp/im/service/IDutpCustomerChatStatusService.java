package cn.dutp.im.service;

import java.util.List;

import cn.dutp.im.domain.DutpCustomerChatStatus;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 聊天状态Service接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IDutpCustomerChatStatusService extends IService<DutpCustomerChatStatus>
{
    /**
     * 查询聊天状态
     *
     * @param statusId 聊天状态主键
     * @return 聊天状态
     */
    public DutpCustomerChatStatus selectDutpCustomerChatStatusByStatusId(Long statusId);

    /**
     * 查询聊天状态列表
     *
     * @param dutpCustomerChatStatus 聊天状态
     * @return 聊天状态集合
     */
    public List<DutpCustomerChatStatus> selectDutpCustomerChatStatusList(DutpCustomerChatStatus dutpCustomerChatStatus);

    /**
     * 新增聊天状态
     *
     * @param dutpCustomerChatStatus 聊天状态
     * @return 结果
     */
    public boolean insertDutpCustomerChatStatus(DutpCustomerChatStatus dutpCustomerChatStatus);

    /**
     * 修改聊天状态
     *
     * @param dutpCustomerChatStatus 聊天状态
     * @return 结果
     */
    public boolean updateDutpCustomerChatStatus(DutpCustomerChatStatus dutpCustomerChatStatus);

    /**
     * 批量删除聊天状态
     *
     * @param statusIds 需要删除的聊天状态主键集合
     * @return 结果
     */
    public boolean deleteDutpCustomerChatStatusByStatusIds(List<Long> statusIds);

}
