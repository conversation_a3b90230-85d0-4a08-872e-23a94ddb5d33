package cn.dutp.im.service;

import cn.dutp.domain.DutpCustomerChatDetail;
import cn.dutp.im.domain.DutpCustomer;
import cn.dutp.im.domain.vo.DutpCustomerChatDetailVO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;
/**
 * 客服聊天明细Service接口
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
public interface IDutpCustomerChatDetailService extends IService<DutpCustomerChatDetail>
{
    /**
     * 查询客服聊天明细
     *
     * @param detailId 客服聊天明细主键
     * @return 客服聊天明细
     */
    public DutpCustomerChatDetail selectDutpCustomerChatDetailByDetailId(Long detailId);

    /**
     * 查询客服聊天明细列表
     *
     * @param dutpCustomerChatDetail 客服聊天明细
     * @return 客服聊天明细集合
     */
    public List<DutpCustomerChatDetail> selectDutpCustomerChatDetailList(DutpCustomerChatDetail dutpCustomerChatDetail);

    /**
     * 新增客服聊天明细
     *
     * @param dutpCustomerChatDetail 客服聊天明细
     * @return 结果
     */
    public boolean insertDutpCustomerChatDetail(DutpCustomerChatDetail dutpCustomerChatDetail);

    /**
     * 修改客服聊天明细
     *
     * @param dutpCustomerChatDetail 客服聊天明细
     * @return 结果
     */
    public boolean updateDutpCustomerChatDetail(DutpCustomerChatDetail dutpCustomerChatDetail);

    /**
     * 批量删除客服聊天明细
     *
     * @param detailIds 需要删除的客服聊天明细主键集合
     * @return 结果
     */
    public boolean deleteDutpCustomerChatDetailByDetailIds(List<Long> detailIds);

    DutpCustomerChatDetail sendMessage(DutpCustomerChatDetail dutpCustomerChatDetail);

    List<DutpCustomerChatDetailVO> getMessage(DutpCustomerChatDetail dutpCustomerChatDetail);

    List<DutpCustomerChatDetailVO> getLastMessage(DutpCustomerChatDetail dutpCustomerChatDetail);

    void changeStatus(DutpCustomerChatDetail dutpCustomerChatDetail);

    Boolean changeReadStatus(DutpCustomerChatDetail dutpCustomerChatDetail);

    DutpCustomer checkCustomer(DutpCustomer dutpCustomer);

    void checkUserChat(DutpCustomerChatDetail detail);

    Boolean checkCustomerByUser(DutpCustomerChatDetail dutpCustomerChatDetail);
}
