package cn.dutp.im.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 聊天状态对象 dutp_customer_chat_status
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@TableName("dutp_customer_chat_status")
public class DutpCustomerChatStatus extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long statusId;

    /**
     * 客服ID
     */
    @Excel(name = "客服ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long customerId;

    /**
     * dutp_user中user_id
     */
    @Excel(name = "dutp_user中user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 1待开启服务2正在会话3会话关闭
     */
    @Excel(name = "1待开启服务2正在会话3会话关闭")
    private Integer chatStatus;

    /**
     * 0未删除2已删除
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("statusId", getStatusId())
                .append("customerId", getCustomerId())
                .append("userId", getUserId())
                .append("chatStatus", getChatStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
