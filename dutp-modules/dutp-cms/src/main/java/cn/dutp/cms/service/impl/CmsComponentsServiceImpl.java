package cn.dutp.cms.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.cms.mapper.CmsComponentsMapper;
import cn.dutp.cms.domain.CmsComponents;
import cn.dutp.cms.service.ICmsComponentsService;

/**
 * DUTP-CMS-004组件集合Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class CmsComponentsServiceImpl extends ServiceImpl<CmsComponentsMapper, CmsComponents> implements ICmsComponentsService
{
    @Autowired
    private CmsComponentsMapper cmsComponentsMapper;

    /**
     * 查询DUTP-CMS-004组件集合
     *
     * @param componentId DUTP-CMS-004组件集合主键
     * @return DUTP-CMS-004组件集合
     */
    @Override
    public CmsComponents selectCmsComponentsByComponentId(Long componentId)
    {
        return this.getById(componentId);
    }

    /**
     * 查询DUTP-CMS-004组件集合列表
     *
     * @param cmsComponents DUTP-CMS-004组件集合
     * @return DUTP-CMS-004组件集合
     */
    @Override
    public List<CmsComponents> selectCmsComponentsList(CmsComponents cmsComponents)
    {
        LambdaQueryWrapper<CmsComponents> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByAsc(CmsComponents::getGroupId);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-CMS-004组件集合
     *
     * @param cmsComponents DUTP-CMS-004组件集合
     * @return 结果
     */
    @Override
    public boolean insertCmsComponents(CmsComponents cmsComponents)
    {
        return this.save(cmsComponents);
    }

    /**
     * 修改DUTP-CMS-004组件集合
     *
     * @param cmsComponents DUTP-CMS-004组件集合
     * @return 结果
     */
    @Override
    public boolean updateCmsComponents(CmsComponents cmsComponents)
    {
        return this.updateById(cmsComponents);
    }

    /**
     * 批量删除DUTP-CMS-004组件集合
     *
     * @param componentIds 需要删除的DUTP-CMS-004组件集合主键
     * @return 结果
     */
    @Override
    public boolean deleteCmsComponentsByComponentIds(List<Long> componentIds)
    {
        return this.removeByIds(componentIds);
    }

}
