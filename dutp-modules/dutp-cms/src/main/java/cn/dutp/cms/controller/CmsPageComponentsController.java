package cn.dutp.cms.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.cms.domain.CmsComponents;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.cms.domain.CmsPageComponents;
import cn.dutp.cms.service.ICmsPageComponentsService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-CMS-0001cms系统组件Controller
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/pageComponent")
public class CmsPageComponentsController extends BaseController
{
    @Autowired
    private ICmsPageComponentsService cmsPageComponentsService;

    /**
     * 查询DUTP-CMS-0001cms系统组件列表
     */
    @GetMapping("/list")
    public AjaxResult list(CmsPageComponents cmsPageComponents)
    {
        List<CmsComponents> list = cmsPageComponentsService.selectCmsPageComponentsList(cmsPageComponents);
        return AjaxResult.success(list);
    }

    @PostMapping
    public AjaxResult add(@RequestBody CmsPageComponents cmsPageComponents)
    {
        return toAjax(cmsPageComponentsService.insertCmsPageComponents(cmsPageComponents));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody CmsPageComponents cmsPageComponents)
    {
        return toAjax(cmsPageComponentsService.updateCmsPageComponents(cmsPageComponents));
    }

    @DeleteMapping("/{pageIds}")
    public AjaxResult remove(@PathVariable Long[] pageIds)
    {
        return toAjax(cmsPageComponentsService.deleteCmsPageComponentsByPageIds(Arrays.asList(pageIds)));
    }
}
