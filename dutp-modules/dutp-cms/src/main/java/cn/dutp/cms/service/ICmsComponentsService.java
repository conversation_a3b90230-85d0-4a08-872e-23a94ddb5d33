package cn.dutp.cms.service;

import java.util.List;
import cn.dutp.cms.domain.CmsComponents;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * DUTP-CMS-004组件集合Service接口
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface ICmsComponentsService extends IService<CmsComponents>
{
    /**
     * 查询DUTP-CMS-004组件集合
     *
     * @param componentId DUTP-CMS-004组件集合主键
     * @return DUTP-CMS-004组件集合
     */
    public CmsComponents selectCmsComponentsByComponentId(Long componentId);

    /**
     * 查询DUTP-CMS-004组件集合列表
     *
     * @param cmsComponents DUTP-CMS-004组件集合
     * @return DUTP-CMS-004组件集合集合
     */
    public List<CmsComponents> selectCmsComponentsList(CmsComponents cmsComponents);

    /**
     * 新增DUTP-CMS-004组件集合
     *
     * @param cmsComponents DUTP-CMS-004组件集合
     * @return 结果
     */
    public boolean insertCmsComponents(CmsComponents cmsComponents);

    /**
     * 修改DUTP-CMS-004组件集合
     *
     * @param cmsComponents DUTP-CMS-004组件集合
     * @return 结果
     */
    public boolean updateCmsComponents(CmsComponents cmsComponents);

    /**
     * 批量删除DUTP-CMS-004组件集合
     *
     * @param componentIds 需要删除的DUTP-CMS-004组件集合主键集合
     * @return 结果
     */
    public boolean deleteCmsComponentsByComponentIds(List<Long> componentIds);

}
