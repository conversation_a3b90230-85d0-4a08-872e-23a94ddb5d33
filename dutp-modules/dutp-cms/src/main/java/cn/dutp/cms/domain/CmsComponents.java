package cn.dutp.cms.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-CMS-004组件集合对象 cms_components
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@TableName("cms_components")
public class CmsComponents extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.ASSIGN_ID)
    private Long componentId;

    private String componentKey;

    private String componentName;

    private String filePath;

    private String formPath;

    private String componentGroup;

    private String componentImage;

    private Integer groupId;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long pageComponentId;

    @TableField(exist = false)
    private String componentData;

    private String delFlag;
}