package cn.dutp.cms.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.cms.domain.CmsPage;
import cn.dutp.cms.service.ICmsPageService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 页面管理Controller
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/page")
public class CmsPageController extends BaseController
{
    @Autowired
    private ICmsPageService cmsPageService;

    /**
     * 查询页面管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CmsPage cmsPage)
    {
        startPage();
        List<CmsPage> list = cmsPageService.selectCmsPageList(cmsPage);
        return getDataTable(list);
    }

    /**
     * 获取页面管理详细信息
     */
    @RequiresPermissions("cms:page:query")
    @GetMapping(value = "/{pageId}")
    public AjaxResult getInfo(@PathVariable("pageId") Long pageId)
    {
        return success(cmsPageService.selectCmsPageByPageId(pageId));
    }

    /**
     * 新增页面管理
     */
    @RequiresPermissions("cms:page:add")
    @Log(title = "新增页面", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CmsPage cmsPage)
    {
        return toAjax(cmsPageService.insertCmsPage(cmsPage));
    }

    /**
     * 修改页面管理
     */
    @RequiresPermissions("cms:page:edit")
    @Log(title = "编辑页面", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CmsPage cmsPage)
    {
        return toAjax(cmsPageService.updateCmsPage(cmsPage));
    }

    /**
     * 删除页面管理
     */
    @RequiresPermissions("cms:page:remove")
    @Log(title = "删除页面", businessType = BusinessType.DELETE)
    @DeleteMapping("/{pageIds}")
    public AjaxResult remove(@PathVariable Long[] pageIds)
    {
        return toAjax(cmsPageService.deleteCmsPageByPageIds(Arrays.asList(pageIds)));
    }

    /**
     * 装修页面
     */
    @Log(title = "装修保存", businessType = BusinessType.UPDATE)
    @PostMapping("/saveDecoration")
    public AjaxResult saveDecoration(@RequestBody CmsPage cmsPage)
    {
        return cmsPageService.saveDecoration(cmsPage);
    }
}
