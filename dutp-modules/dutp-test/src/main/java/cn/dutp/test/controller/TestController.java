package cn.dutp.test.controller;


import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.mail.utils.MailUtils;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.hutool.core.util.RandomUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
public class TestController {

    @GetMapping("/mail")
    public AjaxResult mailTest()
    {
      String from = MailUtils.sendText("<EMAIL>", "测试", "test", null);
      return AjaxResult.success(from);
    }

    @GetMapping("/sendAliyun")
    public R<Object> sendAliyun() {
       String code = RandomUtil.randomNumbers(4);
       // 发送验证码
       AliyunSmsUtil.sendSMSCode("13478612990","code",code,"SMS_314725755", 5);
//       Integer checkCode = AliyunSmsUtil.checkCode("13478612990", code, "SMS_205165032");
       return R.ok(code);
    }

    public static void main(String[] args) {
        int j=10;
        for (int k = 0; k < j; k++) {
            System.out.println(k);
        }
    }

}
