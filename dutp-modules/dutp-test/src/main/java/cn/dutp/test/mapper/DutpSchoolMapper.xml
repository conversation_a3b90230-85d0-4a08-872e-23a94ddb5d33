<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.test.mapper.DutpSchoolMapper">
    
    <resultMap type="DutpSchool" id="DutpSchoolResult">
        <result property="schoolId"    column="school_id"    />
        <result property="schoolName"    column="school_name"    />
        <result property="schoolCode"    column="school_code"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpSchoolVo">
        select school_id, school_name, school_code, del_flag, create_by, create_time, update_by, update_time from dutp_school
    </sql>

    <select id="selectDutpSchoolList" parameterType="DutpSchool" resultMap="DutpSchoolResult">
        <include refid="selectDutpSchoolVo"/>
        <where>  
            <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
            <if test="schoolCode != null  and schoolCode != ''"> and school_code = #{schoolCode}</if>
        </where>
    </select>
    
    <select id="selectDutpSchoolBySchoolId" parameterType="Long" resultMap="DutpSchoolResult">
        <include refid="selectDutpSchoolVo"/>
        where school_id = #{schoolId}
    </select>

    <insert id="insertDutpSchool" parameterType="DutpSchool" useGeneratedKeys="true" keyProperty="schoolId">
        insert into dutp_school
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="schoolName != null">school_name,</if>
            <if test="schoolCode != null">school_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="schoolName != null">#{schoolName},</if>
            <if test="schoolCode != null">#{schoolCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpSchool" parameterType="DutpSchool">
        update dutp_school
        <trim prefix="SET" suffixOverrides=",">
            <if test="schoolName != null">school_name = #{schoolName},</if>
            <if test="schoolCode != null">school_code = #{schoolCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where school_id = #{schoolId}
    </update>

    <delete id="deleteDutpSchoolBySchoolId" parameterType="Long">
        delete from dutp_school where school_id = #{schoolId}
    </delete>

    <delete id="deleteDutpSchoolBySchoolIds" parameterType="String">
        delete from dutp_school where school_id in 
        <foreach item="schoolId" collection="array" open="(" separator="," close=")">
            #{schoolId}
        </foreach>
    </delete>
</mapper>