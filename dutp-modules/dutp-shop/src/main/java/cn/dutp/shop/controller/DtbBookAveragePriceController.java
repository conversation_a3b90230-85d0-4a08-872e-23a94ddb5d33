package cn.dutp.shop.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.shop.domain.dto.DtbBookPriceOrderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.shop.domain.DtbBookAveragePrice;
import cn.dutp.shop.service.IDtbBookAveragePriceService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 核定计算Controller
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/price")
public class DtbBookAveragePriceController extends BaseController
{
    @Autowired
    private IDtbBookAveragePriceService dtbBookAveragePriceService;

    /**
     * 查询核定计算列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookAveragePrice dtbBookAveragePrice)
    {
        startPage();
        List<DtbBookAveragePrice> list = dtbBookAveragePriceService.selectDtbBookAveragePriceList(dtbBookAveragePrice);
        return getDataTable(list);
    }

    /**
     * 导出核定计算列表
     */
    @RequiresPermissions("shop:price:export")
    @Log(title = "导出核定计算", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DtbBookAveragePrice dtbBookAveragePrice)
    {
        List<DtbBookAveragePrice> list = dtbBookAveragePriceService.selectDtbBookAveragePriceList(dtbBookAveragePrice);
        return AjaxResult.success(list);
    }

    /**
     * 获取核定计算详细信息
     */
    @GetMapping(value = "/{priceId}")
    public AjaxResult getInfo(@PathVariable("priceId") Long priceId)
    {
        return success(dtbBookAveragePriceService.selectDtbBookAveragePriceByPriceId(priceId));
    }

    /**
     * 新增核定计算
     */
    @Log(title = "新增核定计算", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookAveragePrice dtbBookAveragePrice)
    {
        return toAjax(dtbBookAveragePriceService.insertDtbBookAveragePrice(dtbBookAveragePrice));
    }

    /**
     * 修改核定计算
     */
    @Log(title = "修改核定计算", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookAveragePrice dtbBookAveragePrice)
    {
        return toAjax(dtbBookAveragePriceService.updateDtbBookAveragePrice(dtbBookAveragePrice));
    }

    /**
     * 删除核定计算
     */
    @RequiresPermissions("shop:price:remove")
    @Log(title = "删除核定计算", businessType = BusinessType.DELETE)
    @DeleteMapping("/{priceIds}")
    public AjaxResult remove(@PathVariable Long[] priceIds)
    {
        return toAjax(dtbBookAveragePriceService.deleteDtbBookAveragePriceByPriceIds(Arrays.asList(priceIds)));
    }

    /**
     * 修改核定计算
     */
    @Log(title = "核定计算", businessType = BusinessType.UPDATE)
    @PutMapping("/calc")
    public AjaxResult calc(@RequestBody DtbBookAveragePrice dtbBookAveragePrice)
    {
        return dtbBookAveragePriceService.calcDtbBookAveragePrice(dtbBookAveragePrice);
    }

    @GetMapping(value = "/orderList")
    public TableDataInfo orderList(DtbBookAveragePrice dtbBookAveragePrice)
    {
        startPage();
        List<DtbBookPriceOrderVo> list = dtbBookAveragePriceService.selectDtbBookAveragePriceOrderList(dtbBookAveragePrice);
        return getDataTable(list);
    }

    @GetMapping(value = "/orderData")
    public AjaxResult orderData(DtbBookAveragePrice dtbBookAveragePrice)
    {
        DtbBookPriceOrderVo data = dtbBookAveragePriceService.selectDtbBookAveragePriceOrderData(dtbBookAveragePrice);
        return AjaxResult.success(data);
    }
}
