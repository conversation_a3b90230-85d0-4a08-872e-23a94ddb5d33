package cn.dutp.shop.service;

import cn.dutp.domain.DtbBookOrderAuditUser;
import cn.dutp.shop.domain.vo.DtbBookOrderAuditUserVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 订单审核人Service接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IDtbBookOrderAuditUserService extends IService<DtbBookOrderAuditUser>
{
    /**
     * 查询订单审核人
     *
     * @param auditUserId 订单审核人主键
     * @return 订单审核人
     */
    public DtbBookOrderAuditUser selectDtbBookOrderAuditUserByAuditUserId(Long auditUserId);

    /**
     * 查询订单审核人列表
     *
     * @param dtbBookOrderAuditUser 订单审核人
     * @return 订单审核人集合
     */
    public List<DtbBookOrderAuditUser> selectDtbBookOrderAuditUserList(DtbBookOrderAuditUser dtbBookOrderAuditUser);

    /**
     * 新增订单审核人
     *
     * @param dtbBookOrderAuditUser 订单审核人
     * @return 结果
     */
    public boolean insertDtbBookOrderAuditUser(DtbBookOrderAuditUser dtbBookOrderAuditUser);

    /**
     * 修改订单审核人
     *
     * @param dtbBookOrderAuditUser 订单审核人
     * @return 结果
     */
    public boolean updateDtbBookOrderAuditUser(DtbBookOrderAuditUser dtbBookOrderAuditUser);

    /**
     * 批量删除订单审核人
     *
     * @param auditUserIds 需要删除的订单审核人主键集合
     * @return 结果
     */
    public boolean deleteDtbBookOrderAuditUserByAuditUserIds(List<Long> auditUserIds);

    /**
     * 根据订单id 查询样书一级审核信息
     *
     * @param orderId 订单id
     * @return 一级审核信息
     */
    DtbBookOrderAuditUserVo selectDtbBookOrderAuditUserByOrderId(Long orderId);

}
