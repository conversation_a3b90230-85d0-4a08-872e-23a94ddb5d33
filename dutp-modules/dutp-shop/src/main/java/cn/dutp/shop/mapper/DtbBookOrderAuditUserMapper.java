package cn.dutp.shop.mapper;

import cn.dutp.domain.DtbBookOrderAuditUser;
import cn.dutp.shop.domain.dto.OrderReviewDto;
import cn.dutp.shop.domain.vo.DtbBookOrderAuditUserVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单审核人Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Repository
public interface DtbBookOrderAuditUserMapper extends BaseMapper<DtbBookOrderAuditUser>
{

    /**
     * 订单所有审批内容
     */
    List<DtbBookOrderAuditUser> getByOrderId(Long orderId);

    /**
     * 采购审核
     * 根据审批状态，驳回： 更新订单审核人表，审核状态改为 2驳回，并添加审核备注
     * 通过： 更新订单审核人表，审核状态改为 1通过
     *
     * @param orderReviewDto 数据
     * @return 结果
     */
    boolean purchaseApprovalRejectAuditUser(OrderReviewDto orderReviewDto);

    /**
     * 根据订单id 查询样书一级审核信息
     *
     * @param orderId 订单id
     * @return 一级审核信息
     */
    DtbBookOrderAuditUserVo selectDtbBookOrderAuditUserByOrderId(Long orderId);
}
