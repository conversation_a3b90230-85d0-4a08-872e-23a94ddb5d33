package cn.dutp.shop.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.domain.DtbBook;
import cn.dutp.shop.service.IDtbBookService;
import cn.hutool.core.lang.tree.Tree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_002数字教材Controller
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@RestController
@RequestMapping("/book")
public class DtbBookController extends BaseController
{
    @Autowired
    private IDtbBookService dtbBookService;

    /**
     * 查询DUTP-DTB_002数字教材列表
     */
//    @RequiresPermissions("shop:book:list")
    @GetMapping("/list")
    public TableDataInfo list(DtbBook dtbBook)
    {
        startPage();
        List<DtbBook> list = dtbBookService.selectDtbBookList(dtbBook);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_002数字教材列表
     */
//    @RequiresPermissions("shop:book:export")
    @Log(title = "DUTP-DTB_002数字教材", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBook dtbBook)
    {
        List<DtbBook> list = dtbBookService.selectDtbBookList(dtbBook);
        ExcelUtil<DtbBook> util = new ExcelUtil<DtbBook>(DtbBook.class);
        util.exportExcel(response, list, "DUTP-DTB_002数字教材数据");
    }

    /**
     * 获取DUTP-DTB_002数字教材详细信息
     */
//    @RequiresPermissions("shop:book:query")
    @GetMapping(value = "/{bookId}")
    public AjaxResult getInfo(@PathVariable("bookId") Long bookId)
    {
        return success(dtbBookService.selectDtbBookByBookId(bookId));
    }

    /**
     * 新增DUTP-DTB_002数字教材
     */
//    @RequiresPermissions("shop:book:add")
    @Log(title = "DUTP-DTB_002数字教材", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBook dtbBook)
    {
        return toAjax(dtbBookService.insertDtbBook(dtbBook));
    }

    /**
     * 修改DUTP-DTB_002数字教材
     */
//    @RequiresPermissions("shop:book:edit")
    @Log(title = "DUTP-DTB_002数字教材", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBook dtbBook)
    {
        return toAjax(dtbBookService.updateDtbBook(dtbBook));
    }

    /**
     * 删除DUTP-DTB_002数字教材
     */
//    @RequiresPermissions("shop:book:remove")
    @Log(title = "DUTP-DTB_002数字教材", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bookIds}")
    public AjaxResult remove(@PathVariable Long[] bookIds)
    {
        return toAjax(dtbBookService.deleteDtbBookByBookIds(Arrays.asList(bookIds)));
    }


    /**
     * 查询DUTP-DTB_002电商中心数字教材列表（教材管理）
     */
//    @RequiresPermissions("shop:book:list")
    @GetMapping("/bookShopList")
    public TableDataInfo bookShopList(DtbBook dtbBook)
    {
        startPage();
        return getDataTable(dtbBookService.bookShopList(dtbBook));
    }

    /**
     * 查询DUTP-DTB_002电商中心数字教材列表（试用中心）
     */
//    @RequiresPermissions("shop:book:list")
    @GetMapping("/bookShopListCount")
    public Integer bookShopListCount(DtbBook dtbBook){
        return dtbBookService.bookShopListCount(dtbBook);
    }

    /**
     * 获取DUTP-DTB_002数字教材详细信息
     */
    @GetMapping(value = "/getInfoByBookId/{bookId}")
    public AjaxResult getInfoByBookId(@PathVariable("bookId") Long bookId)
    {
        return success(dtbBookService.selectDtbBookByBookId(bookId));
    }
}
