package cn.dutp.shop.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * DUTP-DTB-035开票申请对象 dtb_user_invoice_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("dtb_user_invoice_apply")
public class DtbUserInvoiceApply extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long titleId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 结算单id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long statementId;

    /**
     * 申请状态1申请2已开票3作废
     */
    @Excel(name = "申请状态1申请2已开票3作废")
    private Integer applyStatus;

    /**
     * 1商品明细2商品类别
     */
    @Excel(name = "1商品明细2商品类别")
    private Integer applyType;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开票时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;

    /**
     * 处理人id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dealUserId;

    /**
     * 审核时间
     */
    private Date dealTime;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 换开次数
     */
    private Integer changeCount;

    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 书商id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long merchantId;

    /**
     * 发票编码
     */
    @TableField(exist = false)
    private String invoiceCode;

    /**
     * 文件地址
     */
    @TableField(exist = false)
    private String fileUrl;

    /**
     * 文件名称
     */
    @TableField(exist = false)
    private String fileName;

    /**
     * 用户id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 用户id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    @TableField(exist = false)
    private String orderNo;

    @TableField(exist = false)
    private String statementNo;

    @TableField(exist = false)
    private String schoolTitleName;

    @TableField(exist = false)
    private String merchantTitleName;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("applyId", getApplyId())
                .append("titleId", getTitleId())
                .append("orderId", getOrderId())
                .append("applyStatus", getApplyStatus())
                .append("applyType", getApplyType())
                .append("uploadTime", getUploadTime())
                .append("invoiceAmount", getInvoiceAmount())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
