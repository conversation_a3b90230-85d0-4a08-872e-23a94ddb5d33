package cn.dutp.shop.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 核定计算对象 dtb_book_average_price
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@TableName("dtb_book_average_price")
public class DtbBookAveragePrice extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long priceId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    private String orderType;

    @Excel(name = "核定单价")
    private BigDecimal avgPrice;

    @Excel(name = "定价")
    private BigDecimal priceSale;

    @Excel(name = "教材名称")
    @TableField(exist = false)
    private String bookName;

    @Excel(name = "ISBN")
    @TableField(exist = false)
    private String isbn;

    @Excel(name = "ISSN")
    @TableField(exist = false)
    private String issn;
    // 未经修改的价格
    @TableField(exist = false)
    private BigDecimal orderPrice;

    private Integer beEdited;

}
