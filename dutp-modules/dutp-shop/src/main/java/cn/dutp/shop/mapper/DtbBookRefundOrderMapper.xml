<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbBookRefundOrderMapper">
    
    <resultMap type="DtbBookRefundOrder" id="DtbBookRefundOrderResult">
        <result property="refundOrderId"    column="refund_order_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundStatus"    column="refund_status"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="schoolId"    column="school_id"    />
        <result property="schoolName"    column="school_name"    />
        <result property="remark"    column="remark"    />
        <result property="refundOrderNo"    column="refund_order_no"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="auditUserId"    column="audit_user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="applyUserId"    column="apply_user_id"    />
        <result property="applyResource"    column="apply_resource"    />
        <result property="orderType"    column="order_type"    />


    </resultMap>

    <resultMap id="BookRefundOrderVOResult" type="BookRefundOrderVO" extends="DtbBookRefundOrderResult">
        <result property="payTime"    column="pay_time"    />
        <result property="orderTime"    column="order_create_time"    />
        <result property="merchantName"    column="merchant_name"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="orderNo" column="order_no"/>
        <collection property="refundOrderItems" javaType="java.util.List" ofType="RefundOrderItemVO">
            <result property="bookName" column="book_name"/>
            <result property="isbn" column="isbn"/>
            <result property="refundQuantity" column="refund_quantity"/>
            <result property="refundAmount" column="item_refund_amount"/>
            <result property="createBy" column="item_create_by"/>
            <result property="createTime" column="item_create_time"/>
            <result property="updateBy" column="item_update_by"/>
            <result property="updateTime" column="item_update_time"/>

        </collection>
    </resultMap>

    <sql id="selectDtbBookRefundOrderVo">
        select refund_order_id, order_id, refund_amount, refund_status, audit_time, school_id, remark, refund_order_no, audit_remark, audit_user_id, create_by, create_time, update_by, update_time from dtb_book_refund_order
    </sql>

    <sql id="selectRefundOrderVO">
        SELECT
            r.refund_order_id,
            r.order_id,
            r.refund_amount,
            r.refund_status,
            r.audit_time,
            r.school_id,
            r.remark,
            r.refund_order_no,
            r.audit_remark,
            r.audit_user_id,
            r.create_by,
            r.create_time,
            r.update_by,
            r.update_time,
            r.apply_user_id,
            r.apply_resource,
            i.refund_item_id,
            i.order_item_id,
            i.book_id,
            i.refund_quantity,
            i.refund_amount as item_refund_amount,
            i.create_by as item_create_by,
            i.create_time as item_create_time,
            i.update_by as item_update_by,
            i.update_time as item_update_time,
            db.book_name as book_name,
            ifnull(db.isbn,db.issn) as isbn,
            sch.school_name,
            o.order_type,
            o.order_no,
            o.pay_time,
            o.pay_amount as pay_amount,
            o.create_time as order_create_time,
            m.merchan_name as merchant_name
        FROM dtb_book_refund_order r
        LEFT JOIN dtb_book_refund_order_item i ON r.refund_order_id = i.refund_order_id
        LEFT JOIN dtb_book db ON i.book_id = db.book_id
        LEFT JOIN dutp_school sch ON r.school_id = sch.school_id
        LEFT JOIN dtb_book_order o ON r.order_id = o.order_id
        LEFT JOIN dtb_book_merchant m ON o.merchant_id = m.merchant_id
    </sql>

    <select id="selectDtbBookRefundOrderList" parameterType="DtbBookRefundOrder" resultMap="DtbBookRefundOrderResult">
        <include refid="selectDtbBookRefundOrderVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="refundAmount != null "> and refund_amount = #{refundAmount}</if>
            <if test="refundStatus != null "> and refund_status = #{refundStatus}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="schoolId != null "> and school_id = #{schoolId}</if>
            <if test="refundOrderNo != null  and refundOrderNo != ''"> and refund_order_no = #{refundOrderNo}</if>
            <if test="auditRemark != null  and auditRemark != ''"> and audit_remark = #{auditRemark}</if>
            <if test="auditUserId != null "> and audit_user_id = #{auditUserId}</if>
        </where>
    </select>
    
    <select id="selectDtbBookRefundOrderByRefundOrderId" parameterType="Long" resultMap="DtbBookRefundOrderResult">
        <include refid="selectDtbBookRefundOrderVo"/>
        where refund_order_id = #{refundOrderId}
    </select>

    <insert id="insertDtbBookRefundOrder" parameterType="DtbBookRefundOrder" useGeneratedKeys="true" keyProperty="refundOrderId">
        insert into dtb_book_refund_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundStatus != null">refund_status,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="remark != null">remark,</if>
            <if test="refundOrderNo != null">refund_order_no,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundStatus != null">#{refundStatus},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="refundOrderNo != null">#{refundOrderNo},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbBookRefundOrder" parameterType="DtbBookRefundOrder">
        update dtb_book_refund_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundStatus != null">refund_status = #{refundStatus},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="refundOrderNo != null">refund_order_no = #{refundOrderNo},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where refund_order_id = #{refundOrderId}
    </update>

    <delete id="deleteDtbBookRefundOrderByRefundOrderId" parameterType="Long">
        delete from dtb_book_refund_order where refund_order_id = #{refundOrderId}
    </delete>

    <delete id="deleteDtbBookRefundOrderByRefundOrderIds" parameterType="String">
        delete from dtb_book_refund_order where refund_order_id in 
        <foreach item="refundOrderId" collection="array" open="(" separator="," close=")">
            #{refundOrderId}
        </foreach>
    </delete>

    <select id="selectRefundOrderVOList" parameterType="DtbBookRefundOrder" resultMap="BookRefundOrderVOResult">
        <include refid="selectRefundOrderVO"/>
        <where>
            <if test="orderId != null">and r.order_id = #{orderId}</if>
            <if test="refundStatus != null">and r.refund_status = #{refundStatus}</if>
            <if test="schoolId != null">and r.school_id = #{schoolId}</if>
            <if test="refundOrderNo != null and refundOrderNo != ''">and r.refund_order_no = #{refundOrderNo}</if>
            <if test="orderType != null and orderType!=0">and o.order_type = #{orderType}</if>
            <if test="orderType != null and orderType==0">and o.order_type != 1 </if>
            <if test="keyword != null and keyword != ''">
                and (db.book_name LIKE CONCAT('%', #{keyword}, '%') 
                    OR db.isbn LIKE CONCAT('%', #{keyword}, '%')
                    OR db.issn LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="orderNo != null">and o.order_no = #{orderNo}</if>
        </where>
        ORDER BY r.create_time DESC
    </select>

    <select id="selectRefundOrderVOByRefundOrderId" parameterType="Long" resultMap="BookRefundOrderVOResult">
        <include refid="selectRefundOrderVO"/>
        where r.refund_order_id = #{refundOrderId}
    </select>
</mapper>