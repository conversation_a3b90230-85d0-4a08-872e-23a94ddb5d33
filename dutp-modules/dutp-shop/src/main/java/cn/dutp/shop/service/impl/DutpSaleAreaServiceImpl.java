package cn.dutp.shop.service.impl;

import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpSaleArea;
import cn.dutp.domain.DutpSaleAreaMember;
import cn.dutp.shop.mapper.DutpSaleAreaMapper;
import cn.dutp.shop.mapper.DutpSaleAreaMemberMapper;
import cn.dutp.shop.service.IDutpSaleAreaMemberService;
import cn.dutp.shop.service.IDutpSaleAreaService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售大区Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Service
public class DutpSaleAreaServiceImpl extends ServiceImpl<DutpSaleAreaMapper, DutpSaleArea> implements IDutpSaleAreaService
{
    @Autowired
    private DutpSaleAreaMapper dutpSaleAreaMapper;

    @Autowired
    private IDutpSaleAreaMemberService memberService;

    @Autowired
    private DutpSaleAreaMemberMapper memberMapper;

    /**
     * 查询销售大区
     *
     * @param areaId 销售大区主键
     * @return 销售大区
     */
    @Override
    public DutpSaleArea selectDutpSaleAreaByAreaId(Long areaId)
    {
        return dutpSaleAreaMapper.getByAreaId(areaId);
    }

    /**
     * 查询销售大区列表
     *
     * @param dutpSaleArea 销售大区
     * @return 销售大区
     */
    @Override
    public List<DutpSaleArea> selectDutpSaleAreaList(DutpSaleArea dutpSaleArea)
    {
        LambdaQueryWrapper<DutpSaleArea> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpSaleArea.getAreaName())) {
                lambdaQueryWrapper.like(DutpSaleArea::getAreaName
                ,dutpSaleArea.getAreaName());
                }
                if(ObjectUtil.isNotEmpty(dutpSaleArea.getManagerId())) {
                lambdaQueryWrapper.eq(DutpSaleArea::getManagerId
                ,dutpSaleArea.getManagerId());
            }

        return dutpSaleAreaMapper.selectAreaList(dutpSaleArea);
    }

    /**
     * 新增销售大区
     *
     * @param dutpSaleArea 销售大区
     * @return 结果
     */
    @Override
    public boolean insertDutpSaleArea(DutpSaleArea dutpSaleArea)
    {
        List<DutpSaleAreaMember> areaMemberList = dutpSaleArea.getDutpSaleAreaMemberList();
        boolean success = this.save(dutpSaleArea);
        if (success) {
            for (DutpSaleAreaMember dutpSaleAreaMember : areaMemberList) {
                dutpSaleAreaMember.setAreaId(dutpSaleArea.getAreaId());
            }

            memberService.saveBatch(areaMemberList);
        }
        return success;
    }

    /**
     * 修改销售大区
     *
     * @param dutpSaleArea 销售大区
     * @return 结果
     */
    @Override
    public boolean updateDutpSaleArea(DutpSaleArea dutpSaleArea)
    {
        boolean success = this.updateById(dutpSaleArea);
        if(success){
            Long areaId = dutpSaleArea.getAreaId();
            memberMapper.deleteByAreaId(areaId);
            List<DutpSaleAreaMember> areaMemberList = dutpSaleArea.getDutpSaleAreaMemberList();
            for (DutpSaleAreaMember dutpSaleAreaMember : areaMemberList) {
                dutpSaleAreaMember.setMemberId(null);
                dutpSaleAreaMember.setAreaId(areaId);
            }
            memberService.saveBatch(areaMemberList);

        }

        return success;
    }

    /**
     * 批量删除销售大区
     *
     * @param areaIds 需要删除的销售大区主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpSaleAreaByAreaIds(List<Long> areaIds)
    {
        //删除地区的同时也要删除成员
        boolean success = this.removeByIds(areaIds);
        if(success){
            for (Long areaId : areaIds) {
                memberMapper.deleteByAreaId(areaId);
            }
        }
        return success;
    }

    @Override
    public List<DutpSaleArea> getSaleAreaAndMember(DutpSaleArea dutpSaleArea) {
        // 查询当前登陆人是否为大区成员
        QueryWrapper<DutpSaleAreaMember> memberQueryWrapper = new QueryWrapper<>();
        memberQueryWrapper.lambda().eq(DutpSaleAreaMember::getUserId, SecurityUtils.getUserId())
                .eq(DutpSaleAreaMember::getDelFlag,0);
        List<DutpSaleAreaMember> memberList = memberMapper.selectList(memberQueryWrapper);
        QueryWrapper<DutpSaleArea> areaQueryWrapper = new QueryWrapper<>();
        areaQueryWrapper.lambda().eq(DutpSaleArea::getDelFlag,0);
        if (StringUtils.isNotBlank(dutpSaleArea.getAreaName())) {
            areaQueryWrapper.lambda().like(DutpSaleArea::getAreaName,dutpSaleArea.getAreaName());
        }
        if (ObjectUtil.isNotEmpty(memberList)) {
            List<Long> ids = memberList.stream().map(DutpSaleAreaMember::getAreaId).collect(Collectors.toList());
            // 是大区成员
            areaQueryWrapper.lambda().in(DutpSaleArea::getAreaId,ids);
        }
        return dutpSaleAreaMapper.selectList(areaQueryWrapper);
    }
}
