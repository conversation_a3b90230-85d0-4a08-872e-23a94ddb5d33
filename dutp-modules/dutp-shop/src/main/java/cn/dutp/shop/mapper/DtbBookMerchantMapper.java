package cn.dutp.shop.mapper;

import cn.dutp.shop.domain.DtbBookMerchant;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

 /* 书商Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Repository
public interface DtbBookMerchantMapper extends BaseMapper<DtbBookMerchant>
{

    @Select("select * from dtb_book_merchant where merchant_id = #{merchantId}")
    DtbBookMerchant getByMerchantId(Long merchantId);
}
