package cn.dutp.shop.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.shop.domain.DtbBookStatement;
import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.dto.ReconcileLookDto;
import cn.dutp.shop.domain.dto.ReconciliationDto;
import cn.dutp.shop.service.IDtbBookStatementService;
import cn.dutp.shop.service.IDtbUserInvoiceApplyService;
//import com.sun.org.apache.regexp.internal.RE;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 结算单Controller
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/statement")
public class DtbBookStatementController extends BaseController
{
    @Autowired
    private IDtbBookStatementService dtbBookStatementService;
    @Autowired
    private IDtbUserInvoiceApplyService dtbUserInvoiceApplyService;

    /**
     * 查询结算单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookStatement dtbBookStatement)
    {
        startPage();
        List<DtbBookStatement> list = dtbBookStatementService.selectDtbBookStatementList(dtbBookStatement);
        return getDataTable(list);
    }

    /**
     * 导出结算单列表
     */
    @RequiresPermissions("shop:statement:export")
    @Log(title = "导出结算单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookStatement dtbBookStatement)
    {
        dtbBookStatementService.exportStatement(response, dtbBookStatement);

    }

    /**
     * 导出对账单
     */
    @Log(title = "导出对账单", businessType = BusinessType.EXPORT)
    @PostMapping("/exportReconcile")
    public void exportReconcile(HttpServletResponse response, DtbBookStatement dtbBookStatement)
    {
        dtbBookStatementService.exportReconcile(response, dtbBookStatement);

    }

    /**
     * 获取结算单详细信息
     */
    @RequiresPermissions("shop:statement:query")
    @GetMapping(value = "/{statementId}")
    public AjaxResult getInfo(@PathVariable("statementId") Long statementId)
    {
        return success(dtbBookStatementService.selectDtbBookStatementByStatementId(statementId));
    }

    /**
     * 新增结算单
     */
    @RequiresPermissions("shop:statement:add")
    @Log(title = "新增结算单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookStatement dtbBookStatement)
    {
        return toAjax(dtbBookStatementService.insertDtbBookStatement(dtbBookStatement));
    }

    /**
     * 修改结算单
     */
    @RequiresPermissions("shop:statement:edit")
    @Log(title = "修改结算单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookStatement dtbBookStatement)
    {
        return toAjax(dtbBookStatementService.updateDtbBookStatement(dtbBookStatement));
    }

    /**
     * 删除结算单
     */
    @RequiresPermissions("shop:statement:remove")
    @Log(title = "删除结算单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{statementId}")
    public AjaxResult remove(@PathVariable Long statementId)
    {
        return toAjax(dtbBookStatementService.deleteDtbBookStatementByStatementIds(statementId));
    }

    /**
     * 确认结算单
     */
    @PutMapping("/changeStatementStatus/{statementId}")
    public AjaxResult changeStatementStatus(@PathVariable Long statementId)
    {
        return toAjax(dtbBookStatementService.changeStatementStatus(statementId));
    }

    /**
     * 查看该笔结算单的发票
     */
    @GetMapping(value = "/getStatementInvoice/{statementId}")
    public AjaxResult getStatementInvoice(@PathVariable("statementId") Long statementId)
    {
        return success(dtbUserInvoiceApplyService.getStatementInvoice(statementId));
    }

    /**
     * 对账列表
     */
    @GetMapping("/reconcileList")
    public TableDataInfo reconcileList(ReconciliationDto reconciliationDto)
    {
        startPage();
        List<ReconciliationDto> list = dtbBookStatementService.reconcileList(reconciliationDto);
        return getDataTable(list);
    }

    /**
     * 对账单查看
     */
    @GetMapping("/getReconcileListById/{orderId}")
    public TableDataInfo getReconcileListById(@PathVariable("orderId") Long orderId)
    {
        List<ReconcileLookDto> list = dtbBookStatementService.getReconcileListById(orderId);
        return getDataTable(list);
    }

    /**
     * 查询订单明细
     * @param orderId 结算单id
     * @return 结果
     */
    @GetMapping("/selectOrderItemByOrderId/{orderId}")
    public TableDataInfo selectOrderItemByOrderId(@PathVariable("orderId") Long orderId){
        return getDataTable(dtbBookStatementService.selectOrderItemByOrderId(orderId));
    }

    /**
     * 查询结算单明细（发票管理）
     * @param statementId 结算单id
     * @return 结果
     */
    @GetMapping("/selectStatementItem/{statementId}")
    public AjaxResult selectStatementItem(@PathVariable("statementId") Long statementId){
        return success(dtbBookStatementService.selectStatementItem(statementId));
    }

    /**
     * 查询订单明细（发票管理）
     * @param orderId 结算单id
     * @return 结果
     */
    @GetMapping("/selectOrderItem/{orderId}")
    public AjaxResult selectOrderItem(@PathVariable("orderId") Long orderId){
        return success(dtbBookStatementService.selectOrderItem(orderId));
    }

    /**
     *重新开票
     */
    @PutMapping("changeInvoice/{StatementId}")
    public AjaxResult changeInvoice(@PathVariable("StatementId") Long StatementId)
    {
        return toAjax(dtbBookStatementService.changeInvoice(StatementId));
    }
}
