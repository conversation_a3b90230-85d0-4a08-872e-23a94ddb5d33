package cn.dutp.shop.mapper;

import cn.dutp.book.domain.DtbUserBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB_014学生/教师书架Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Repository
public interface DtbUserBookMapper extends BaseMapper<DtbUserBook>
{
    List<DtbUserBook> selectByCodeList(@Param("purchaseCodeList") List<Long> purchaseCodeList);
}
