<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbBookOrderMapper">

    <resultMap type="DtbBookOrder" id="DtbBookOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderType"    column="order_type"    />
        <result property="bookId"    column="book_id"    />
        <result property="userId"    column="user_id"    />
        <result property="schoolUserId"    column="school_user_id"    />
        <result property="schoolId"    column="school_id"    />
        <result property="paymentStatus"    column="payment_status"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="invoiceStatus"    column="invoice_status"    />
        <result property="payTime"    column="pay_time"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payCertImageUrl"    column="pay_cert_image_url"    />
        <result property="orderAuditStatus"    column="order_audit_status"    />
        <result property="paymentAuditStatus"    column="payment_audit_status"    />
        <result property="price"    column="price"    />
        <result property="discount"    column="discount"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
    </resultMap>

    <resultMap type="cn.dutp.domain.vo.BookOrderVo" id="getDetailResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderType"    column="order_type"    />
        <result property="userId"    column="user_id"    />
        <result property="schoolUserId"    column="school_user_id"    />
        <result property="schoolId"    column="school_id"    />
        <result property="paymentStatus"    column="payment_status"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="invoiceStatus"    column="invoice_status"    />
        <result property="payTime"    column="pay_time"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="payCertImageUrl"    column="pay_cert_image_url"    />
        <result property="orderAuditStatus"    column="order_audit_status"    />
        <result property="paymentAuditStatus"    column="payment_audit_status"    />
        <result property="price"    column="price"    />
        <result property="discount"    column="discount"    />
        <collection property="bookList" ofType="cn.dutp.domain.DtbBook">
            <result property="bookId"    column="book_id"    />
            <result property="bookName"    column="book_name"    />
            <result property="isbn"    column="isbn"    />
            <result property="issn"    column="issn"    />
        </collection>
    </resultMap>


    <sql id="selectDtbBookOrderVo">
        select order_id, order_no, order_type, book_id, user_id, school_user_id, school_id, payment_status, payment_method, order_status, invoice_status, pay_time, pay_amount, pay_cert_image_url, order_audit_status, payment_audit_status, price, discount, remark, create_by, create_time, update_by, update_time, deleted from dtb_book_order
    </sql>

    <select id="selectDtbBookOrderList" parameterType="DtbBookOrder" resultMap="DtbBookOrderResult">
        <include refid="selectDtbBookOrderVo"/>
        <where>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="orderType != null "> and order_type = #{orderType}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="schoolUserId != null "> and school_user_id = #{schoolUserId}</if>
            <if test="schoolId != null "> and school_id = #{schoolId}</if>
            <if test="paymentStatus != null  and paymentStatus != ''"> and payment_status = #{paymentStatus}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="invoiceStatus != null "> and invoice_status = #{invoiceStatus}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="payAmount != null "> and pay_amount = #{payAmount}</if>
            <if test="payCertImageUrl != null  and payCertImageUrl != ''"> and pay_cert_image_url = #{payCertImageUrl}</if>
            <if test="orderAuditStatus != null "> and order_audit_status = #{orderAuditStatus}</if>
            <if test="paymentAuditStatus != null "> and payment_audit_status = #{paymentAuditStatus}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="discount != null "> and discount = #{discount}</if>
            <if test="deleted != null "> and deleted = #{deleted}</if>
        </where>
    </select>

    <select id="selectDtbBookOrderByOrderId" parameterType="Long" resultMap="DtbBookOrderResult">
        <include refid="selectDtbBookOrderVo"/>
        where order_id = #{orderId}
    </select>

    <insert id="insertDtbBookOrder" parameterType="DtbBookOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into dtb_book_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="orderType != null">order_type,</if>
            <if test="bookId != null">book_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="schoolUserId != null">school_user_id,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="orderStatus != null and orderStatus != ''">order_status,</if>
            <if test="invoiceStatus != null">invoice_status,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="payCertImageUrl != null">pay_cert_image_url,</if>
            <if test="orderAuditStatus != null">order_audit_status,</if>
            <if test="paymentAuditStatus != null">payment_audit_status,</if>
            <if test="price != null">price,</if>
            <if test="discount != null">discount,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="schoolUserId != null">#{schoolUserId},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="orderStatus != null and orderStatus != ''">#{orderStatus},</if>
            <if test="invoiceStatus != null">#{invoiceStatus},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="payCertImageUrl != null">#{payCertImageUrl},</if>
            <if test="orderAuditStatus != null">#{orderAuditStatus},</if>
            <if test="paymentAuditStatus != null">#{paymentAuditStatus},</if>
            <if test="price != null">#{price},</if>
            <if test="discount != null">#{discount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
         </trim>
    </insert>

    <update id="updateDtbBookOrder" parameterType="DtbBookOrder">
        update dtb_book_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="schoolUserId != null">school_user_id = #{schoolUserId},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="orderStatus != null and orderStatus != ''">order_status = #{orderStatus},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="payCertImageUrl != null">pay_cert_image_url = #{payCertImageUrl},</if>
            <if test="orderAuditStatus != null">order_audit_status = #{orderAuditStatus},</if>
            <if test="paymentAuditStatus != null">payment_audit_status = #{paymentAuditStatus},</if>
            <if test="price != null">price = #{price},</if>
            <if test="discount != null">discount = #{discount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteDtbBookOrderByOrderId" parameterType="Long">
        delete from dtb_book_order where order_id = #{orderId}
    </delete>

    <delete id="deleteDtbBookOrderByOrderIds" parameterType="String">
        delete from dtb_book_order where order_id in
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <select id="getOrderMerchantBookList" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        select
            dtb.*,
            (select default_discount from dtb_book_merchant where merchant_id = #{merchantId} and del_flag = 0)as discount,
        (select min_discount from dtb_book where book_id = dtb.book_id and del_flag = 0)as minDiscount
        from
            dtb_book dtb
        <where>

            dtb.del_flag = 0 and dtb.master_flag != 3 and dtb.shelf_state = 1 and dtb.book_organize = 1
            and
                (select count(0) from dtb_book_purchase_code where book_id = dtb.book_id
                and del_flag = 0 and state = 1 and code_type = 1
                ) > 0
            <if test="bookName != null and bookName != ''">
                and (
                    dtb.book_name like concat('%', #{bookName}, '%')
                    or dtb.isbn like concat('%', #{bookName}, '%')
                    or dtb.issn like concat('%', #{bookName}, '%')
                )
            </if>
            GROUP BY dtb.book_id

        </where>

    </select>

    <select id="getOrderSampleBookList" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        select
            dtb.*,
            sd.leader_id
        from
            dtb_book dtb
        left join
            sys_dept sd on sd.dept_id = dtb.dept_id
        <where>
            dtb.del_flag = 0 and dtb.master_flag != 3 and dtb.shelf_state = 1 and dtb.book_organize = 1
            and
            (select count(0) from dtb_book_purchase_code where book_id = dtb.book_id
            and del_flag = 0 and state = 1 and code_type = 1
            ) > 0
            <if test="bookName != null and bookName != ''">
                and (
                dtb.book_name like concat('%', #{bookName}, '%')
                or dtb.isbn like concat('%', #{bookName}, '%')
                or dtb.issn like concat('%', #{bookName}, '%')
                )
            </if>
            
            GROUP BY dtb.book_id
        </where>
    </select>

    <select id="getOrderSchoolBookList" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        select
            dtb.*,
            (select count(0) from dtb_book_purchase_code where book_id = dtb.book_id
               and del_flag = 0 and state = 1 and code_type = 1
            ) as stockCount,
            (select default_discount from dutp_school where school_id = #{schoolId} and del_flag = 0)as discount,
            (select min_discount from dtb_book where book_id = dtb.book_id and del_flag = 0)as minDiscount
        from
            dtb_book dtb
        <where>
            dtb.del_flag = 0 and dtb.master_flag != 3 and dtb.shelf_state = 1 and dtb.book_organize = 1 and
            (select count(0) from dtb_book_purchase_code where book_id = dtb.book_id
                and del_flag = 0 and state = 1 and code_type = 1
            ) > 0
            <if test="bookName != null and bookName != ''">
                and (
                    dtb.book_name like concat('%', #{bookName}, '%')
                    or dtb.isbn like concat('%', #{bookName}, '%')
                    or dtb.issn like concat('%', #{bookName}, '%')
                )
            </if>

            GROUP BY dtb.book_id
        </where>
    </select>

    <select id="getEducationalOrderList" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        select
            dbo.order_id,
            dbo.order_no,
            dbo.order_type,
            dbo.book_id,
            dbo.user_id,
            dbo.school_user_id,
            dbo.school_id,
            dbo.payment_status,
            dbo.payment_method,
            dbo.order_status,
            dbo.invoice_status,
            dbo.pay_time,
            dbo.pay_amount,
            dbo.pay_cert_image_url,
            dbo.order_audit_status,
            dbo.payment_audit_status,
            dbo.price,
            dbo.discount,
            dbo.create_time,
            du.nick_name,
            du.user_name,
            ds.school_name,
            db.book_name,
            db.isbn,
            db.issn,
            di.price_sale as itemPriceSale,
            di.book_quantity
        from
            dtb_book_order dbo
        left join
            dutp_user du on du.user_id = dbo.user_id and du.del_flag = 0
        left join
            dutp_school ds on ds.school_id = dbo.school_id and ds.del_flag = 0
        left join
            dtb_book db on db.book_id = dbo.book_id and db.del_flag = 0
        left join
            dtb_book_order_item di on di.order_id = dbo.order_id
        where dbo.deleted = 1 and dbo.order_type = 1
        <if test="orderNo != null">and dbo.order_no like concat('%', #{orderNo}, '%')</if>
        <if test="bookName != null and bookName != ''">
          and (
                db.book_name like concat('%', #{bookName}, '%')
                or db.isbn like concat('%', #{bookName}, '%')
                or db.issn like concat('%', #{bookName}, '%')
            )
        </if>
        <if test="schoolName != null and schoolName != ''">and ds.school_name like concat('%', #{schoolName}, '%')</if>
        <if test="orderStatus != null and orderStatus != ''">and dbo.order_status like concat('%', #{orderStatus}, '%')</if>
    </select>

    <select id="getRetailOrderList" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        select
            dbo.order_id,
            dbo.order_no,
            dbo.order_type,
            dbo.book_id,
            dbo.user_id,
            dbo.school_user_id,
            dbo.school_id,
            dbo.payment_status,
            dbo.payment_method,
            dbo.order_status,
            dbo.invoice_status,
            dbo.pay_time,
            dbo.pay_amount,
            dbo.pay_cert_image_url,
            dbo.order_audit_status,
            dbo.payment_audit_status,
            dbo.price,
            dbo.discount,
            dbo.create_time,
            du.nick_name,
            du.user_name,
            ds.school_name,
            db.book_name,
            db.isbn,
            db.issn,
            (select sum(book_quantity) from dtb_book_order_item where order_id = dbo.order_id) as bookQuantity
        from
            dtb_book_order dbo
        left join
            dutp_user du on du.user_id = dbo.user_id and du.del_flag = 0
        left join
            dutp_school ds on ds.school_id = dbo.school_id and ds.del_flag = 0
        left join
            dtb_book db on db.book_id = dbo.book_id and db.del_flag = 0
        <where>
            dbo.deleted = 1 and dbo.order_type = #{orderType}
            <if test="orderNo != null and orderNo != ''">and dbo.order_no like concat('%', #{orderNo}, '%')</if>
            <if test="bookName != null and bookName != ''">
                and (
                db.book_name like concat('%', #{bookName}, '%')
                or db.isbn like concat('%', #{bookName}, '%')
                or db.issn like concat('%', #{bookName}, '%')
                )
            </if>
            <if test="schoolId != null">
                and dbo.school_id = #{schoolId}
            </if>
            <if test="orderStatus != null and orderStatus != ''">and dbo.order_status = #{orderStatus}</if>
            <if test="exportList!= null and exportList.size()>0">
                and dbo.order_id in
                <foreach collection="exportList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by dbo.create_time desc
    </select>

    <select id="getBatchOrderList" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        select
            t.*
        from
            (
                select
                dbo.order_id,
                dbo.order_no,
                dbo.order_type,
                dbo.book_id,
                dbo.user_id,
                dbo.school_user_id,
                dbo.school_id,
                dbo.create_user_id,
                su.nick_name as createUserName,
                dbo.payment_status,
                dbo.payment_method,
                dbo.order_status,
                dbo.invoice_status,
                dbo.pay_time,
                dbo.pay_amount,
                dbo.pay_cert_image_url,
                dbo.order_audit_status,
                dbo.payment_audit_status,
                dbo.price,
                dbo.discount,
                dbo.area_id,
                dbo.merchant_id,
                dbo.operator_id,
                dbo.create_time,
                du.nick_name,
                du.user_name,
                dsa.area_name,
                ds.school_name,
                (case when (select count(0) from dtb_book_statement_order where order_id = dbo.order_id) > 0 then 1 else 0 end) as isStatementFlag,
                (
                select
                GROUP_CONCAT(d.book_name SEPARATOR ',')
                from
                dtb_book d
                where
                d.book_id in(
                select book_id from dtb_book_order_item where order_id = dbo.order_id
                )
                ) AS bookName,
                (
                select
                GROUP_CONCAT(CASE WHEN d.isbn IS NOT NULL and d.isbn!= '' THEN d.isbn else d.issn END)
                from
                dtb_book d
                where
                d.book_id in(
                select book_id from dtb_book_order_item where order_id = dbo.order_id
                )
                ) as isbn,
                dbm.merchan_name,
                su1.nick_name as operatorName,
                (select sum(book_quantity) from dtb_book_order_item where order_id = dbo.order_id) as bookQuantity
                from
                dtb_book_order dbo
                left join
                dutp_user du on du.user_id = dbo.user_id and du.del_flag = 0
                left join
                dutp_school ds on ds.school_id = dbo.school_id and ds.del_flag = 0
                left join
                dtb_book_merchant dbm on dbm.merchant_id = dbo.merchant_id and dbm.del_flag = 0
                left join
                sys_user su on su.user_id = dbo.create_user_id
                left join
                sys_user su1 on su1.user_id = dbo.operator_id
                left join
                    dutp_sale_area dsa on dsa.area_id = dbo.area_id
                <where>
                    dbo.deleted = 1 and dbo.order_type in (2, 3, 5)
                    <if test="orderNo != null">and dbo.order_no like concat('%', #{orderNo}, '%')</if>
                    <if test="schoolName != null and schoolName != ''">and ds.school_name like concat('%', #{schoolName}, '%')</if>
                    <if test="orderStatus != null">and dbo.order_status =  #{orderStatus}</if>
                    <if test="areaId != null">and dbo.area_id = #{areaId}</if>
                    <if test="merchantId != null">and dbo.merchant_id =  #{merchantId}</if>
                    <if test="operatorId != null">and dbo.operator_id =  #{operatorId}</if>
                    <if test="schoolId != null">and dbo.school_id =  #{schoolId}</if>
                    <if test="exportList!= null and exportList.size()>0">
                        and dbo.order_id in
                        <foreach collection="exportList" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </where>
                order by dbo.create_time desc
            ) t
        <where>
            <if test="bookName != null and bookName != ''">
                and (
                t.bookName like concat('%', #{bookName}, '%')
                or t.isbn like concat('%', #{bookName}, '%')
                )
            </if>
            <if test="orderType != null">and t.order_type = #{orderType}</if>
        </where>

    </select>

    <select id="getRetailOrderDetail" resultType="cn.dutp.domain.vo.BookOrderVo" parameterType="cn.dutp.shop.domain.dto.BookOrderDto">
        select
            dbo.order_id,
            dbo.order_no,
            dbo.order_type,
            dbo.book_id,
            dbo.user_id,
            dbo.school_user_id,
            dbo.school_id,
            dbo.payment_status,
            dbo.payment_method,
            dbo.order_status,
            dbo.invoice_status,
            dbo.pay_time,
            dbo.pay_amount,
            dbo.pay_cert_image_url,
            dbo.order_audit_status,
            dbo.payment_audit_status,
            dbo.price,
            dbo.discount,
            dbo.create_time,
            ds.school_name,
            ds1.school_name as collegesName,
            du.user_name,
            du.nick_name,
            du.email
        from
            dtb_book_order dbo
        left join
            dutp_school ds on ds.school_id = dbo.school_id and ds.del_flag = 0
        left join
            dutp_user du on du.user_id = dbo.user_id and du.del_flag = 0
        left join
            dutp_school ds1 on du.academy_id = ds1.school_id and ds1.del_flag = 0
        where
            dbo.deleted = 1 and dbo.order_id = #{orderId}
    </select>

    <select id="getEducationalDetail" resultType="cn.dutp.domain.vo.BookOrderVo" parameterType="cn.dutp.shop.domain.dto.BookOrderDto">
        select
            dbo.order_id,
            dbo.order_no,
            dbo.order_type,
            dbo.order_status,
            dbo.merchant_id,
            dbo.area_id,
            dbo.operator_id,
            dbo.book_id,
            dbo.user_id,
            dbo.school_user_id,
            dbo.school_id,
            dbo.payment_status,
            dbo.payment_method,
            dbo.order_status,
            dbo.invoice_status,
            dbo.pay_time,
            dbo.pay_amount,
            dbo.pay_cert_image_url,
            dbo.order_audit_status,
            dbo.payment_audit_status,
            dbo.price,
            dbo.discount,
            dbo.create_time,
            dbo.create_by,
            ds.school_name,
            ds1.school_name as collegesName,
            du.user_name,
            du.nick_name,
            du.email,
            dbm.merchan_name,
            da.area_name,
            ss.nick_name as operatorName
        from
            dtb_book_order dbo
        left join
            dutp_school ds on ds.school_id = dbo.school_id and ds.del_flag = 0
        left join
            dutp_user du on du.user_id = dbo.user_id and du.del_flag = 0
        left join
            dutp_school ds1 on du.academy_id = ds1.school_id and ds1.del_flag = 0
        left join
            dtb_book_merchant dbm on dbm.merchant_id = dbo.merchant_id
        left join
            dutp_sale_area da on da.area_id = dbo.area_id
        left join
            dutp_sale_area_member dam on dam.member_id = dbo.merchant_id
        left join
            sys_user ss on ss.user_id = dbo.operator_id
        where
            dbo.deleted = 1 and dbo.order_id = #{orderId}
    </select>

    <select id="getItemByOrderId" resultType="cn.dutp.domain.DtbBookOrderItem" parameterType="Long">
        select
            db.book_name,
            db.book_id,
            db.issn,
            db.isbn,
            di.discount,
            di.price_sale,
            db.master_flag,
            db.master_book_id,
            di.book_quantity,
            di.price_order_item,
            di.item_status,
            db.shelf_state,
            di.order_item_id,
            di.item_status,
            (
                case when (select
                               count(0)
                           from dtb_book_refund_order m
                            inner join dtb_book_refund_order_item n on n.refund_order_id = m.refund_order_id
                           where m.refund_status in(0,1) and n.order_item_id = di.order_item_id
                           ) > 0 then true else false end
            ) as isRefund,
            (select
                 count(oc.order_code_id)
             from dtb_book_order_code oc
             left join dtb_book_order_item dio on dio.order_item_id = oc.order_item_id
             where oc.order_id = #{orderId} and oc.state = 3 and oc.order_item_id = di.order_item_id) as activationCodeCount
        from
            dtb_book db
        inner join
            dtb_book_order_item di on di.book_id = db.book_id
        where
            di.order_id = #{orderId}
        union
        select
            db1.book_name,
            db1.book_id,
            db1.issn,
            db1.isbn,
            di1.discount,
            di1.price_sale,
            db1.master_flag,
            db1.master_book_id,
            di1.book_quantity,
            di1.price_order_item,
            di1.item_status,
            db1.shelf_state,
            di1.order_item_id,
            di1.item_status,
            (
                case when (select
                               count(0)
                           from dtb_book_refund_order m
                                    inner join dtb_book_refund_order_item n on n.refund_order_id = m.refund_order_id
                           where m.refund_status in(0,1) and n.order_item_id = di1.order_item_id
                          ) > 0 then true else false end
                ) as isRefund,
            0 as activationCodeCount
        from
            dtb_book db1
        left join
            dtb_book_order_item di1 on di1.book_id = db1.book_id
        where
            db1.publish_status = 2 and
            db1.master_book_id in (
                select
                    db.book_id
                from
                    dtb_book db
                        inner join
                    dtb_book_order_item di on di.book_id = db.book_id
                where
                    di.order_id = #{orderId}
                )
    </select>

    <select id="getChildItemByOrderId" resultType="cn.dutp.domain.DtbBookOrderItem" parameterType="Long">
        select
            db.book_name,
            db.book_id,
            db.issn,
            db.isbn,
            db.cover,
            db.master_book_id
        from
            dtb_book db
        where
            db.del_flag = 0 and
            db.master_book_id in
        <foreach collection="list" item="bookId" open="(" separator="," close=")">
            #{bookId}
        </foreach>
    </select>

    <select id="getRefundByOrderId"  parameterType="Long" resultType="cn.dutp.domain.DtbBookRefundOrderItem">
        select
            db.book_name,
            db.book_id,
            IF(db.isbn IS NULL OR db.isbn = '', db.issn, db.isbn) AS isbn,
            dro.audit_time,
            dro.audit_user_id,
            doi.refund_quantity,
            doi.refund_amount,
            dro.audit_time,
            dro.refund_status,
            dro.remark,
            su.nick_name as auditUserName
        from
            dtb_book_refund_order_item doi
        left join
            dtb_book_refund_order dro on dro.refund_order_id = doi.refund_order_id
        left join
            sys_user su on su.user_id = dro.audit_user_id
        left join
            dtb_book db on db.book_id = doi.book_id
        where
            dro.order_id = #{orderId}
    </select>
    <select id="getOrderInfo" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        SELECT
        dbo.order_id,
        dbo.order_no,
        dbo.order_type,
        dbo.book_id,
        dbo.user_id,
        dbo.school_user_id,
        dbo.create_user_id,
        dbo.school_id,
        dbo.payment_status,
        dbo.payment_method,
        dbo.order_status,
        dbo.current_audit_user_id,
        dbo.refund_status,
        dbo.invoice_status,
        dbo.pay_time,
        dbo.pay_amount,
        dbo.pay_cert_image_url,
        dbo.order_audit_status,
        dbo.payment_audit_status,
        dbo.price,
        dbo.discount,
        dbo.area_id,
        dbo.operator_id,
        dbo.create_time,
        dbo.deleted,
        dboi.order_item_id,
        dboi.price_sale,
        dboi.price_counter,
        dboi.item_status,
        dboi.price_order_item,
        dboi.book_quantity,
        dbo.remark,
        dboc.order_code_id,
        dboc.code_id,
        dboc.state,
        dboc.refund_state,
        dboc.export_quantity,
        dbpc.code_id,
        dbpc.phone,
        dbpc.`code`,
        dbpc.code_from,
        dbpc.state,
        dbpc.bind_date,
        dbpc.exchange_date,
        dbpc.time_limit,
        dbpc.expiry_date,
        dbpc.code_type,
        dbpc.is_frozen,
        db.book_name,
        db.author_label,
        db.author_value,
        db.cover,
        db.top_subject_id,
        db.second_subject_id,
        db.third_subject_id,
        db.forth_subject_id,
        db.isbn,
        db.issn,
        db.book_no,
        db.publish_date,
        db.current_version_id,
        db.last_version_id,
        db.shelf_time,
        db.unshelf_time,
        db.publish_organization,
        db.publish_status,
        db.shelf_state,
        db.house_id,
        db.book_type,
        db.master_flag,
        db.master_book_id,
        db.sold_quantity,
        db.read_quantity,
        db.price_counter,
        db.price_sale,
        db.min_discount,
        db.book_organize,
        db.topic_no,
        db.language_id,
        db.current_step_id,
        db.edition,
        db.table_number_type,
        db.image_number_type
        FROM
        dtb_book_order AS dbo
        LEFT JOIN dtb_book_order_item AS dboi ON dbo.order_id = dboi.order_id
        AND dbo.book_id = dboi.book_id
        LEFT JOIN dtb_book_order_code AS dboc ON dboi.order_item_id = dboc.order_item_id
        AND dboi.order_id = dboc.order_id
        LEFT JOIN dtb_book_purchase_code AS dbpc ON dboc.code_id = dbpc.code_id
        LEFT JOIN dtb_book AS db ON dbpc.book_id = db.book_id
        <where>
            dbo.order_type = 1 AND dbo.deleted = 1
            <if test="selParm != null and selParm != ''">
                AND (
                db.book_name LIKE concat('%', #{selParm}, '%')
                OR db.isbn LIKE concat('%', #{selParm}, '%')
                )
            </if>
            <if test="startDate != null and endDate != null">
                AND dbo.create_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="userId != null">AND dbo.user_id = #{userId}</if>
            <if test="orderNo != null">AND dbo.order_no = #{orderNo}</if>
            <if test="shelfState != null">AND db.shelf_state = #{shelfState}</if>
            <if test="orderStatus != null">AND dbo.order_status = #{orderStatus}</if>
            <if test="refundStatus != null">AND dbo.refund_status = #{refundStatus}</if>
            <if test="orderId != null">AND dbo.order_id = #{orderId}</if>
        </where>
    </select>
    <!--订单审核列表-->
    <select id="getOrderReview" parameterType="cn.dutp.shop.domain.dto.BookOrderDto"
            resultType="cn.dutp.domain.vo.BookOrderVo">
        SELECT dborder.order_id,
        dborder.order_no,
        audit.audit_type,
        audit.audit_user_id,
        dborder.create_time,
        audit.audit_status,
        audit.audit_content,
        dborder.price,
        dborder.pay_amount,
        dborder.remark,
        audit.audit_date,
        merchant.merchan_name,
        merchant.merchant_id,
        school.school_name,
        school.school_id,
        dborder.create_by,
        dborder.create_user_id,
        suser.nick_name,
        suser.user_id AS operatorId,
        dborder.order_type,
        dborder.remark,
        area.area_name
        FROM dtb_book_order_audit_user audit
        LEFT JOIN dtb_book_order dborder
        ON dborder.order_id = audit.order_id
        AND audit.user_id = #{userId}
        AND audit.audit_status IN (0,1,2)
        LEFT JOIN dutp_sale_area area
        ON dborder.area_id = area.area_id
        LEFT JOIN dtb_book_merchant merchant
        ON merchant.merchant_id = dborder.merchant_id
        LEFT JOIN sys_user suser
        ON dborder.operator_id = suser.user_id
        LEFT JOIN dutp_school school
        ON dborder.school_id = school.school_id
        WHERE
        dborder.order_type IN (2,3,5)
        and dborder.deleted ='1'
        <if test="orderNo != null and orderNo != ''">
            AND dborder.order_no = #{orderNo}
        </if>
        <if test="merchantId != null and merchantId != ''">
            AND dborder.merchant_id = #{merchantId}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND dborder.school_id = #{schoolId}
        </if>
        <if test="operatorId != null and operatorId != ''">
            AND dborder.operator_id = #{operatorId}
        </if>
        <if test="auditStatus != null">
            AND audit.audit_status = #{auditStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            AND dborder.order_type = #{orderType}
        </if>
        <if test="auditType != null and auditType != ''">
            AND audit.audit_type = #{auditType}
        </if>
        ORDER BY dborder.create_time DESC
    </select>
    <!--根据订单id，统计所有子订单的数量，计算出订单总的采购数量-->
    <select id="getOrderQuantityByOrderId" resultType="Integer">
        SELECT SUM(book_quantity) quantity
        FROM dtb_book_order_item
        WHERE order_id = #{orderId}
    </select>
    <!--订单审核   根据审批状态，驳回 更新订单表的订单状态为采购驳回：create_refused-->
    <!--通过：更新订单表的订单状态为结算中：settlement-->
    <update id="purchaseApprovalReject" parameterType="cn.dutp.shop.domain.dto.OrderReviewDto">
        UPDATE dtb_book_order
        <trim prefix="SET" suffixOverrides=",">
            update_by = #{updateBy},
            update_time = #{updateTime},
            <if test="functionType == 'reject' and auditType  == 1">
                order_status = 'create_refused',
            </if>
            <if test="functionType == 'reject' and auditType  == 2">
                order_status = 'edit_refused',
            </if>
            <if test="functionType == 'reject' and auditType  == 3">
                order_status = 'cancel_refused',
            </if>
            <if test="functionType == 'pass' ">
                order_status = 'settlement',
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="operatorId != '' and operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="merchantId != '' and merchantId != null">
                merchant_id = #{merchantId},
            </if>
            <if test="schoolId != '' and schoolId != null">
                school_id = #{schoolId},
            </if>

        </trim>
        WHERE order_id = #{orderId}
    </update>

    <select id="getSampleOrderList" parameterType="cn.dutp.shop.domain.dto.BookOrderDto" resultType="cn.dutp.domain.vo.BookOrderVo">
        select
            t.*
        from
            (
                select
                dbo.order_id,
                dbo.order_no,
                dbo.order_type,
                dbo.book_id,
                dbo.user_id,
                dbo.area_id,
                dbo.operator_id,
                dbo.school_user_id,
                dbo.school_id,
                dbo.create_user_id,
                su.nick_name as createUserName,
                dbo.payment_status,
                dbo.payment_method,
                dbo.order_status,
                dbo.invoice_status,
                dbo.pay_time,
                dbo.pay_amount,
                dbo.pay_cert_image_url,
                dbo.order_audit_status,
                dbo.payment_audit_status,
                dbo.price,
                dbo.discount,
                dbo.create_time,
                du.nick_name,
                du.user_name,
                ds.school_name,
                dsa.area_name,
                (case when (select count(0) from dtb_book_statement_order where order_id = dbo.order_id) > 0 then 1 else 0 end) as isStatementFlag,
                (
                select
                GROUP_CONCAT(d.book_name SEPARATOR ',')
                from
                dtb_book d
                where
                d.book_id in(
                select book_id from dtb_book_order_item where order_id = dbo.order_id
                )
                ) AS bookName,
                (
                select
                GROUP_CONCAT(CASE WHEN d.isbn IS NOT NULL and d.isbn!= '' THEN d.isbn else d.issn END)
                from
                dtb_book d
                where
                d.book_id in(
                select book_id from dtb_book_order_item where order_id = dbo.order_id
                )
                ) as isbn,
                su1.nick_name as operatorName,
                (select sum(book_quantity) from dtb_book_order_item where order_id = dbo.order_id) as bookQuantity
                from
                dtb_book_order dbo
                left join
                dutp_user du on du.user_id = dbo.user_id and du.del_flag = 0
                left join
                dutp_school ds on ds.school_id = dbo.school_id and ds.del_flag = 0
                left join
                sys_user su on su.user_id = dbo.create_user_id
                left join
                sys_user su1 on su1.user_id = dbo.operator_id
                left join
                dutp_sale_area dsa on dsa.area_id = dbo.area_id
                <where>
                    dbo.deleted = 1 and dbo.order_type = #{orderType}
                    <if test="orderSampleStatus != null">
                        <if test="orderSampleStatus == 1">and dbo.order_status = 'pending' or dbo.order_status = 'second_auditting'</if>
                        <if test="orderSampleStatus == 2">and dbo.order_status = 'first_refused' or dbo.order_status = 'second_refused'</if>
                        <if test="orderSampleStatus == 3">and dbo.order_status = 'completed'</if>
                        <if test="orderSampleStatus == 4">and dbo.order_status = 'cancelled'</if>
                    </if>
                    <if test="orderNo != null and orderNo != ''">
                        and dbo.order_no like concat('%', #{orderNo}, '%')
                    </if>
                    <if test="nickName != null and nickName != ''">
                        and du.nick_name like concat('%', #{nickName}, '%')
                    </if>
                    <if test="schoolId != null">
                        and dbo.school_id = #{schoolId}
                    </if>
                    <if test="areaId != null">
                        and dbo.area_id = #{areaId}
                    </if>
                    <if test="operatorId != null">
                        and dbo.operator_id = #{operatorId}
                    </if>
                    <if test="exportList!= null and exportList.size()>0">
                        and dbo.order_id in
                        <foreach collection="exportList" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </where>
            ) as t
        <where>
            <if test="bookName != null and bookName != ''">
                and (
                t.bookName like concat('%', #{bookName}, '%')
                or t.isbn like concat('%', #{bookName}, '%')
                )
            </if>

        </where>
        order by t.create_time desc
    </select>

    <!--  样书审核列表   -->
    <select id="getSampleBookOrderReview" parameterType="cn.dutp.shop.domain.dto.BookOrderDto"
            resultType="cn.dutp.domain.vo.BookOrderVo">
        SELECT dborder.order_id,
        dborder.order_no,
        audit.audit_type,
        audit.audit_user_id,
        dborder.create_time,
        audit.audit_status,
        audit.audit_content,
        audit.audit_date,
        audit.update_by,
        dborder.price,
        dborder.pay_amount,
        dborder.remark,
        du.user_id,
        du.user_name,
        du.nick_name,
        school.school_name,
        school.school_id,
        dborder.create_by,
        dborder.create_user_id,
        suser.nick_name AS operatorName,
        suser.user_id AS operatorId,
        dborder.order_type,
        dborder.remark,
        area.area_name
        FROM dtb_book_order_audit_user audit
        LEFT JOIN dtb_book_order dborder
        ON dborder.order_id = audit.order_id
        AND audit.user_id = #{userId}
        AND audit.audit_status IN (0,1,2)
        LEFT JOIN dutp_sale_area area
        ON dborder.area_id = area.area_id
        LEFT JOIN dutp_user du
        ON du.user_id = dborder.user_id
        LEFT JOIN sys_user suser
        ON dborder.operator_id = suser.user_id
        LEFT JOIN dutp_school school
        ON dborder.school_id = school.school_id
        WHERE
        dborder.order_type = 4
        AND dborder.deleted ='1'
        <if test="orderNo != null and orderNo != ''">
            AND dborder.order_no = #{orderNo}
        </if>
        <if test="nickName != null and nickName != ''">
            AND du.nick_name = #{nickName}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND dborder.school_id = #{schoolId}
        </if>
        <if test="operatorId != null and operatorId != ''">
            AND dborder.operator_id = #{operatorId}
        </if>
        <if test="auditStatus != null">
            AND audit.audit_status = #{auditStatus}
        </if>
        <if test="areaId != null and areaId != ''">
            AND dborder.area_id = #{areaId}
        </if>
        ORDER BY dborder.create_time DESC
    </select>

    <!--样书审核   根据审批状态 更新订单表的订单状态-->
    <!--通过：更新订单表的订单状态为已完成-->
    <update id="updateOrderBySampleBook" parameterType="cn.dutp.shop.domain.dto.OrderReviewDto">
        UPDATE dtb_book_order
        <trim prefix="SET" suffixOverrides=",">
            update_by = #{updateBy},
            update_time = #{updateTime},
            <if test="functionType == 'reject' and auditType  == 4">
                order_status = 'first_refused',
            </if>
            <if test="functionType == 'pass' and auditType  == 4">
                order_status = 'second_auditting',
            </if>
            <if test="functionType == 'reject' and auditType  == 5">
                order_status = 'second_refused',
            </if>
            <if test="functionType == 'pass' and auditType  == 5">
                order_status = 'completed',
            </if>
        </trim>
        WHERE order_id = #{orderId}
    </update>
    <update id="updateBookOrderCodeId">
        UPDATE dtb_book_order_code
        SET state = 3,
        update_time = now(),
        update_by = #{updateBy}
        WHERE code_id = #{codeId}
    </update>
</mapper>