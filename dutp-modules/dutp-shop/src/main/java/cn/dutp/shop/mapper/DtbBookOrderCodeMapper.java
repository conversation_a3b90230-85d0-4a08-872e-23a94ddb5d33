package cn.dutp.shop.mapper;

import cn.dutp.shop.domain.DtbBookOrderCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单下的购书码Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Repository
public interface DtbBookOrderCodeMapper extends BaseMapper<DtbBookOrderCode>
{
    /**
     * 根据订单id，查询此订单下所有绑定的购书码id的集合
     *
     * @param orderId 订单id
     * @return 购书码id的集合
     */
    @Select("select code_id from dtb_book_order_code where order_id = #{orderId}")
    List<Long> selectPurchaseCodeByOrderId(Long orderId);

    /**
     * 更新子订单的导出次数+1
     *
     * @param orderItemList 子订单集合
     * @return 操作结果
     */
    boolean updateExportQuantity(@Param("updateBy") String updateBy, @Param("orderItemList") List<Long> orderItemList);

    // 根据购书码id的集合，删除集合内冻结状态的订单下的购书码的数据
    boolean deleteByCodeId(@Param("codeIdList") List<Long> codeIdList);
}
