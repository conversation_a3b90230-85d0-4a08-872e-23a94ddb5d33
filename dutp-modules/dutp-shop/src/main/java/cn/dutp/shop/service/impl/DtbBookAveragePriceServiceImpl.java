package cn.dutp.shop.service.impl;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.shop.domain.DtbBookAveragePrice;
import cn.dutp.shop.domain.dto.DtbBookPriceOrderVo;
import cn.dutp.shop.mapper.DtbBookAveragePriceMapper;
import cn.dutp.shop.service.IDtbBookAveragePriceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 核定计算Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class DtbBookAveragePriceServiceImpl extends ServiceImpl<DtbBookAveragePriceMapper, DtbBookAveragePrice> implements IDtbBookAveragePriceService
{
    @Autowired
    private DtbBookAveragePriceMapper dtbBookAveragePriceMapper;

    /**
     * 查询核定计算
     *
     * @param priceId 核定计算主键
     * @return 核定计算
     */
    @Override
    public DtbBookAveragePrice selectDtbBookAveragePriceByPriceId(Long priceId)
    {
        return this.getById(priceId);
    }

    /**
     * 查询核定计算列表
     *
     * @param dtbBookAveragePrice 核定计算
     * @return 核定计算
     */
    @Override
    public List<DtbBookAveragePrice> selectDtbBookAveragePriceList(DtbBookAveragePrice dtbBookAveragePrice)
    {
        return dtbBookAveragePriceMapper.selectDtbBookAveragePriceList(dtbBookAveragePrice);
    }

    /**
     * 新增核定计算
     *
     * @param dtbBookAveragePrice 核定计算
     * @return 结果
     */
    @Override
    public boolean insertDtbBookAveragePrice(DtbBookAveragePrice dtbBookAveragePrice)
    {
        dtbBookAveragePrice.setBeEdited(1);
        return this.save(dtbBookAveragePrice);
    }

    /**
     * 修改核定计算
     *
     * @param dtbBookAveragePrice 核定计算
     * @return 结果
     */
    @Override
    public boolean updateDtbBookAveragePrice(DtbBookAveragePrice dtbBookAveragePrice)
    {
        dtbBookAveragePrice.setBeEdited(1);
        return this.updateById(dtbBookAveragePrice);
    }

    /**
     * 批量删除核定计算
     *
     * @param priceIds 需要删除的核定计算主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookAveragePriceByPriceIds(List<Long> priceIds)
    {
        return this.removeByIds(priceIds);
    }

    @Override
    public AjaxResult calcDtbBookAveragePrice(DtbBookAveragePrice dtbBookAveragePrice) {
        BigDecimal calcPrice = dtbBookAveragePriceMapper.calcDtbBookAveragePrice(dtbBookAveragePrice);
        if (dtbBookAveragePrice.getPriceId()!=null && dtbBookAveragePrice.getPriceId().longValue()!=0l) {
            DtbBookAveragePrice param = new DtbBookAveragePrice();
            param.setPriceId(dtbBookAveragePrice.getPriceId());
            param.setAvgPrice(calcPrice);
            param.setBeEdited(0);
            dtbBookAveragePriceMapper.updateById(param);
        }
        return AjaxResult.success(calcPrice);
    }

    @Override
    public List<DtbBookPriceOrderVo> selectDtbBookAveragePriceOrderList(DtbBookAveragePrice dtbBookAveragePrice) {
        return dtbBookAveragePriceMapper.selectDtbBookAveragePriceOrderList(dtbBookAveragePrice);
    }

    @Override
    public DtbBookPriceOrderVo selectDtbBookAveragePriceOrderData(DtbBookAveragePrice dtbBookAveragePrice) {
       DtbBookPriceOrderVo dtbBookPriceOrderVo = dtbBookAveragePriceMapper.selectDtbBookAveragePriceOrderData(dtbBookAveragePrice);
        return dtbBookPriceOrderVo;
    }

}
