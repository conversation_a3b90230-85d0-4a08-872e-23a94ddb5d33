package cn.dutp.shop.domain.dto;

import cn.dutp.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 批量订单导出对象
 *
 */
@Data
public class BookOrderBatchExport {

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderNo;

    /**
     * 教材名称
     */
    @Excel(name = "教材名称")
    private String bookName;

    /**
     * ISBN序列号
     */
    @Excel(name = "ISBN/ISSN")
    private String isbn;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单类型
     */
    @Excel(name = "订单类型")
    private String orderTypeName;

    /**
     * 学校名称
     */
    @Excel(name = "学校名称")
    private String schoolName;

    /**
     * 书籍数量
     */
    @Excel(name = "采购数量")
    private Long bookQuantity;

    /**
     * 支付金额
     */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /**
     * 地区名称
     */
    @Excel(name = "地区名称")
    private String areaName;

    /** 经办人 */
    @Excel(name = "经办人")
    private String operatorName;
}
