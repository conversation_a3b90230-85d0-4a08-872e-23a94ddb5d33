<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbBookOrderCodeMapper">
    
    <resultMap type="DtbBookOrderCode" id="DtbBookOrderCodeResult">
        <result property="orderCodeId"    column="order_code_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderItemId"    column="order_item_id"    />
        <result property="codeId"    column="code_id"    />
        <result property="state"    column="state"    />
        <result property="refundState"    column="refund_state"    />
        <result property="bookId"    column="book_id"    />
        <result property="schoolId"    column="school_id"    />
        <result property="exportQuantity"    column="export_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbBookOrderCodeVo">
        select order_code_id, order_id, order_item_id, code_id, state, refund_state, book_id, school_id, export_quantity, create_by, create_time, update_by, update_time from dtb_book_order_code
    </sql>

    <select id="selectDtbBookOrderCodeList" parameterType="DtbBookOrderCode" resultMap="DtbBookOrderCodeResult">
        <include refid="selectDtbBookOrderCodeVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderItemId != null "> and order_item_id = #{orderItemId}</if>
            <if test="codeId != null "> and code_id = #{codeId}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="refundState != null "> and refund_state = #{refundState}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="schoolId != null "> and school_id = #{schoolId}</if>
            <if test="exportQuantity != null "> and export_quantity = #{exportQuantity}</if>
        </where>
    </select>
    
    <select id="selectDtbBookOrderCodeByOrderCodeId" parameterType="Long" resultMap="DtbBookOrderCodeResult">
        <include refid="selectDtbBookOrderCodeVo"/>
        where order_code_id = #{orderCodeId}
    </select>

    <insert id="insertDtbBookOrderCode" parameterType="DtbBookOrderCode" useGeneratedKeys="true" keyProperty="orderCodeId">
        insert into dtb_book_order_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderItemId != null">order_item_id,</if>
            <if test="codeId != null">code_id,</if>
            <if test="state != null">state,</if>
            <if test="refundState != null">refund_state,</if>
            <if test="bookId != null">book_id,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="exportQuantity != null">export_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderItemId != null">#{orderItemId},</if>
            <if test="codeId != null">#{codeId},</if>
            <if test="state != null">#{state},</if>
            <if test="refundState != null">#{refundState},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="exportQuantity != null">#{exportQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbBookOrderCode" parameterType="DtbBookOrderCode">
        update dtb_book_order_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderItemId != null">order_item_id = #{orderItemId},</if>
            <if test="codeId != null">code_id = #{codeId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="refundState != null">refund_state = #{refundState},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="exportQuantity != null">export_quantity = #{exportQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_code_id = #{orderCodeId}
    </update>

    <delete id="deleteDtbBookOrderCodeByOrderCodeId" parameterType="Long">
        delete from dtb_book_order_code where order_code_id = #{orderCodeId}
    </delete>

    <delete id="deleteDtbBookOrderCodeByOrderCodeIds" parameterType="String">
        delete from dtb_book_order_code where order_code_id in 
        <foreach item="orderCodeId" collection="array" open="(" separator="," close=")">
            #{orderCodeId}
        </foreach>
    </delete>

    <!--更新子订单的导出次数+1-->
    <update id="updateExportQuantity">
        UPDATE dtb_book_order_code
        SET export_quantity = export_quantity + 1,
        update_time = now(),
        update_by = #{updateBy}
        WHERE order_item_id in
        <foreach item="orderCodeId" collection="orderItemList" open="(" separator="," close=")">
            #{orderCodeId}
        </foreach>
    </update>
    <!--根据购书码id的集合，删除集合内冻结状态的订单下的购书码的数据-->
    <delete id="deleteByCodeId" parameterType="Long">
        DELETE
        FROM dtb_book_order_code
        WHERE code_id IN
        (SELECT code_id
        FROM dtb_book_purchase_code
        WHERE code_id IN
        <foreach collection='codeIdList' item='codeId' open='(' separator=',' close=')'>
            #{codeId}
        </foreach>
        AND is_frozen = 1
        AND state != 5)
    </delete>
</mapper>