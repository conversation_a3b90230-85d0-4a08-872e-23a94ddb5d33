package cn.dutp.shop.domain.vo;

import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.shop.domain.DtbUserInvoiceFile;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DUTP-DTB-035开票申请对象 dtb_user_invoice_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class DtbUserInvoiceApplyVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long titleId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 结算单id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long statementId;

    /**
     * 申请状态1申请2已开票3作废
     */
    private Integer applyStatus;

    /**
     * 1商品明细2商品类别
     */
    private Integer applyType;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date uploadTime;

    /**
     * 处理人id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dealUserId;

    /**
     * 审核时间
     */
    private Date dealTime;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 换开次数
     */
    private Integer changeCount;

    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 发票类型1数电普票2数电专票
     */
    private Integer invoiceType;

    /**
     * 1个人2企业
     */
    private Integer titleType;

    /**
     * 抬头名称
     */
    private String titleName;

    /**
     * 税号
     */
    private String taxNo;

    /**
     * 注册地址
     */
    private String registAddress;

    /**
     * 注册手机号
     */
    private String registTel;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 银行账户
     */
    private String accountNo;

    /**
     * 用户ID dutp_user表中id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 发票文件
     */
    private List<DtbUserInvoiceFile> invoiceFileList;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;
}
