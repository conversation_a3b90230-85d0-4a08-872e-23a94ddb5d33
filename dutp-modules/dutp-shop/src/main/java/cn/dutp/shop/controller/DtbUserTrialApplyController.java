package cn.dutp.shop.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.shop.domain.DtbUserInvoiceTitle;
import cn.dutp.shop.domain.DtbUserTrialApply;
import cn.dutp.shop.service.IDtbUserTrialApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_025教师试用申请Controller
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@RestController
@RequestMapping("/trial")
public class DtbUserTrialApplyController extends BaseController {
    @Autowired
    private IDtbUserTrialApplyService dtbUserTrialApplyService;

    /**
     * 查询DUTP-DTB_025教师试用申请列表
     */
    @RequiresPermissions("shop:trial:list")
    @GetMapping("/list")
    public TableDataInfo list(DtbUserTrialApply dtbUserTrialApply) {
        startPage();
        List<DtbUserTrialApply> list = dtbUserTrialApplyService.selectDtbUserTrialApplyList(dtbUserTrialApply);
        return getDataTable(list);
    }

    /**
     * 学生教师端教师试用申请列表
     */
    @GetMapping("/listEducation")
    public AjaxResult listEducation(DtbUserTrialApply dtbUserTrialApply) {
        return success(dtbUserTrialApplyService.listEducation(dtbUserTrialApply));
    }

    /**
     * 导出DUTP-DTB_025教师试用申请列表
     */
    @RequiresPermissions("shop:apply:export")
    @Log(title = "DUTP-DTB_025教师试用申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserTrialApply dtbUserTrialApply) {
        List<DtbUserTrialApply> list = dtbUserTrialApplyService.selectDtbUserTrialApplyList(dtbUserTrialApply);
        ExcelUtil<DtbUserTrialApply> util = new ExcelUtil<DtbUserTrialApply>(DtbUserTrialApply.class);
        util.exportExcel(response, list, "DUTP-DTB_025教师试用申请数据");
    }

    /**
     * 获取DUTP-DTB_025教师试用申请详细信息
     */
    @RequiresPermissions("shop:trial:query")
    @GetMapping(value = "/{applyId}")
    public AjaxResult getInfo(@PathVariable("applyId") Long applyId) {
        return success(dtbUserTrialApplyService.selectDtbUserTrialApplyByApplyId(applyId));
    }

    /**
     * 新增DUTP-DTB_025教师试用申请
     */
    @RequiresPermissions("shop:apply:add")
    @Log(title = "DUTP-DTB_025教师试用申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserTrialApply dtbUserTrialApply) {
        return toAjax(dtbUserTrialApplyService.insertDtbUserTrialApply(dtbUserTrialApply));
    }

    /**
     * 修改DUTP-DTB_025教师试用申请
     */
    @RequiresPermissions("shop:trial:edit")
    @Log(title = "DUTP-DTB_025教师试用申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserTrialApply dtbUserTrialApply) {
        return toAjax(dtbUserTrialApplyService.updateDtbUserTrialApply(dtbUserTrialApply));
    }

    /**
     * 学生教师端修改教师试用申请
     */
    @Log(title = "DUTP-DTB_025教师试用申请", businessType = BusinessType.UPDATE)
    @PutMapping("/editEducation")
    public AjaxResult editEducation(@RequestBody DtbUserTrialApply dtbUserTrialApply) {
        return toAjax(dtbUserTrialApplyService.editEducation(dtbUserTrialApply));
    }

    /**
     * 删除DUTP-DTB_025教师试用申请
     */
    @RequiresPermissions("shop:apply:remove")
    @Log(title = "DUTP-DTB_025教师试用申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applyIds}")
    public AjaxResult remove(@PathVariable Long[] applyIds) {
        return toAjax(dtbUserTrialApplyService.deleteDtbUserTrialApplyByApplyIds(Arrays.asList(applyIds)));
    }

    /**
     * 查询DUTP-DTB_025教师试用申请列表（试用中心）
     */
    @RequiresPermissions("shop:trial:list")
    @GetMapping("/TrialShopList")
    public TableDataInfo TrialShopList(DtbUserTrialApply dtbUserTrialApply) {
        startPage();
        List<DtbUserTrialApply> list = dtbUserTrialApplyService.TrialShopList(dtbUserTrialApply);
        return getDataTable(list);
    }

    /**
     * 审核通过绑定购书码
     */
    @RequiresPermissions("shop:trial:approve")
    @GetMapping("/binding")
    public AjaxResult binding(DtbUserTrialApply dtbUserTrialApply) {
        return AjaxResult.success(dtbUserTrialApplyService.binding(dtbUserTrialApply));
    }

    /**
     * 获取临时码
     */
    @RequiresPermissions("shop:trial:approve")
    @GetMapping("/temporaryCode")
    public AjaxResult getTemporaryCode(DtbUserTrialApply dtbUserTrialApply) {
        return AjaxResult.success(dtbUserTrialApplyService.getTemporaryCode(dtbUserTrialApply));
    }



    /**
     * 学生教师端教师提交试用申请
     *
     * @param dtbUserTrialApply 教师试用申请
     * @return 教师试用申请结果
     */
    @GetMapping("/submitTrialApplicationEducation")
    public R<String> submitTrialApplicationEducation(DtbUserTrialApply dtbUserTrialApply) {
        return dtbUserTrialApplyService.submitTrialApplicationEducation(dtbUserTrialApply);
    }

    /**
     * 后台管理审核通过试用申请
     */
    @RequiresPermissions("shop:trial:edit")
    @Log(title = "DUTP-DTB_025教师试用申请", businessType = BusinessType.UPDATE)
    @PutMapping("/agreeTrial")
    public AjaxResult agreeTrial(@RequestBody DtbUserTrialApply dtbUserTrialApply) {
        return dtbUserTrialApplyService.agreeTrial(dtbUserTrialApply);
    }

}
