package cn.dutp.shop.service;

import cn.dutp.shop.domain.DtbUserInvoiceFile;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * DUTP-DTB-036发票文件Service接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface IDtbUserInvoiceFileService extends IService<DtbUserInvoiceFile>
{
    /**
     * 查询DUTP-DTB-036发票文件
     *
     * @param fileId DUTP-DTB-036发票文件主键
     * @return DUTP-DTB-036发票文件
     */
    public DtbUserInvoiceFile selectDtbUserInvoiceFileByFileId(Long fileId);

    /**
     * 查询DUTP-DTB-036发票文件列表
     *
     * @param dtbUserInvoiceFile DUTP-DTB-036发票文件
     * @return DUTP-DTB-036发票文件集合
     */
    public List<DtbUserInvoiceFile> selectDtbUserInvoiceFileList(DtbUserInvoiceFile dtbUserInvoiceFile);

    /**
     * 新增DUTP-DTB-036发票文件
     *
     * @param dtbUserInvoiceFile DUTP-DTB-036发票文件
     * @return 结果
     */
    public boolean insertDtbUserInvoiceFile(DtbUserInvoiceFile dtbUserInvoiceFile);

    /**
     * 修改DUTP-DTB-036发票文件
     *
     * @param dtbUserInvoiceFile DUTP-DTB-036发票文件
     * @return 结果
     */
    public boolean updateDtbUserInvoiceFile(DtbUserInvoiceFile dtbUserInvoiceFile);

    /**
     * 批量删除DUTP-DTB-036发票文件
     *
     * @param fileIds 需要删除的DUTP-DTB-036发票文件主键集合
     * @return 结果
     */
    public boolean deleteDtbUserInvoiceFileByFileIds(List<Long> fileIds);

    public List<DtbUserInvoiceFile> selectByApplyId(Long applyId);

    public boolean addsSaleInvoice(List<DtbUserInvoiceFile> dtbUserInvoiceFile);
}
