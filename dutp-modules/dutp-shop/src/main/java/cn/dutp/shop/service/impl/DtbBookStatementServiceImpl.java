package cn.dutp.shop.service.impl;
import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBookOrder;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.shop.domain.DtbBookStatement;
import cn.dutp.shop.domain.DtbBookStatementOrder;
import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.dto.ReconcileLookDto;
import cn.dutp.shop.domain.dto.ReconciliationDto;
import cn.dutp.shop.domain.dto.StatementItemDto;
import cn.dutp.shop.domain.vo.DtbBookOrderItemVo;
import cn.dutp.shop.mapper.DtbBookOrderMapper;
import cn.dutp.shop.mapper.DtbBookStatementMapper;
import cn.dutp.shop.mapper.DtbBookStatementOrderMapper;
import cn.dutp.shop.mapper.DtbUserInvoiceApplyMapper;
import cn.dutp.shop.service.IDtbBookOrderService;
import cn.dutp.shop.service.IDtbBookStatementOrderService;
import cn.dutp.shop.service.IDtbBookStatementService;
import cn.dutp.shop.service.IDtbUserInvoiceApplyService;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class DtbBookStatementServiceImpl extends ServiceImpl<DtbBookStatementMapper, DtbBookStatement> implements IDtbBookStatementService
{
    @Autowired
    private DtbBookStatementMapper dtbBookStatementMapper;
    @Autowired
    private IDtbBookStatementOrderService dtbBookStatementOrderService;
    @Autowired
    private DtbBookStatementOrderMapper dtbBookStatementOrderMapper;
    @Autowired
    private IDtbUserInvoiceApplyService dtbUserInvoiceApplyService;
    @Autowired
    private DtbUserInvoiceApplyMapper dtbUserInvoiceApplyMapper;
    @Autowired
    private DtbBookOrderMapper dtbBookOrderMapper;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private RemoteUserMessageService remoteUserMessageService;
    /**
     * 查询结算单
     *
     * @param statementId 结算单主键
     * @return 结算单
     */
    @Override
    public DtbBookStatement selectDtbBookStatementByStatementId(Long statementId)
    {
        return this.getById(statementId);
    }

    /**
     * 查询结算单列表
     *
     * @param dtbBookStatement 结算单
     * @return 结算单
     */
    @Override
    public List<DtbBookStatement> selectDtbBookStatementList(DtbBookStatement dtbBookStatement)
    {

        return dtbBookStatementMapper.selectStatementList(dtbBookStatement);
    }

    /**
     * 新增结算单
     *
     * @param dtbBookStatement 结算单
     * @return 结果
     */
    @Override
    public boolean insertDtbBookStatement(DtbBookStatement dtbBookStatement)
    {
        DtbBookStatement statement = new DtbBookStatement();
        Long currentMerchantId = dtbBookStatement.getMerchantId();
        statement.setStatementNo(orderNumberGenerator());
        if (currentMerchantId != null){
            statement.setMerchantId(currentMerchantId);
        }
        statement.setSchoolId(dtbBookStatement.getSchoolId());
        boolean save = this.save(statement);
        List<Long> orderIds = dtbBookStatement.getOrderIds();
        for (Long orderId : orderIds){
            DtbBookStatementOrder dtbBookStatementOrder = new DtbBookStatementOrder();
            dtbBookStatementOrder.setStatementId(statement.getStatementId());
            dtbBookStatementOrder.setOrderId(orderId);
            dtbBookStatementOrderService.insertDtbBookStatementOrder(dtbBookStatementOrder);
        }
        return save;
    }

    /**
     * 修改结算单
     *
     * @param dtbBookStatement 结算单
     * @return 结果
     */
    @Override
    public boolean updateDtbBookStatement(DtbBookStatement dtbBookStatement)
    {
        return this.updateById(dtbBookStatement);
    }

    /**
     * 批量删除结算单
     *
     * @param statementId 需要删除的结算单主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookStatementByStatementIds(Long statementId)
    {
        //删除 statement_order
        dtbBookStatementOrderMapper.deleteStatementOrderById(statementId);
        return dtbBookStatementMapper.delById(statementId);
    }

    @Override
    public boolean changeStatementStatus( Long statementId) {
        //修改订单状态为已结算
        LambdaQueryWrapper<DtbBookStatementOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DtbBookStatementOrder::getStatementId, statementId);
        List<DtbBookStatementOrder> statementOrders = dtbBookStatementOrderMapper.selectList(wrapper);
        for (DtbBookStatementOrder statementOrder : statementOrders){
            Long orderId = statementOrder.getOrderId();
            DtbBookOrder dtbBookOrder = dtbBookOrderMapper.selectById(orderId);
            if(ObjectUtil.isNotNull(dtbBookOrder)){
                dtbBookOrder.setOrderStatus("completed");

                if (null == dtbBookOrder.getPayTime()){
                    dtbBookOrder.setPayTime(new Date());
                }

                dtbBookOrderMapper.updateById(dtbBookOrder);
            }

        }
        return dtbBookStatementMapper.changeStatementStatus(statementId);
    }

    @Override
    public List<ReconciliationDto> reconcileList(ReconciliationDto reconciliationDto) {
        List<ReconciliationDto> list = dtbBookStatementMapper.selectReconcileList(reconciliationDto);
//        if (ObjectUtil.isNotEmpty(list)) {
//            //查询statementOrder列表
//            LambdaQueryWrapper<DtbBookStatementOrder> queryWrapper = new LambdaQueryWrapper<>();
//            List<DtbBookStatementOrder> statementOrders = dtbBookStatementOrderMapper.selectList(queryWrapper);
//            //过滤出所有statementOrder中orderId
//            List<Long> orderIds = statementOrders.stream().map(DtbBookStatementOrder::getOrderId).collect(Collectors.toList());
//            //对账列表中过滤
//            list = list.stream().filter(item -> !orderIds.contains(item.getOrderId())).collect(Collectors.toList());
//        }
        return list;
    }

    @Override
    public List<ReconcileLookDto> getReconcileListById(Long orderId) {
        return dtbBookStatementMapper.getReconcileListById(orderId);
    }

    @Override
    public List<DtbBookOrderItemVo> selectOrderItemByOrderId(Long orderId) {
        return dtbBookStatementMapper.selectOrderItemByOrderId(orderId);
    }

    @Override
    public void exportStatement(HttpServletResponse response, DtbBookStatement dtbBookStatement) {
        // 获取结算单中的订单数据
        List<DtbBookStatement> list = dtbBookStatementMapper.selectStatmentExportList(dtbBookStatement.getStatementId());

        // 按书商名称、订单号和学校名称分组
        Map<String, List<DtbBookStatement>> collect = list.stream()
                .collect(Collectors.groupingBy(statement ->
                        statement.getMerchanName() + "_" + statement.getOrderNo() + "_" + statement.getSchoolName()));

        // 创建行列表，用于记录需要合并的行
        List<Map<String, Integer>> indexList = new ArrayList<>();

        // 从第一行开始
        int rowNum = 1;

        // 遍历分组后的数据，计算需要合并的行数
        for (Map.Entry<String, List<DtbBookStatement>> entry : collect.entrySet()) {
            List<DtbBookStatement> statementList = entry.getValue();
            int merger = statementList.size();

            if (merger > 1) {
                Map<String, Integer> rowMap = new HashMap<>();
                rowMap.put("row", rowNum); // 记录起始行
                rowMap.put("merger", merger - 1); // 记录需要合并的行数
                indexList.add(rowMap);
            }

            rowNum += merger; // 更新行号
        }

        // 创建 Excel 文件对象
        XSSFWorkbook wb = new XSSFWorkbook();

        // 创建 Sheet 列
        XSSFSheet sheet = wb.createSheet("结算单明细");

        // 创建第一行，即表头
        XSSFRow row0 = sheet.createRow(0);

        // 定义 Excel 标题
        String[] titles = new String[]{"书商名称", "订单号", "学校名称", "教材名称", "ISBN/ISSN", "采购数量", "单价", "总计"};

        // 添加表头
        for (int i = 0; i < titles.length; i++) {
            row0.createCell(i).setCellValue(titles[i]);
        }

        // 如果数据为空，返回空表
        if (ObjectUtil.isEmpty(list)) {
            try {
                String fileName = "结算单明细";
                OutputStream output = response.getOutputStream();
                response.reset();
                response.setHeader("Content-disposition", "attachment; filename=" + new String(fileName.getBytes(), "iso-8859-1") + ".xlsx");
                response.setContentType("*/*");
                response.setCharacterEncoding("utf-8");
                output.close();
            } catch (Exception e) {
                throw new ServiceException("导出失败");
            }
            return;
        }

        // 填充数据
        int dataRowNum = 1; // 数据从第二行开始
        for (DtbBookStatement statement : list) {
            XSSFRow newRow = sheet.createRow(dataRowNum);
            newRow.createCell(0).setCellValue(statement.getMerchanName());
            newRow.createCell(1).setCellValue(statement.getOrderNo());
            newRow.createCell(2).setCellValue(statement.getSchoolName());
            newRow.createCell(3).setCellValue(statement.getBookName());
            newRow.createCell(4).setCellValue(statement.getIsbn());
            if (ObjectUtil.isNull(statement.getIsbn())) {
                newRow.createCell(4).setCellValue(statement.getIssn());
            }
            newRow.createCell(5).setCellValue(statement.getBookQuantity());
            newRow.createCell(6).setCellValue(statement.getPriceOrderItem().toString());
            newRow.createCell(7).setCellValue(statement.getTotalAmount().toString());
            dataRowNum++;
        }

        // 合并单元格（只合并书商名称、订单号、学校名称列）
        for (Map<String, Integer> m : indexList) {
            int startRow = m.get("row");
            int endRow = startRow + m.get("merger");

            // 只合并第 0 列（书商名称）、第 1 列（订单号）、第 2 列（学校名称）
            sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 0, 0)); // 书商名称
            sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 1, 1)); // 订单号
            sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 2, 2)); // 学校名称
        }

        // 导出 Excel 文件
        try {
            String fileName = "结算单明细";
            OutputStream output = response.getOutputStream();
            response.reset();
            response.setHeader("Content-disposition", "attachment; filename=" + new String(fileName.getBytes(), "iso-8859-1") + ".xlsx");
            response.setContentType("*/*");
            response.setCharacterEncoding("utf-8");
            wb.write(output);
            output.close();
        } catch (Exception e) {
            throw new ServiceException("导出失败");
        }
    }

    @Override
    public void exportReconcile(HttpServletResponse response, DtbBookStatement dtbBookStatement) {
        if(ObjectUtil.isNull(dtbBookStatement.getIds())){
            throw new ServiceException("请选择要导出的数据");
        }

        // 去重处理，避免重复导出相同订单
        List<Long> ids = dtbBookStatement.getIds().stream().distinct().collect(Collectors.toList());

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet("对账单");

        // 创建表头
        XSSFRow row0 = sheet.createRow(0);
        String[] titles = {"书商名称", "订单号", "学校名称", "教材名称", "ISBN/ISSN", "采购数量", "已使用购书码", "未使用购书码"};
        for (int i = 0; i < titles.length; i++) {
            row0.createCell(i).setCellValue(titles[i]);
        }

        List<Map<String, Integer>> mergeRegions = new ArrayList<>();
        int rowNum = 1;

        for (Long orderId : ids) {
            List<DtbBookStatement> list = dtbBookStatementMapper.selectReconcileExportList(orderId);
            if (ObjectUtil.isEmpty(list))
                continue;

            // 按关键字段分组
            Map<String, List<DtbBookStatement>> groups = list.stream()
                    .collect(Collectors.groupingBy(item ->
                            item.getMerchanName() + "_" + item.getOrderNo() + "_" + item.getSchoolName()));

            for (Map.Entry<String, List<DtbBookStatement>> entry : groups.entrySet()) {
                List<DtbBookStatement> groupItems = entry.getValue();
                int groupSize = groupItems.size();

                // 记录需要合并的行
                if (groupSize > 1) {
                    Map<String, Integer> mergeInfo = new HashMap<>();
                    mergeInfo.put("startRow", rowNum);
                    mergeInfo.put("rowsToMerge", groupSize - 1); // 合并行数 = 总行数 - 1
                    mergeRegions.add(mergeInfo);
                }

                // 填充数据行
                for (DtbBookStatement item : groupItems) {
                    XSSFRow row = sheet.createRow(rowNum);
                    row.createCell(0).setCellValue(item.getMerchanName());
                    row.createCell(1).setCellValue(item.getOrderNo());
                    row.createCell(2).setCellValue(item.getSchoolName());
                    row.createCell(3).setCellValue(item.getBookName());
                    // 处理 ISBN/ISSN：优先取 ISBN，为空时取 ISSN
                    String isbnOrIssn = ObjectUtil.isNotNull(item.getIsbn()) ? item.getIsbn() : item.getIssn();
                    row.createCell(4).setCellValue(isbnOrIssn);
                    row.createCell(5).setCellValue(item.getBookQuantity());
                    row.createCell(6).setCellValue(item.getUseCode());
                    row.createCell(7).setCellValue(item.getNoUseCode());
                    rowNum++;
                }
            }
        }

        // 合并单元格操作
        for (Map<String, Integer> region : mergeRegions) {
            int startRow = region.get("startRow");
            int endRow = startRow + region.get("rowsToMerge");
            // 合并书商名称、订单号、学校名称三列
            sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 0, 0));
            sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 1, 1));
            sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, 2, 2));
        }

        // 导出文件
        try {
            String fileName = "对账单_" + System.currentTimeMillis() + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            wb.write(response.getOutputStream());
            wb.close();
        } catch (Exception e) {
            throw new ServiceException("导出失败: " + e.getMessage());
        }
    }

    @Override
    public List<StatementItemDto> selectStatementItem(Long statementId) {
        return dtbBookStatementMapper.selectStatementItem(statementId);
    }

    @Override
    public List<StatementItemDto> selectOrderItem(Long orderId) {
        return dtbBookStatementMapper.selectOrderItem(orderId);
    }

    /**
     * 重新开票
     * @param statementId 对象
     * @return 结果
     */
    @Override
    public boolean changeInvoice(Long statementId) {
        LambdaUpdateWrapper<DtbUserInvoiceApply> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DtbUserInvoiceApply::getStatementId,statementId)
                .set(DtbUserInvoiceApply::getApplyStatus,3);

        //限制换票次数
        LambdaQueryWrapper<DtbUserInvoiceApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DtbUserInvoiceApply::getStatementId, statementId);
        List<DtbUserInvoiceApply> dtbUserInvoiceApplies = dtbUserInvoiceApplyMapper.selectList(wrapper);
        if (ObjectUtil.isNotEmpty(dtbUserInvoiceApplies)){
            for(DtbUserInvoiceApply apply : dtbUserInvoiceApplies){
                if(apply.getChangeCount() == 1){
                    throw new ServiceException("只有一次换开机会");
                }
            }
        }

        //发送消息
        String title = "开票申请提醒";
        String content = String.format(NotificationConstants.REISSUE_INVOICE, SecurityUtils.getUsername());
        // 发消息给管理员
        R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == adminIdList.getCode())
        {
            throw new ServiceException("根据角色查获取用户列表失败");
        }
        List<SysUser> receiveList = adminIdList.getData();
        if (!CollectionUtils.isEmpty(receiveList)) {
            receiveList.stream().forEach(e -> {
                sendOrderMsg(title, content, e.getUserId(),statementId);
            });
        }
        // 发送消息给售后管理模块的人
        R<List<SysUser>> menuUserIdList = remoteUserService.listUserByMenuId(2139L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == menuUserIdList.getCode())
        {
            throw new ServiceException("据菜单获取用户列表失败");
        }
        List<SysUser> menuUserList = menuUserIdList.getData();
        if (!CollectionUtils.isEmpty(menuUserList)) {
            menuUserList.stream().forEach(e -> {
                sendOrderMsg(title, content, e.getUserId(),statementId);
            });
        }
        return dtbUserInvoiceApplyService.update(updateWrapper);
    }

    public void sendOrderMsg(String title,String content,Long toUserId,Long statementId) {
        boolean isResult = false;
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent(content);
        dutpUserMessage.setTitle(title);
        // 发送者
        dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(toUserId);
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        dutpUserMessage.setBusinessId(statementId);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            throw new ServiceException("Message模块未启动，无法发送消息，申请提交失败！");
        }
    }

    /**
     * 自动生成结算单
     * @return
     */
    public String orderNumberGenerator() {
        Date now = new Date();
        String formattedDate = new SimpleDateFormat(DutpConstant.DATE_FORMAT).format(now);
        LambdaQueryWrapper<DtbBookStatement> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.likeRight(DtbBookStatement::getStatementNo, formattedDate);
        Integer sequenceNumber = dtbBookStatementMapper.selectCount(lambdaQueryWrapper);
        ++sequenceNumber;
        return formattedDate + DutpConstant.STATMENT_CODE + String.format("%06d", sequenceNumber);
    }

}
