package cn.dutp.shop.service;

import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * DUTP-DTB_002数字教材Service接口
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
public interface IDtbBookService extends IService<DtbBook>
{
    /**
     * 查询DUTP-DTB_002数字教材
     *
     * @param bookId DUTP-DTB_002数字教材主键
     * @return DUTP-DTB_002数字教材
     */
    public DtbBook selectDtbBookByBookId(Long bookId);

    /**
     * 查询DUTP-DTB_002数字教材列表
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return DUTP-DTB_002数字教材集合
     */
    public List<DtbBook> selectDtbBookList(DtbBook dtbBook);

    /**
     * 新增DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    public boolean insertDtbBook(DtbBook dtbBook);

    /**
     * 修改DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    public boolean updateDtbBook(DtbBook dtbBook);

    /**
     * 批量删除DUTP-DTB_002数字教材
     *
     * @param bookIds 需要删除的DUTP-DTB_002数字教材主键集合
     * @return 结果
     */
    public boolean deleteDtbBookByBookIds(List<Long> bookIds);

    List<DtbBook> bookShopList(DtbBook dtbBook);

    List<Tree<String>> selectShopDtbBookList(DtbBook dtbBook);

    Integer bookShopListCount(DtbBook dtbBook);

    /**
     * 查询合作院校数字教材集合
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 数字教材集合
     */
    public List<DtbBook> selectPartnersDtbBookList(DtbBook dtbBook);
    /**
     * 查询购买教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 数字教材集合
     */
    public DtbBook getShopBook(DtbBook dtbBook);
}
