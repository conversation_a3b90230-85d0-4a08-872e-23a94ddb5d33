package cn.dutp.shop.service.impl;

import cn.dutp.domain.DtbBookOrder;
import cn.dutp.shop.domain.DtbBookStatementOrder;
import cn.dutp.shop.mapper.DtbBookStatementOrderMapper;
import cn.dutp.shop.service.IDtbBookStatementOrderService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 结算单的订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class DtbBookStatementOrderServiceImpl extends ServiceImpl<DtbBookStatementOrderMapper, DtbBookStatementOrder> implements IDtbBookStatementOrderService
{
    @Autowired
    private DtbBookStatementOrderMapper dtbBookStatementOrderMapper;

    /**
     * 查询结算单的订单
     *
     * @param statementOrderId 结算单的订单主键
     * @return 结算单的订单
     */
    @Override
    public DtbBookStatementOrder selectDtbBookStatementOrderByStatementOrderId(Long statementOrderId)
    {
        return this.getById(statementOrderId);
    }

    /**
     * 查询结算单的订单列表
     *
     * @param dtbBookStatementOrder 结算单的订单
     * @return 结算单的订单
     */
    @Override
    public List<DtbBookStatementOrder> selectDtbBookStatementOrderList(DtbBookStatementOrder dtbBookStatementOrder)
    {
        LambdaQueryWrapper<DtbBookStatementOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookStatementOrder.getStatementId())) {
                lambdaQueryWrapper.eq(DtbBookStatementOrder::getStatementId
                ,dtbBookStatementOrder.getStatementId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookStatementOrder.getOrderId())) {
                lambdaQueryWrapper.eq(DtbBookStatementOrder::getOrderId
                ,dtbBookStatementOrder.getOrderId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增结算单的订单
     *
     * @param dtbBookStatementOrder 结算单的订单
     * @return 结果
     */
    @Override
    public boolean insertDtbBookStatementOrder(DtbBookStatementOrder dtbBookStatementOrder)
    {

        return this.save(dtbBookStatementOrder);
    }

    /**
     * 修改结算单的订单
     *
     * @param dtbBookStatementOrder 结算单的订单
     * @return 结果
     */
    @Override
    public boolean updateDtbBookStatementOrder(DtbBookStatementOrder dtbBookStatementOrder)
    {
        return this.updateById(dtbBookStatementOrder);
    }

    /**
     * 批量删除结算单的订单
     *
     * @param statementOrderIds 需要删除的结算单的订单主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookStatementOrderByStatementOrderIds(List<Long> statementOrderIds)
    {
        return this.removeByIds(statementOrderIds);
    }

    @Override
    public List<DtbBookStatementOrder> getStatementOrderByStatementId(Long statementId) {
        return dtbBookStatementOrderMapper.selectByStatementId(statementId);
    }



}
