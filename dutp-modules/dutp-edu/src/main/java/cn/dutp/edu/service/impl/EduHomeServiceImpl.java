package cn.dutp.edu.service.impl;

import cn.dutp.domain.DtbBookOrderItem;
import cn.dutp.domain.DtbBookOrderVo;
import cn.dutp.domain.DtbBookVo;
import cn.dutp.domain.vo.DtbEduBookOrderDetailVo;
import cn.dutp.domain.vo.DtbEduBookOrderVo;
import cn.dutp.edu.domain.DutpSchool;
import cn.dutp.edu.domain.vo.DtbBookRefundOrderManagementVo;
import cn.dutp.edu.mapper.EduHomeMapper;
import cn.dutp.edu.service.EduHomeService;
import cn.dutp.message.api.domain.DutpUserMessage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【dutp_school(DUTP-BASE-024学校表)】的数据库操作Service实现
 * @createDate 2025-01-13 16:00:49
 */
@Service
public class EduHomeServiceImpl extends ServiceImpl<EduHomeMapper, DutpSchool>
        implements EduHomeService {
    @Autowired
    private EduHomeMapper eduHomeMapper;

    /**
     * 根据登陆用户的学校id获取学校 院系数量
     *
     * @param schoolId 学校id
     * @return List<Long>
     */
    @Override
    public List<Long> selectDepartmentIdBySchoolId(long schoolId) {
        return eduHomeMapper.selectDepartmentIdBySchoolId(schoolId);
    }

    /**
     * 根据登陆用户的学校id获取 专业数量
     *
     * @param departmentList 院系id列表
     * @return Integer
     */
    @Override
    public Integer countMajorByDepartmentList(List<Long> departmentList) {
        return eduHomeMapper.countMajorByDepartmentList(departmentList);
    }

    /**
     * 根据登陆用户id获取消息列表
     *
     * @param toUserId 用户id
     * @return List<DutpUserMessage>
     */
    @Override
    public List<DutpUserMessage> messageList(long toUserId) {
        // 用户类型1为后台用户
        return eduHomeMapper.messageList(toUserId);
    }

    /**
     * 根据登陆用户的学校id获取订单信息
     *
     * @param schoolId 学校id
     * @return DtbBookOrderVo
     */
    @Override
    public DtbBookOrderVo countOrderNumber(long schoolId) {
        return eduHomeMapper.countOrderNumber(schoolId);
    }


    /**
     *  获取订单图书列表
     *
     * @param schoolId 学校id, limitNum 限制条数
     * @return DtbBookOrderVo
     */
    @Override
    public List<DtbBookOrderItem> getBookListOfOrder(long schoolId, int limitNum) {
        return null;
    }

    /**
     * 根据登陆用户的学校id获取售后订单信息
     *
     * @param schoolId 学校id
     * @return DtbBookRefundOrderVo
     */
    @Override
    public DtbBookRefundOrderManagementVo countRefundOrderNumber(long schoolId) {
        return eduHomeMapper.countRefundOrderNumber(schoolId);
    }

    /**
     * 根据登陆用户的学校id获取教材信息
     *
     * @param schoolId 学校id
     * @return DtbBookVo
     */
    @Override
    public DtbBookVo overviewOfTextbooks(long schoolId) {
        return eduHomeMapper.overviewOfTextbooks(schoolId);
    }

    /**
     * 根据登陆用户的学校id获取教材订单信息
     *
     * @param schoolId 学校id, limitNum 限制个数
     * @return List<DtbEduBookOrderVo>
     */
    @Override
    public List<DtbEduBookOrderVo> selectOrderInfoBySchoolId(long schoolId, int limitNum) {
        return eduHomeMapper.selectOrderInfoBySchoolId(schoolId, limitNum);
    }

    /**
     * 查询子订单详情
     *
     * @param orderId 订单id
     * @return 子订单详情
     */
    @Override
    public List<DtbEduBookOrderDetailVo> selectOrderDetailById(long orderId) {
        return eduHomeMapper.selectOrderDetailById(orderId);
    }
}




