package cn.dutp.edu.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.domain.DtbBook;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DutpEduOrderDetailVo {

    /**
     * 明细ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderItemId;

    /**
     * 书籍ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 教材名称
     */
    private String bookName;

    /**
     * 教材编号
     */
    private String bookNo;

    /**
     * ISBN序列号
     */
    private String isbn;

    /**
     * 主编
     */
    private String editor;

    /** 出版社id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long houseId;

    /** 出版社名称*/
    private String houseName;

    /**
     * normal正常canceling申请作废cancel已作废
     */
    @Excel(name = "normal正常canceling申请作废cancel已作废")
    private String itemStatus;

    /**
     * 出版日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date publishDate;

    /**
     * 出版状态1未出版2已出版
     */
    private Integer publishStatus;

    /**
     * 上架状态1已上架2未上架3召回4即将上架
     */
    private Integer shelfState;

    /**
     * 合作院校
     */
    private String schoolName;

    /**
     * 书籍数量
     */
    private Long bookQuantity;

    /**
     * 书的售价
     */
    private BigDecimal priceSale;

    /**
     * 书的定价
     */
    private BigDecimal priceCounter;

    /**
     * 改完的单价
     */
    private BigDecimal priceOrderItem;

    /**
     * 已绑定购书码数量
     */
    private Integer bindingCodeCount;

    /**
     * 未绑定购书码数量
     */
    private Integer unBindingCodeCount;

    /**
     * 子教材
     */
    private List<DtbBook> childList;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;
}
