package cn.dutp.edu.service;

import cn.dutp.domain.DtbBookOrderItem;
import cn.dutp.domain.DtbBookOrderVo;
import cn.dutp.domain.DtbBookVo;
import cn.dutp.domain.vo.DtbEduBookOrderDetailVo;
import cn.dutp.domain.vo.DtbEduBookOrderVo;
import cn.dutp.edu.domain.DutpSchool;
import cn.dutp.edu.domain.vo.DtbBookRefundOrderManagementVo;
import cn.dutp.message.api.domain.DutpUserMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【dutp_school(DUTP-BASE-024学校表)】的数据库操作Service
 * @createDate 2025-01-13 16:00:50
 */
public interface EduHomeService extends IService<DutpSchool> {
    /**
     * 根据登陆用户的学校id获取学校 院系数量
     *
     * @param schoolId 学校id
     * @return List<Long>
     */
    List<Long> selectDepartmentIdBySchoolId(long schoolId);

    /**
     * 根据登陆用户的学校id获取 专业数量
     *
     * @param departmentList 院系id列表
     * @return Integer
     */
    Integer countMajorByDepartmentList(List<Long> departmentList);

    /**
     * 根据登陆用户id获取消息列表
     *
     * @param toUserId 用户id
     * @return List<DutpUserMessage>
     */
    List<DutpUserMessage> messageList(long toUserId);

    /**
     * 根据登陆用户的学校id获取订单信息
     *
     * @param schoolId 学校id
     * @return DtbBookOrderVo
     */
    DtbBookOrderVo countOrderNumber(long schoolId);

    /**
     * 查询指定个数的订单图书信息
     *
     * @param schoolId 学校id, limitNum 限制个数
     * @return DtbBookOrderVo
     */
    List<DtbBookOrderItem> getBookListOfOrder(long schoolId, int limitNum);

    /**
     * 根据登陆用户的学校id获取售后订单信息
     *
     * @param schoolId 学校id
     * @return DtbBookRefundOrderVo
     */
    DtbBookRefundOrderManagementVo countRefundOrderNumber(long schoolId);

    /**
     * 根据登陆用户的学校id获取教材信息
     *
     * @param schoolId 学校id
     * @return DtbBookVo
     */
    DtbBookVo overviewOfTextbooks(long schoolId);

    /**
     * 根据登陆用户的学校id获取教材订单信息
     *
     * @param schoolId 学校id, limitNum 限制个数
     * @return List<DtbEduBookOrderVo>
     */
    List<DtbEduBookOrderVo> selectOrderInfoBySchoolId(long schoolId, int limitNum);

    /**
     * 查询子订单详情
     *
     * @param orderId 订单id
     * @return 子订单详情
     */
    List<DtbEduBookOrderDetailVo> selectOrderDetailById(long orderId);


}
