<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.DtbBookCodeExportMapper">

    <resultMap id="BaseResultMap" type="cn.dutp.edu.domain.DtbBookCodeExport">
            <id property="exportDataId" column="export_data_id" />
            <result property="bookId" column="book_id" />
            <result property="exportUserId" column="export_user_id" />
            <result property="exportDate" column="export_date" />
            <result property="codeQuantity" column="code_quantity" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <!--查询当前书籍下已兌換的购书码信息列表-->
    <select id="alreadyBoundCode" resultType="cn.dutp.domain.vo.DtbBookPurchaseCodeVO">
        SELECT
            du.nick_name,
            du.user_type,
            ds.school_id,
            ds.school_name,
            dbpc.code_id,
            dbpc.code,
            dbpc.book_id,
            dbpc.phone,
            dbpc.bind_date,
            CASE
            WHEN DATE_ADD(dbpc.bind_date, INTERVAL 30 DAY) &lt; NOW() THEN 1
            ELSE 0
            END AS isUnbind,
            dboc.unbind_quantity,
            dboc.order_code_id,
            dboc.export_quantity,
            book.book_name,
            book.isbn
        FROM
            dtb_book_purchase_code dbpc
                LEFT JOIN
            dtb_book_order_code dboc
            ON dbpc.code_id = dboc.code_id
                LEFT JOIN dutp_user du
                          ON du.user_id = dbpc.user_id
                LEFT JOIN dutp_school ds
                          ON ds.school_id = du.academy_id
                LEFT JOIN dtb_book book
                          ON book.book_id = dbpc.book_id
        WHERE
            dbpc.state = '3'
          AND dbpc.del_flag ='0'
          AND dbpc.book_id = #{bookId}
          AND dboc.school_id = #{schoolId}
            AND dboc.order_id = #{orderId}
    </select>
    <!--查询登录人学校名下当前书籍下的未兌換的购书码-->
    <select id="getUnboundCodeList" resultType="cn.dutp.domain.vo.DtbBookPurchaseCodeVO">
        SELECT dbpc.code_id,
               dbpc.code,
               dbpc.book_id,
               dboc.order_code_id,
               dboc.export_quantity,
               book.shelf_state
        from
            dtb_book_order_code dboc
        left join
            dtb_book_purchase_code dbpc on dbpc.code_id = dboc.code_id
        LEFT JOIN
            dtb_book book ON book.book_id = dboc.book_id
        WHERE dbpc.state = '2'
          AND dbpc.del_flag = '0'
          AND dbpc.book_id = #{bookId}
          AND dboc.school_id = #{schoolId}
        and dboc.order_id = #{orderId}
        ORDER BY dboc.export_quantity
    </select>
    <!--获取持有当前书籍购书码的学院集合-->
    <select id="getExportCollegeList" resultType="cn.dutp.domain.vo.DtbBookSchoolVo">
        SELECT ds.school_id,
               ds.school_name
        FROM dtb_book_purchase_code dbpc
                 LEFT JOIN
             dtb_book_order_code dboc
             ON dbpc.code_id = dboc.code_id
                 LEFT JOIN dutp_user du
                           ON du.user_id = dbpc.user_id
                 LEFT JOIN dutp_school ds
                           ON ds.school_id = du.academy_id
        WHERE dbpc.state = '3'
          AND dbpc.del_flag = '0'
          AND dbpc.book_id = #{bookId}
          AND dboc.school_id = #{schoolId}
        GROUP BY ds.school_id
    </select>

    <!--获取导出记录列表-->
    <select id="getExportRecordList" resultType="cn.dutp.domain.vo.DtbBookCodeExportInfoVo">
        SELECT info.export_date,
        info.code_quantity,
        info.export_user_id,
        du.nick_name,
        du.user_type,
        du.phonenumber,
        du.email,
        book.book_name,
        book.book_id
        FROM dtb_book_code_export_info info
        LEFT JOIN
        sys_user du
        ON du.user_id = info.export_user_id
        LEFT JOIN dutp_school ds
        ON ds.school_id = du.school_id
        LEFT JOIN dtb_book book
        ON book.book_id = info.book_id
        WHERE info.book_id IN
        <foreach collection="bookIdList" item="bookId" open="(" separator="," close=")">
            #{bookId}
        </foreach>
        ORDER BY info.export_date DESC
    </select>
    <!--获取要导出的未兌換购书码信息-->
    <select id="getUnboundExport" resultType="cn.dutp.edu.domain.vo.DtbBookdtbPurchaseCodeUnboundExport">
        SELECT dbpc.code,
               book.book_name,
               dbpc.code_id,
               dboc.order_code_id,
               book.isbn
        FROM dtb_book_purchase_code dbpc
                 LEFT JOIN
             dtb_book_order_code dboc
             ON dbpc.code_id = dboc.code_id
                 LEFT JOIN dtb_book book
                           ON book.book_id = dbpc.book_id
        WHERE dbpc.state = '2'
          AND dbpc.del_flag = '0'
          AND dbpc.book_id = #{bookId}
          AND dboc.school_id = #{schoolId}
        ORDER BY dboc.export_quantity
        LIMIT #{exportQuantity};
    </select>
    <!--获取要导出的已兌換购书码信息-->
    <select id="alreadyBoundExport" resultType="cn.dutp.edu.domain.vo.DtbBookPurchaseCodeExport">
        SELECT
        du.nick_name,
        du.user_type,
        ds.school_id,
        ds.school_name,
        dbpc.code_id,
        dbpc.code,
        dbpc.book_id,
        dbpc.phone,
        dbpc.bind_date,
        CASE
        WHEN DATE_ADD(dbpc.bind_date, INTERVAL 30 DAY) &lt; NOW() THEN 1
        ELSE 0
        END AS isUnbind,
        dboc.unbind_quantity,
        dboc.order_code_id,
        dboc.export_quantity,
        book.book_name,
        book.isbn
        FROM
        dtb_book_purchase_code dbpc
        LEFT JOIN
        dtb_book_order_code dboc
        ON dbpc.code_id = dboc.code_id
        LEFT JOIN dutp_user du
        ON du.user_id = dbpc.user_id
        LEFT JOIN dutp_school ds
        ON ds.school_id = du.academy_id
        LEFT JOIN dtb_book book
        ON book.book_id = dbpc.book_id
        WHERE
        dbpc.state = '3'
        AND dbpc.del_flag ='0'
        AND dbpc.book_id = #{bookId}
        AND dboc.school_id = #{schoolId}
        <if test="null!=collegeIdList and collegeIdList.size > 0">
            AND ds.school_id in
            <foreach collection="collegeIdList" item="collegeId" open="(" separator="," close=")">
                #{collegeId}
            </foreach>
        </if>
    </select>
</mapper>
