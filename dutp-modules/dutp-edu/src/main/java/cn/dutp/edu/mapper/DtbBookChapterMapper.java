package cn.dutp.edu.mapper;

import cn.dutp.edu.domain.vo.DtbBookChapter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数字教材章节目录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Repository
public interface DtbBookChapterMapper extends BaseMapper<DtbBookChapter> {

    /**
     * 获取数字教材章节目录列表
     *
     * @param bookId
     * @param currentVersionId
     * @return
     */
    List<DtbBookChapter> queryBookChapterDataList(@Param("bookId") Long bookId, @Param("currentVersionId") Long currentVersionId);
}
