package cn.dutp.edu.domain.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DutpEduOrderVo {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 原价
     */
    private BigDecimal price;

    /**
     * 采购时间
     */
    private Date createTime;

    /**
     * 订单状态，pending：订单待确认[待支付]、paid已支付、editing：修改待确认、canceling作废待确认，create_refused采购驳回，edit_refused修改驳回, cancel_refused作废驳回，settlement结算中，completed：已完成（已结算）、first_refused一级审核驳回【样书】，second_refused二级审核驳回【样书】，second_auditting二级审核中【样书】，cancelled：已取消
     */
    private String orderStatus;


    List<DutpEduOrderDetailVo> itemList;
    /**
     * 激活数量
     */
    private Integer activationCount;
    /**
     * 未激活数量
     */
    private Integer notActiveCount;

}
