<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.IDtbBookOrderCodeMapper">
    <!--更新子订单的导出次数+1-->
    <update id="updateBookCodeExportQuantity">
        UPDATE dtb_book_order_code
        SET export_quantity = export_quantity + 1,
        update_time = now(),
        update_by = #{updateBy}
        WHERE order_code_id in
        <foreach item="orderCodeId" collection="orderItemList" open="(" separator="," close=")">
            #{orderCodeId}
        </foreach>
    </update>
</mapper>