package cn.dutp.edu;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
@EnableDiscoveryClient
public class DutpEduApplication {
    public static void main(String[] args) {
        SpringApplication.run(DutpEduApplication.class, args);
        System.out.println("教育模块启动成功");
    }
}
