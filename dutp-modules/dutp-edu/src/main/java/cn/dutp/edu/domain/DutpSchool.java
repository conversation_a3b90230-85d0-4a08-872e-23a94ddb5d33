package cn.dutp.edu.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * DUTP-BASE-024学校表
 * TableName dutp_school
 */
@TableName(value = "dutp_school")
@Data
public class DutpSchool extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 学校ID
     */
    @TableId(type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 上级ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 学校代码
     */
    private String schoolCode;

    /**
     * LOGO
     */
    private String logoUrl;

    /**
     * 是否是合作院校1不是2是
     */
    private Integer isPartner;

    /**
     * 0学校1院系2专业
     */
    private Integer dataType;

    /**
     * 教育类型关联dutp_subject
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long degreeId;

    /**
     * 学校排序
     */
    private Integer sort;

    /**
     * 专业描述
     */
    private String description;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}