package cn.dutp.edu.domain.dto;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.edu.domian.DutpUser;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * @author: dutp
 * @date: 2025/3/29 17:19
 */
@Data
public class DutpUserDto extends BaseEntity {
    /**
     * 导入的list
     */
    private List<DutpUser> list;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 学院名称
     */
    private String academyName;
    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户编号，学生为学生编号，教师为教师员工号
     */
    private String userNo;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户昵称
     */
    private String realName;

    /**
     *
     * 用户密码
     */
    private String password;
}
