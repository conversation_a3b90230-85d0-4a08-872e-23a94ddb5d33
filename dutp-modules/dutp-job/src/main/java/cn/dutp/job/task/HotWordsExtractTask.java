package cn.dutp.job.task;

import cn.dutp.common.ai.common.ai.domain.ChatAiRequest;
import cn.dutp.common.ai.common.ai.utils.xunfei.ChatAiUtil;
import cn.dutp.common.ai.common.ai.utils.xunfei.HttpUtil;
//import cn.dutp.job.domain.DutpAiHistory;
//import cn.dutp.job.domain.DutpAiPrompt;
//import cn.dutp.job.domain.DutpCustomerChatDetail;
//import cn.dutp.job.mapper.DutpAiPromptMapper;
//import cn.dutp.job.mapper.DutpCustomerChatDetailMapper;
//import cn.dutp.job.service.IDutpAiHistoryService;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.domain.DutpAiHistory;
import cn.dutp.domain.DutpAiPrompt;
import cn.dutp.domain.DutpCustomerChatDetail;
import cn.dutp.domain.DutpCustomerHotWord;
import cn.dutp.job.mapper.DutpAiPromptMapper;
import cn.dutp.job.mapper.DutpCustomerChatDetailMapper;
import cn.dutp.job.mapper.DutpCustomerHotWordMapper;
import cn.dutp.job.service.IDutpAiHistoryService;
import cn.dutp.job.service.DutpCustomerHotWordService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.db.handler.StringHandler;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 热词提取
 * <AUTHOR>
 */
@Slf4j
@Component("hotWordsExtractTask")
public class HotWordsExtractTask {
    @Autowired
    DutpCustomerChatDetailMapper dutpCustomerChatDetailMapper;

    @Autowired
    DutpAiPromptMapper dutpAiPromptMapper;

    @Autowired
    IDutpAiHistoryService dutpAiHistoryService;

    @Autowired
    DutpCustomerHotWordMapper dutpCustomerHotWordMapper;

    @Autowired(required=true)
    DutpCustomerHotWordService dutpCustomerHotWordService;

    // 大模型
    @Value("${ai.chatCompletionsUrl}")
    private String chatCompletionsUrl;


    // 大模型
    @Value("${ai.apiPassword}")
    private String apiPassword;

    public void hotWordsExtractTask() {
         List<String> res = new ArrayList<>();
        // 获取当天聊天内容
        // 获取今天的开始和结束时间
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(23, 59, 59);

        // 转换为 Date 对象
        Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());


        QueryWrapper<DutpCustomerChatDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpCustomerChatDetail::getUserType,2)
                .between(DutpCustomerChatDetail::getCreateTime,startDate,endDate);
        List<DutpCustomerChatDetail> list = dutpCustomerChatDetailMapper.selectList(queryWrapper);
        List<CompletableFuture<String>> futureList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
             for(DutpCustomerChatDetail detail : list) {
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> getChatResult(detail));
                if (ObjectUtil.isNotEmpty(future)) {
                     futureList.add(future);
                }
             }
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
            try {
                if(allFutures != null) {
//                    allFutures.get();
                    for (CompletableFuture<String> future : futureList) {
                        try {
                            // 获取结果并输出
                            String result = future.get();
                            if (StringUtils.isNotBlank(result)) {
                                List<String> strList = strToList(result);
                                if (StringUtils.isNotBlank(result)) {
                                    if (!CollectionUtils.isEmpty(strList)) {
                                        res.addAll(strList);
                                    }
                                }
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            continue;
                        } catch (ExecutionException e) {
                            continue;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        if (!CollectionUtils.isEmpty(res)) {
            saveResult(res.stream()
                    .distinct() // 去重
                    .collect(Collectors.toList()),list);
        }
    }

    @Transactional
    public void saveResult(List<String> list,List<DutpCustomerChatDetail> detailList) {
        // 获取所有热词数据
        QueryWrapper<DutpCustomerHotWord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpCustomerHotWord::getDelFlag,0);
        List<DutpCustomerHotWord> wordList = dutpCustomerHotWordMapper.selectList(queryWrapper);
        List<DutpCustomerHotWord> addList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wordList)) {
            DutpCustomerHotWord word = new DutpCustomerHotWord(null, null, null, null, "0");
            word.setInUse(0);
            word.setQuantity(1);
            word.setCreateBy("系统任务");
            word.setCreateTime(new Date());
            list.stream().forEach(e -> {
                word.setWord(e);
                addList.add(word);
            });
//            List<DutpCustomerHotWord> addAllList = groupByWordAndMergeQuantities(addList);
            List<DutpCustomerHotWord> addAllList = mergeWordList(addList);
            dutpCustomerHotWordService.saveBatch(addAllList);
        } else {
            processHotWords(wordList,list,detailList);
        }
    }

    private List<DutpCustomerHotWord> mergeWordList(List<DutpCustomerHotWord> addList) {
        Map<String, Integer> wordMap = new HashMap<>();

        // 遍历原始 list
        for (DutpCustomerHotWord item : addList) {
            // 如果 map 中已经包含该 word，累加数量
            wordMap.put(item.getWord(), wordMap.getOrDefault(item.getWord(), 0) + item.getQuantity());
        }

        // 将合并后的结果放回到新 list 中
        List<DutpCustomerHotWord> result = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : wordMap.entrySet()) {
            result.add(new DutpCustomerHotWord(null,entry.getKey(), entry.getValue(), 0, "0"));
//            word.setWord(entry.getKey());
//            word.setQuantity(entry.getValue());
//            result.add(word);
        }

        return result;
    }

    public void processHotWords(List<DutpCustomerHotWord> list1, List<String> list2, List<DutpCustomerChatDetail> detailList) {
        List<DutpCustomerHotWord> addList = new ArrayList<>();
        List<DutpCustomerHotWord> updateList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list2)) {
            for (String word : list2) {
                DutpCustomerHotWord matchedObject = matchFirstObject(word, list1);
                if (ObjectUtil.isNotEmpty(matchedObject)) {
                    // 如果存在，匹配入参中有多少数量并增加 quantity 并添加到 updateList
                    Long count = getQuantityByIns(word,detailList);
                    try {
                        matchedObject.setQuantity(matchedObject.getQuantity() + Math.toIntExact(count));
                    } catch (ArithmeticException e) {
                        System.out.println("数值超出 int 范围: " + count);
                    }
                    matchedObject.setUpdateBy("系统任务");
                    matchedObject.setUpdateTime(new Date());
                    updateList.add(matchedObject);
                } else {
                    // 如果不存在，新增并添加到 addList
                    DutpCustomerHotWord newHotWord = new DutpCustomerHotWord(null, null, null, null, "0");
                    Long count = getQuantityByIns(word,detailList);
                    newHotWord.setInUse(0);
                    newHotWord.setWord(word);
                    newHotWord.setQuantity(Math.toIntExact(count));
                    newHotWord.setCreateBy("系统任务");
                    newHotWord.setCreateTime(new Date());
                    addList.add(newHotWord);
                }

            }


            if (!CollectionUtils.isEmpty(addList)) {
                List<DutpCustomerHotWord> addAllList = mergeWordList(addList);
                dutpCustomerHotWordService.saveBatch(addAllList);
            }
            if (!CollectionUtils.isEmpty(updateList)) {
                dutpCustomerHotWordService.updateBatchById(updateList);
            }
        }
    }

    private Long getQuantityByIns(String word, List<DutpCustomerChatDetail> list1) {
        long count = list1.stream()
                .map(DutpCustomerChatDetail::getChatContent) // 获取每个对象的 word 属性
                .mapToLong(words -> countOccurrences(words, word)) // 计算每个 word 中 searchString 的出现次数
                .sum(); // 汇总所有的出现次数
        return count;
    }

    // 计算子字符串在字符串中出现的次数
    private static long countOccurrences(String word, String searchString) {
        if (word == null || searchString == null || searchString.isEmpty()) {
            return 0;
        }
        long count = 0;
        int index = 0;
        while ((index = word.indexOf(searchString, index)) != -1) {
            count++;
            index += searchString.length(); // 移动索引到下一个位置
        }
        return count;
    }

    private DutpCustomerHotWord matchFirstObject(String inputString, List<DutpCustomerHotWord> objectList) {
        // 遍历对象列表，寻找第一个匹配的对象
        for (DutpCustomerHotWord obj : objectList) {
            // 将对象的 word 属性分隔成数组
            List<String> wordsFromObject = Arrays.asList(obj.getWord().split(","));
            // 检查输入字符串是否与 word 属性中的任何一项匹配
            for (String word : wordsFromObject) {
                if (word.trim().equals(inputString)) {
                    return obj;  // 返回第一个匹配的对象
                }
            }
        }
        return null; // 如果没有找到匹配对象，则返回 null
    }

    /*按word分组后计算出现次数*/
    private static List<DutpCustomerHotWord> groupByWordAndMergeQuantities(List<DutpCustomerHotWord> hotWords) {
        return hotWords.stream()
                .collect(Collectors.toMap(
                        DutpCustomerHotWord::getWord,
                        item -> new DutpCustomerHotWord(item.getWordId(), item.getWord(), item.getQuantity(), item.getInUse(), item.getDelFlag()),
                        (existing, newItem) -> {
                            existing.setQuantity(existing.getQuantity() + newItem.getQuantity());
                            return existing;
                        }
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }

    private String getChatResult(DutpCustomerChatDetail e) {
        DutpAiHistory history = new DutpAiHistory();
        history.setQuestion(e.getChatContent());
        history.setPromptAbility(99);
        QueryWrapper<DutpAiPrompt> query = new QueryWrapper<>();
        query.lambda().eq(DutpAiPrompt::getAbility, 99);
        DutpAiPrompt prompt = dutpAiPromptMapper.selectOne(query);
        if (prompt != null){
            ChatAiRequest dutpAiPrompt = new ChatAiRequest();
            dutpAiPrompt.setAbility(99);
            dutpAiPrompt.setQuestion(e.getChatContent());
            dutpAiPrompt.setPrompt(prompt.getPrompt());
            String requestParam = null;
            try {
                requestParam = ChatAiUtil.buildHttpBody(dutpAiPrompt);
            } catch (Exception ex) {
                throw new RuntimeException(ex);
            }
            log.info(requestParam);
            Map<String, String> header = ChatAiUtil.buildHttpHeader(apiPassword);
            Map<String, Object> resultMap = HttpUtil.doPost2(chatCompletionsUrl, header, requestParam);

            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = null;
            try {
                jsonString = objectMapper.writeValueAsString(resultMap);
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
            history.setAnswer(jsonString);
            dutpAiHistoryService.insertDutpAiHistory(history);
            log.info("实际返回结果：\n" + jsonString);
            if (StringUtils.isNotBlank(jsonString)) {
                String json = editJson(jsonString);
                if (StringUtils.isNotBlank(json)) {
                    return json;
                }
            }
        }
        return null;
    }

    private List<String> analysisJson(String jsonStr) {
        List<String> resultList = new ArrayList<>();
        try {
            // 使用 ObjectMapper 解析 JSON
            ObjectMapper objectMapper = new ObjectMapper();

            // 将 JSON 转换为 JsonNode
            JsonNode rootNode = objectMapper.readTree(jsonStr);

            // 提取 content 字段
            String content = rootNode.path("body").path("content").asText();

            // 进一步解析内部的 JSON 字符串
            JsonNode contentNode = objectMapper.readTree(content);
            String result = contentNode.path("keywords").asText();

            // 将 result 字符串转换为 List
            resultList.addAll(Arrays.asList(result.split(",\\s*")));
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String editJson(String json){
        if (StringUtils.isNotBlank(json)) {
            // 解析原始 JSON 字符串
            JSONObject jsonObj = new JSONObject(json);

            // 提取 content 字段
            String body = jsonObj.get("body").toString();

            String content = new JSONObject(body).getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .get("content").toString();

            // 去掉多余的部分并修复 content
            String fixedContent = fixContent(content);

            String newBody = new JSONObject(body).getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .put("content",fixedContent).toString();
            JSONObject newResult = new JSONObject(fixedContent);

            // 更新修正后的 content
            String res = newResult.get("keywords").toString();
            return res;
        }
        return null;
    }

    private static List<String> strToList(String content) {
        if (StringUtils.isNotBlank(content)) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                List<String> result = objectMapper.readValue(content, List.class);
                return result;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    // 修正 content 中的非标准 JSON 格式
    private static String fixContent(String content) {
        // 判断 content 是否为标准的 JSON 格式
        if (isValidJson(content)) {
            return content;  // 如果是有效的 JSON，直接返回
        }

        // 如果 content 不是有效的 JSON，则去掉 json 和换行符，返回有效 JSON
        content = content.replaceAll("```json", "").replaceAll("```", "");

        // 最终去掉空白符、换行符等多余的字符后返回
        return content.trim();
    }

    // 判断是否为有效的 JSON 格式
    private static boolean isValidJson(String content) {
        try {
            new JSONObject(content);  // 尝试解析为 JSON 对象
            return true;  // 如果没有抛出异常，则为有效 JSON
        } catch (Exception e) {
            return false;  // 如果抛出异常，则不是有效 JSON
        }
    }
}
