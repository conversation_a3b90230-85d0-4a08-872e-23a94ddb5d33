package cn.dutp.job.task;

import cn.dutp.job.mapper.JobUserTrialApplyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 试用申请状态每日更新
 * <AUTHOR>
 */
@Slf4j
@Component("trialStatusTask")
public class TrialStatusTask {

    @Autowired
    JobUserTrialApplyMapper dtbUserTrialApplyMapper;

    public void trialStatusTask() {
        dtbUserTrialApplyMapper.updateByActivation();
    }

}
