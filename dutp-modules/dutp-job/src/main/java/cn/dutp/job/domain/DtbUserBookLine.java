package cn.dutp.job.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB_020划线对象 dtb_user_book_line
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName("dtb_user_book_line")
public class DtbUserBookLine extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lineId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    @Excel(name = "页码")
    private Long pageNumber;
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    @Excel(name = "划线文字")
    private String word;
    @Excel(name = "颜色色值#666")
    private String color;
    private String fromWordId;
    private String endWordId;
    private String lineStyle;
    private String delFlag;

    /**
     * 章节名称
     */
    @TableField(exist = false)
    private String chapterName;
}
