package cn.dutp.job.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 问题答案对象 dtb_book_question_answer
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Data
@TableName("dtb_book_question_answer")
public class DtbBookQuestionAnswer extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answerId;

    /**
     * 小题ID
     */
    @Excel(name = "小题ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userQuestionId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookQuestionId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 答题结果
     */
    @Excel(name = "答题结果")
    private String answerContent;

    /**
     * 100表示完全正确0表示完全错误
     */
    @Excel(name = "100表示完全正确0表示完全错误")
    private Integer score;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 答题人ID
     */
    @Excel(name = "答题人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 题目类型
     */
    @TableField(exist = false)
    private Integer questionType;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;
}
