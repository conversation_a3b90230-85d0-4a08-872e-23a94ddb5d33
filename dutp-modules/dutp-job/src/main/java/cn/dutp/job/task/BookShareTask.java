package cn.dutp.job.task;

import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.job.mapper.BookPurchaseCodeMapper;
import cn.dutp.job.mapper.BookShareMapper;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 购书码过期任务
 */
@Slf4j
@Component("bookShareTask")
public class BookShareTask {

    @Autowired
    BookShareMapper bookShareMapper;


    /**
     * 定时更新失效状态
     */
    public void shareLinkedStatus() {
        bookShareMapper.updateShareLinkedStatus();
    }

}
