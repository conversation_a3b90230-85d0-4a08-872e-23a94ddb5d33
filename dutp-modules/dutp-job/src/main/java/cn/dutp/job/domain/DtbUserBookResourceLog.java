package cn.dutp.job.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB_017学生观看数字教材资源记录对象 dtb_user_book_resource_log
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName("dtb_user_book_resource_log")
public class DtbUserBookResourceLog extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long logId;

    /**
     * 用户ID dutp_user表中user_id
     */
    @Excel(name = "用户ID dutp_user表中user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 资源ID
     */
    @Excel(name = "资源ID")
    private Long resourceId;

    /**
     * 文件路径
     */
    @Excel(name = "文件路径")
    private String fileUrl;

    /**
     * 持续时间（单位分钟）
     */
    @Excel(name = "持续时间", readConverterExp = "单=位分钟")
    private Long lastTime;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 进度0-100
     */
    @Excel(name = "进度0-100")
    private Integer progressRate;

    /**
     * 文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题（弃用），8课件
     */
    private Integer fileType;

}
