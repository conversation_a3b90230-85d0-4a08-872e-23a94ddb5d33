<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.job.mapper.DtbBookChapterMapper">
    <select id="queryChapter" resultType="cn.dutp.job.domain.DtbBookChapter">
        SELECT
            c.chapter_id,
            c.chapter_name,
            c.sort,
            IFNULL(c.parent_id,0) as parent_id
        FROM
            dtb_book_chapter c
        <if test="bookId != null and versionId == null">
            INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        </if>

        WHERE
            c.del_flag = '0'
            <if test="bookId != null">
                AND c.book_id = #{bookId}
            </if>
            <if test="versionId != null">
                AND c.version_id = #{versionId}
            </if>
        ORDER BY
            sort asc
    </select>
</mapper>