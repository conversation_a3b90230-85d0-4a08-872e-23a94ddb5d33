package cn.dutp.job.mapper;


import cn.dutp.job.domain.DtbBookQuestionAnswer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 问题答案Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Repository
public interface DtbBookQuestionAnswerMapper extends BaseMapper<DtbBookQuestionAnswer> {

    List<DtbBookQuestionAnswer> queryTestQuestionListByChapterId(Long chapterId);
}
