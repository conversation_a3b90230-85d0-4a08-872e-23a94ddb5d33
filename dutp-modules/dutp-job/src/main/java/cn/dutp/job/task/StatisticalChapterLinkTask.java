package cn.dutp.job.task;

import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.domain.DtbBook;
import cn.dutp.job.domain.DtbBookChapter;
import cn.dutp.job.domain.DtbBookChapterContent;
import cn.dutp.job.domain.DtbBookChapterLink;
import cn.dutp.job.mapper.BookMapper;
import cn.dutp.job.mapper.DtbBookChapterLinkMapper;
import cn.dutp.job.mapper.DtbBookChapterMapper;
import cn.dutp.job.service.IDtbBookChapterLinkService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.dutp.common.core.constant.ChapterContentConstants.BOOK_CHAPTER_CONTENT;


/**
 * 统计章节外部链接任务
 */
@Component("statisticalChapterLinkTask")
@Slf4j
public class StatisticalChapterLinkTask {

    @Autowired
    private BookMapper bookMapper;

    @Autowired
    private DtbBookChapterMapper chapterMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DtbBookChapterLinkMapper linkMapper;

    @Autowired
    private IDtbBookChapterLinkService linkService;


    /**
     * 校验url是否合法
     *
     * @param url
     * @return
     */
    public static boolean validateUrlIsLegal(String url) {
        if (url == null) return true;
        if (url.contains("http") || url.contains("https")) {
            return url.contains("dutp");
        }
        return true;
    }

    /**
     * 每天0点50秒执行
     */
    public void statisticalChapterLink(Boolean isScanOldData) {
        log.info("统计章节链接任务开始执行");
        // 查找所有教材
        List<DtbBook> bookList = bookMapper.selectList(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookId, DtbBook::getLastVersionId,
                        DtbBook::getCurrentVersionId,
                        DtbBook::getCurrentStepId));
        if (ObjectUtil.isEmpty(isScanOldData)) {
            isScanOldData = false;
        }
        // 根据教材查找所有章节
        for (DtbBook book : bookList) {
            if (book.getCurrentVersionId().longValue() != book.getLastVersionId().longValue()) {
                log.info("修正教材，教材id:{},版本id:{},当前版本id:{},当前步骤id:{}", book.getBookId(), book.getLastVersionId(), book.getCurrentVersionId(), book.getCurrentStepId());
                // 在修正中
                // 统计章节内数据
                statisticalChapterInnerData(book, isScanOldData);
            } else if (book.getCurrentStepId().intValue() < 16) {
                log.info("正在编写，教材id:{},版本id:{},当前版本id:{},当前步骤id:{}", book.getBookId(), book.getLastVersionId(), book.getCurrentVersionId(), book.getCurrentStepId());
                // 正在编写 统计章节内数据
                statisticalChapterInnerData(book, isScanOldData);
            }

        }
        log.info("统计章节链接任务执行结束");
    }

    /**
     * 统计章节内部数据
     *
     * @param book
     */
    private void statisticalChapterInnerData(DtbBook book, Boolean isScanOldData) {
        Long bookId = book.getBookId();
        LambdaQueryWrapper<DtbBookChapter> lambdaQueryWrapper = new LambdaQueryWrapper<DtbBookChapter>()
                .select(DtbBookChapter::getChapterId)
                .eq(DtbBookChapter::getBookId, bookId);
        if (!isScanOldData) {
            lambdaQueryWrapper.eq(DtbBookChapter::getVersionId, book.getLastVersionId());
        }

        List<DtbBookChapter> chapterList = chapterMapper.selectList(lambdaQueryWrapper);

        List<DtbBookChapterLink> oldLinkList = linkMapper.selectList(new LambdaQueryWrapper<DtbBookChapterLink>()
                .select(DtbBookChapterLink::getLinkId, DtbBookChapterLink::getUrl)
                .eq(DtbBookChapterLink::getBookId, bookId));
        List<DtbBookChapterLink> scanLinkList = new ArrayList<>();
        for (DtbBookChapter chapter : chapterList) {
            Long chapterId = chapter.getChapterId();
            // 查询章节内容
            Query query = new Query(Criteria.where("chapterId").is(chapterId));
            DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
            if (ObjectUtil.isEmpty(chapterContent)) {
                continue;
            }
            String contentJson = chapterContent.getContent();
            if (ObjectUtil.isEmpty(contentJson)) {
                continue;
            }
            if (ObjectUtil.isNotEmpty(contentJson)) {
                JSONObject jsonObject = JSONUtil.parseObj(contentJson);
                JSONArray pageList = jsonObject.getJSONArray("content");
                if (ObjectUtil.isEmpty(pageList)) {
                    continue;
                }
                analysisNode(pageList, chapterId, bookId, 1, scanLinkList);
            }
        }


        List<DtbBookChapterLink> willAddLinkList = new ArrayList<>();
        if (ObjectUtil.isEmpty(oldLinkList)) {
            willAddLinkList = scanLinkList;
        } else {
            Set<String> oldLinkSet = oldLinkList.stream().map(DtbBookChapterLink::getUrl).collect(Collectors.toSet());
            for (DtbBookChapterLink chapterLink : scanLinkList) {
                if (!oldLinkSet.contains(chapterLink.getUrl())) {
                    willAddLinkList.add(chapterLink);
                    oldLinkSet.add(chapterLink.getUrl());
                }
            }
        }
        if (ObjectUtil.isNotEmpty(willAddLinkList)) {
            linkService.saveBatch(willAddLinkList);
        }
    }

    /**
     * 分析节点
     *
     * @param nodeList
     */
    private void analysisNode(JSONArray nodeList, Long chapterId, Long bookId, Integer pageNumber, List<DtbBookChapterLink> linkList) {
        for (Object parentNode : nodeList) {
            JSONObject node = (JSONObject) parentNode;
            String type = node.getStr("type");

            if ("page".equals(type)) {
                JSONObject attrs = node.getJSONObject("attrs");
                pageNumber = attrs.getInt("pageNumber");
            }

            if ("imageLayout".equals(type) || "imageIcon".equals(type) || "image".equals(type) || "formulaInLine".equals(type)) {
                // 图片和公式
                JSONObject attrs = node.getJSONObject("attrs");
                String url = attrs.getStr("linkAddress");
                generateOutLinkObj(chapterId, bookId, pageNumber, linkList, url);
            } else if ("links".equals(type)) {
                // 链接
                JSONObject attrs = node.getJSONObject("attrs");
                String href = attrs.getStr("href");
                generateOutLinkObj(chapterId, bookId, pageNumber, linkList, href);
            } else if ("resourceCover".equals(type)) {
                // 资源封面组件
                JSONObject attrs = node.getJSONObject("attrs");
                String url = attrs.getStr("url");
                generateOutLinkObj(chapterId, bookId, pageNumber, linkList, url);
            } else if ("bubbleInline".equals(type)) {
                // 气泡
                JSONObject attrs = node.getJSONObject("attrs");
                String url = attrs.getStr("bubbleUrl");
                generateOutLinkObj(chapterId, bookId, pageNumber, linkList, url);
            }

            JSONArray childrenNodeList = node.getJSONArray("content");
            if (ObjectUtil.isNotEmpty(childrenNodeList)) {
                analysisNode(childrenNodeList, chapterId, bookId, pageNumber, linkList);
            }
        }
    }

    /**
     * 生成链接对象
     *
     * @param chapterId
     * @param bookId
     * @param pageNumber
     * @param linkList
     * @param url
     */
    private static void generateOutLinkObj(Long chapterId, Long bookId, Integer pageNumber, List<DtbBookChapterLink> linkList, String url) {
        if (ObjectUtil.isNotEmpty(url) && !validateUrlIsLegal(url)) {
            DtbBookChapterLink chapterLink = new DtbBookChapterLink();
            chapterLink.setUrl(url);
            chapterLink.setChapterId(chapterId);
            chapterLink.setBookId(bookId);
            chapterLink.setPageNumber(pageNumber);
            chapterLink.setCreateTime(new Date());
            linkList.add(chapterLink);
        }
    }

}
