package cn.dutp.job.task;


import cn.dutp.common.core.utils.TreeUtil;
import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.job.domain.DtbBookChapter;
import cn.dutp.job.domain.DtbBookChapterContent;
import cn.dutp.job.domain.DtbBookChapterData;
import cn.dutp.job.domain.DutpTask;
import cn.dutp.job.mapper.DtbBookChapterDataMapper;
import cn.dutp.job.mapper.DtbBookChapterMapper;
import cn.dutp.job.mapper.DutpTaskMapper;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.dutp.common.core.constant.ChapterContentConstants.BOOK_CHAPTER_CONTENT;


/**
 * 教材统计任务
 */

@Component("statisticalBookDataTask")
@Slf4j
public class StatisticalBookDataTask {

    @Autowired
    private DtbBookChapterMapper chapterMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DutpTaskMapper taskMapper;

    @Autowired
    private DtbBookChapterDataMapper chapterDataMapper;

    /**
     * 每天0点30秒执行
     */
    public void statisticalChapterData() {
        log.info("统计教材统计任务开始执行");
        List<DutpTask> taskList = taskMapper.selectList(new LambdaQueryWrapper<DutpTask>()
                .select(DutpTask::getDataId, DutpTask::getTaskId)
                .ne(DutpTask::getTaskState, 2)
                .eq(DutpTask::getTaskType, 11));
        if (ObjectUtil.isNotEmpty(taskList)) {
            for (DutpTask task : taskList) {
                try {
                    Long bookId = task.getDataId();
                    if (ObjectUtil.isEmpty(bookId)) {
                        continue;
                    }

                    task.setTaskState(1);
                    task.setStartTime(new Date());
                    taskMapper.updateById(task);
                    DtbBookChapter dtbBookChapter = new DtbBookChapter();
                    dtbBookChapter.setBookId(bookId);
                    List<DtbBookChapter> chapterList = chapterMapper.queryChapter(dtbBookChapter);
                    if (ObjectUtil.isEmpty(chapterList)) {
                        continue;
                    }
                    chapterList = TreeUtil.makeTree(chapterList,
                            x -> x.getParentId() == 0L, (x, y) -> x.getChapterId().equals(y.getParentId()), DtbBookChapter::setChildrenOfTypeChapter);

                    for (DtbBookChapter chapter : chapterList) {
                        List<DtbBookChapter> newChapterList = new ArrayList<>();

                        collectChapterChildrenNode(chapter, newChapterList);

                        if (ObjectUtil.isEmpty(newChapterList)) {
                            continue;
                        }

                        // 查询数据统计
                        DtbBookChapterData bookChapterData = getDtbBookChapterData(bookId, chapter);
                        for (DtbBookChapter bookChapter : newChapterList) {
                            // 查询章节内容
                            Long chapterId = bookChapter.getChapterId();
                            Query query = new Query(Criteria.where("chapterId").is(chapterId));
                            DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                            if (ObjectUtil.isEmpty(chapterContent)) {
                                continue;
                            }
                            String contentJson = chapterContent.getContent();
                            if (ObjectUtil.isEmpty(contentJson)) {
                                continue;
                            }

                            // 查询章节内容
                            JSONObject jsonObject = JSONUtil.parseObj(contentJson);
                            JSONArray pageList = jsonObject.getJSONArray("content");
                            if (ObjectUtil.isEmpty(pageList)) {
                                continue;
                            }
                            analysisNode(bookChapterData, pageList);
                        }

                        // 更新数据统计
                        if (ObjectUtil.isEmpty(bookChapterData.getDataId())) {
                            chapterDataMapper.insert(bookChapterData);
                        } else {
                            chapterDataMapper.updateById(bookChapterData);
                        }
                    }

                    task.setTaskState(2);
                } catch (Exception e) {
                    task.setTaskState(3);
                    log.info("统计教材统计任务执行失败：", e);
                }
                task.setEndTime(new Date());
                taskMapper.updateById(task);

            }
        }
        log.info("统计教材统计任务执行结束");
    }

    /**
     * 收集章节节点的叶子节点
     *
     * @param chapter
     * @param newChapterList
     */
    private void collectChapterChildrenNode(DtbBookChapter chapter, List<DtbBookChapter> newChapterList) {
        List<DtbBookChapter> children = chapter.getChildrenOfTypeChapter();
        if (ObjectUtil.isEmpty(children)) {
            newChapterList.add(chapter);
            return;
        }
        for (DtbBookChapter child : children) {
            collectChapterChildrenNode(child, newChapterList);
        }
    }

    /**
     * 获取统计数据
     *
     * @param bookId
     * @param chapter
     * @return
     */
    private DtbBookChapterData getDtbBookChapterData(Long bookId, DtbBookChapter chapter) {
        DtbBookChapterData bookChapterData = chapterDataMapper.queryChapterDataByChapterId(chapter.getChapterId());
        if (ObjectUtil.isEmpty(bookChapterData)) {
            bookChapterData = new DtbBookChapterData();
            bookChapterData.setBookId(bookId);
            bookChapterData.setChapterId(chapter.getChapterId());
            bookChapterData.setWordQuantity(0L);
            bookChapterData.setImageQuanity(0L);
            bookChapterData.setBubbleQuantity(0L);
            bookChapterData.setOutsideLinkQuantity(0L);
            bookChapterData.setFormulaQuantity(0L);
            bookChapterData.setThreeDimenQuantity(0L);
            bookChapterData.setAvrQuantity(0L);
            bookChapterData.setSimulationQuantity(0L);
            bookChapterData.setQuestionQuantity(0L);
            bookChapterData.setGameQuantity(0L);
            bookChapterData.setFootnoteQuantity(0L);
            bookChapterData.setResouceQuantity(0L);
            bookChapterData.setAudioQuantity(0L);
            bookChapterData.setVideoQuantity(0L);
            bookChapterData.setExtQuantity(0L);
            bookChapterData.setCodeQuantity(0L);
            bookChapterData.setAudioTotalDuration(0L);
            bookChapterData.setInteractionVoteQuantity(0L);
            bookChapterData.setVideoTotalDuration(0L);
            bookChapterData.setInteractionWordCloudQuantity(0L);
            bookChapterData.setInteractionDiscussQuantity(0L);
            bookChapterData.setInteractionImageWaterfallQuantity(0L);
            bookChapterData.setInsideLinkQuantity(0L);
            bookChapterData.setStudyTotalTime(0L);
            bookChapterData.setStudyVideoTime(0L);
            bookChapterData.setStudyNoteQuantity(0L);
            bookChapterData.setStudyHighlightQuantity(0L);
            bookChapterData.setStudyDiscussQuantity(0L);
            bookChapterData.setStudyUserQuantity(0L);
            bookChapterData.setStudyQuestionQuantity(0L);
            bookChapterData.setStudyQuestionRate(BigDecimal.ZERO);

        } else {
            bookChapterData.setWordQuantity(0L);
            bookChapterData.setImageQuanity(0L);
            bookChapterData.setBubbleQuantity(0L);
            bookChapterData.setOutsideLinkQuantity(0L);
            bookChapterData.setFormulaQuantity(0L);
            bookChapterData.setThreeDimenQuantity(0L);
            bookChapterData.setAvrQuantity(0L);
            bookChapterData.setSimulationQuantity(0L);
            bookChapterData.setQuestionQuantity(0L);
            bookChapterData.setGameQuantity(0L);
            bookChapterData.setFootnoteQuantity(0L);
            bookChapterData.setResouceQuantity(0L);
            bookChapterData.setAudioQuantity(0L);
            bookChapterData.setVideoQuantity(0L);
            bookChapterData.setExtQuantity(0L);
            bookChapterData.setCodeQuantity(0L);
            bookChapterData.setAudioTotalDuration(0L);
            bookChapterData.setInteractionVoteQuantity(0L);
            bookChapterData.setVideoTotalDuration(0L);
            bookChapterData.setInteractionWordCloudQuantity(0L);
            bookChapterData.setInteractionDiscussQuantity(0L);
            bookChapterData.setInteractionImageWaterfallQuantity(0L);
            bookChapterData.setInsideLinkQuantity(0L);
        }

        return bookChapterData;
    }

    /**
     * 分析节点
     *
     * @param bookChapterData
     * @param nodeList
     */
    private void analysisNode(DtbBookChapterData bookChapterData, JSONArray nodeList) {
        for (Object parentNode : nodeList) {
            JSONObject node = (JSONObject) parentNode;
            String type = node.getStr("type");

            if ("text".equals(type)) {
                // 文字
                String text = node.getStr("text");
                if (ObjectUtil.isNotEmpty(text)) {
                    Long wordQuantity = bookChapterData.getWordQuantity();
                    if (ObjectUtil.isEmpty(wordQuantity)) {
                        wordQuantity = 0L;
                    }
                    wordQuantity += text.length();
                    bookChapterData.setWordQuantity(wordQuantity);
                }
            } else if ("imageLayout".equals(type) || "imageInLine".equals(type)
                    || "imageIcon".equals(type) || "imageGallery".equals(type) || "surround".equals(type)) {
                // 图片
                Long imageQuanity = bookChapterData.getImageQuanity();
                if (ObjectUtil.isEmpty(imageQuanity)) {
                    imageQuanity = 0L;
                }
                if ("imageGallery".equals(type)) {
                    // 画廊
                    JSONObject attrs = node.getJSONObject("attrs");
                    // 兼容以前导入久数据
                    if (ObjectUtil.isNotEmpty(attrs)) {
                        JSONArray imgList = attrs.getJSONArray("imgList");
                        if (ObjectUtil.isNotEmpty(imgList)) {
                            imageQuanity += imgList.size();
                        }
                    }
                } else if ("surround".equals(type)) {
                    JSONObject attrs = node.getJSONObject("attrs");
                    String url = attrs.getStr("imageUrl");
                    if (ObjectUtil.isNotEmpty(url)) {
                        imageQuanity += 1L;
                    }
                } else {
                    // 非画廊
                    imageQuanity += 1L;
                }
                bookChapterData.setImageQuanity(imageQuanity);
            } else if ("bubbleInline".equals(type)) {
                // 气泡
                Long bubbleQuantity = bookChapterData.getBubbleQuantity();
                if (ObjectUtil.isEmpty(bubbleQuantity)) {
                    bubbleQuantity = 0L;
                }
                bubbleQuantity += 1L;
                bookChapterData.setBubbleQuantity(bubbleQuantity);
            } else if ("links".equals(type)) {
                // 链接
                JSONObject attrs = node.getJSONObject("attrs");
                String linkType = attrs.getStr("type");
                if ("websiteLink".equals(linkType)) {
                    // 外部链接
                    Long outsideLinkQuantity = bookChapterData.getOutsideLinkQuantity();
                    if (ObjectUtil.isEmpty(outsideLinkQuantity)) {
                        outsideLinkQuantity = 0L;
                    }
                    outsideLinkQuantity += 1L;
                    bookChapterData.setOutsideLinkQuantity(outsideLinkQuantity);
                } else {
                    // 内部链接
                    Long insideLinkQuantity = bookChapterData.getInsideLinkQuantity();
                    if (ObjectUtil.isEmpty(insideLinkQuantity)) {
                        insideLinkQuantity = 0L;
                    }
                    insideLinkQuantity += 1L;
                    bookChapterData.setInsideLinkQuantity(insideLinkQuantity);
                }
            }

            // TODO 先默认这个为公式
            else if ("image".equals(type) || "formulaInLine".equals(type)) {
                // 公式
                Long formulaQuantity = bookChapterData.getFormulaQuantity();
                if (ObjectUtil.isEmpty(formulaQuantity)) {
                    formulaQuantity = 0L;
                }
                formulaQuantity += 1L;
                bookChapterData.setFormulaQuantity(formulaQuantity);
            } else if ("resourceCover".equals(type)) {
                // 资源封面组件
                JSONObject attrs = node.getJSONObject("attrs");
                Integer rcType = attrs.getInt("rcType");
                if (rcType == 0) {
                    // 3D
                    Long threeDimenQuantity = bookChapterData.getThreeDimenQuantity();
                    if (ObjectUtil.isEmpty(threeDimenQuantity)) {
                        threeDimenQuantity = 0L;
                    }
                    threeDimenQuantity += 1L;
                    bookChapterData.setThreeDimenQuantity(threeDimenQuantity);
                } else if (rcType == 1 || rcType == 2) {
                    // AR/VR
                    Long avrQuantity = bookChapterData.getAvrQuantity();
                    if (ObjectUtil.isEmpty(avrQuantity)) {
                        avrQuantity = 0L;
                    }
                    avrQuantity += 1L;
                    bookChapterData.setAvrQuantity(avrQuantity);
                } else if (rcType == 3) {
                    // 仿真
                    Long simulationQuantity = bookChapterData.getSimulationQuantity();
                    if (ObjectUtil.isEmpty(simulationQuantity)) {
                        simulationQuantity = 0L;
                    }
                    simulationQuantity += 1L;
                    bookChapterData.setSimulationQuantity(simulationQuantity);
                } else if (rcType == 4) {
                    // 游戏
                    Long gameQuantity = bookChapterData.getGameQuantity();
                    if (ObjectUtil.isEmpty(gameQuantity)) {
                        gameQuantity = 0L;
                    }
                    gameQuantity += 1L;
                    bookChapterData.setGameQuantity(gameQuantity);
                } else if (rcType == 5) {
                    // 教学资源
                    Long resouceQuantity = bookChapterData.getResouceQuantity();
                    if (ObjectUtil.isEmpty(resouceQuantity)) {
                        resouceQuantity = 0L;
                    }
                    resouceQuantity += 1L;
                    bookChapterData.setResouceQuantity(resouceQuantity);
                }
                // else if (rcType == 6) {
                //     // 扩展阅读
                //     Long extQuantity = bookChapterData.getExtQuantity();
                //     if (ObjectUtil.isEmpty(extQuantity)) {
                //         extQuantity = 0l;
                //     }
                //     extQuantity += 1l;
                //     bookChapterData.setExtQuantity(extQuantity);
                // }
                else if (rcType == 7) {
                    // TODO 实训

                }
            } else if ("questions".equals(type)) {
                // 试题
                Long questionQuantity = bookChapterData.getQuestionQuantity();
                if (ObjectUtil.isEmpty(questionQuantity)) {
                    questionQuantity = 0L;
                }
                questionQuantity += 1L;
                bookChapterData.setQuestionQuantity(questionQuantity);
            }
            // TODO 二期 脚注


            else if ("extendedReading".equals(type)) {
                // 扩展阅读
                Long extQuantity = bookChapterData.getExtQuantity();
                if (ObjectUtil.isEmpty(extQuantity)) {
                    extQuantity = 0L;
                }
                extQuantity += 1L;
                bookChapterData.setExtQuantity(extQuantity);
            } else if ("audio".equals(type) || "audioHanlin".equals(type)) {
                // 音频
                // 数量
                Long audioQuantity = bookChapterData.getAudioQuantity();
                if (ObjectUtil.isEmpty(audioQuantity)) {
                    audioQuantity = 0L;
                }
                audioQuantity += 1L;
                bookChapterData.setAudioQuantity(audioQuantity);
                // 总时长
                Long audioTotalDuration = bookChapterData.getAudioTotalDuration();
                if (ObjectUtil.isEmpty(audioTotalDuration)) {
                    audioTotalDuration = 0L;
                }
                JSONObject attrs = node.getJSONObject("attrs");
                Integer duration = attrs.getInt("duration");
                audioTotalDuration += duration;
                bookChapterData.setAudioTotalDuration(audioTotalDuration);
            } else if ("video".equals(type)) {
                // 视频
                // 数量
                Long videoQuantity = bookChapterData.getVideoQuantity();
                if (ObjectUtil.isEmpty(videoQuantity)) {
                    videoQuantity = 0L;
                }
                videoQuantity += 1L;
                bookChapterData.setVideoQuantity(videoQuantity);
                // 总时长
                Long videoTotalDuration = bookChapterData.getVideoTotalDuration();
                if (ObjectUtil.isEmpty(videoTotalDuration)) {
                    videoTotalDuration = 0L;
                }
                JSONObject attrs = node.getJSONObject("attrs");
                Integer duration = attrs.getInt("duration");
                videoTotalDuration += duration;
                bookChapterData.setVideoTotalDuration(videoTotalDuration);
            } else if ("codeBlock".equals(type)) {
                // 代码块
                Long codeQuantity = bookChapterData.getCodeQuantity();
                if (ObjectUtil.isEmpty(codeQuantity)) {
                    codeQuantity = 0L;
                }
                codeQuantity += 1;
                bookChapterData.setCodeQuantity(codeQuantity);
            }
            // TODO 二期 互动


            JSONArray childrenNodeList = node.getJSONArray("content");
            if (ObjectUtil.isNotEmpty(childrenNodeList)) {
                analysisNode(bookChapterData, childrenNodeList);
            }
        }
    }

}
