spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      discovery:
        server-addr: 172.17.15.80:8848
        namespace: test
      config:
        # 命名空间
        server-addr: 172.17.15.80:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: test
