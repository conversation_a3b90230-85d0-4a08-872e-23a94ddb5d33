package cn.dutp.qrcode.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.qrcode.domain.DutpDiskBook;
import cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto;
import cn.dutp.qrcode.domain.vo.DutpDiskBookVO;
import cn.dutp.qrcode.service.IDutpDiskBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 智典云盘统计
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/statistics")
public class DutpDiskStatisticsController extends BaseController {

    @Autowired
    private IDutpDiskBookService dutpDiskBookService;

    /**
     * 查询智典云盘统计列表
     */
    @GetMapping("/list")
    public TableDataInfo getStatisticsList(DutpDiskStatisticsDto dto) {
        startPage();
        return getDataTable(dutpDiskBookService.getStatisticsList(dto));
    }

    /**
     * 查询智典云盘统计汇总数据
     */
    @GetMapping("/getStatisticsSummary")
    public AjaxResult getStatisticsSummary() {
        return success(dutpDiskBookService.getStatisticsSummary());
    }

    /**
     * 查询智典云盘明细列表
     */
    @Log(title = "智典云盘", businessType = BusinessType.EXPORT)
    @GetMapping("/detailList")
    public TableDataInfo getStatisticsDetailList(DutpDiskStatisticsDto dto) {
        return getDataTable(dutpDiskBookService.getStatisticsDetailList(dto));
    }

    /**
     * 导出列表
     */
    @Log(title = "智典云盘扫码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpDiskStatisticsDto dto) {
        List<DutpDiskBookVO> list =  dutpDiskBookService.getStatisticsList(dto);
        ExcelUtil<DutpDiskBookVO> util = new ExcelUtil<DutpDiskBookVO>(DutpDiskBookVO.class);
        if (dto.getSelectType() == 1) {
            util.hideColumn(new String[] {"imageUrl", "qrcodeName", "createTime","ISSN"});
        } else if (dto.getSelectType() == 2) {
            util.hideColumn(new String[] {"imageUrl", "createTime","ISSN","qrCodeCount"});
        }

        util.exportExcel(response, list, "智典云盘扫码");
    }

    /**
     * 导出明细列表
     */
    @Log(title = "智典云盘扫码明细", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDetail")
    public void exportDetail(HttpServletResponse response,DutpDiskStatisticsDto dto) {
        List<DutpDiskBookVO> list =  dutpDiskBookService.getStatisticsDetailList(dto);
        ExcelUtil<DutpDiskBookVO> excelUtil = new ExcelUtil<>(DutpDiskBookVO.class);
        excelUtil.hideColumn(new String[] { "qrCodeCount", "scanningCount"});
        excelUtil.exportExcel(response, list, "智典云盘扫码明细");
    }
}
