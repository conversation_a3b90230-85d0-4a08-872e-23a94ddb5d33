package cn.dutp.qrcode.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.qrcode.domain.DutpDiskUserTemplate;
import cn.dutp.qrcode.service.IDutpDiskUserTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 用户的二维码模板Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/userTemplate")
public class DutpDiskUserTemplateController extends BaseController
{
    @Autowired
    private IDutpDiskUserTemplateService dutpDiskUserTemplateService;

    /**
     * 查询用户的二维码模板列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        startPage();
        List<DutpDiskUserTemplate> list = dutpDiskUserTemplateService.selectDutpDiskUserTemplateList(dutpDiskUserTemplate);
        return getDataTable(list);
    }

    /**
     * 导出用户的二维码模板列表
     */
    @RequiresPermissions("qrcode:userTemplate:export")
    @Log(title = "用户的二维码模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        List<DutpDiskUserTemplate> list = dutpDiskUserTemplateService.selectDutpDiskUserTemplateList(dutpDiskUserTemplate);
        ExcelUtil<DutpDiskUserTemplate> util = new ExcelUtil<DutpDiskUserTemplate>(DutpDiskUserTemplate.class);
        util.exportExcel(response, list, "用户的二维码模板数据");
    }

    /**
     * 获取用户的二维码模板详细信息
     */
    @RequiresPermissions("qrcode:userTemplate:query")
    @GetMapping(value = "/{userTemplateId}")
    public AjaxResult getInfo(@PathVariable("userTemplateId") Long userTemplateId)
    {
        return success(dutpDiskUserTemplateService.selectDutpDiskUserTemplateByUserTemplateId(userTemplateId));
    }

    /**
     * 新增用户的二维码模板
     */
    @RequiresPermissions("qrcode:userTemplate:add")
    @Log(title = "新增用户的二维码模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        return toAjax(dutpDiskUserTemplateService.insertDutpDiskUserTemplate(dutpDiskUserTemplate));
    }

    /**
     * 修改用户的二维码模板
     */
    @RequiresPermissions("qrcode:userTemplate:edit")
    @Log(title = "修改用户的二维码模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        return toAjax(dutpDiskUserTemplateService.updateDutpDiskUserTemplate(dutpDiskUserTemplate));
    }

    /**
     * 删除用户的二维码模板
     */
    @RequiresPermissions("qrcode:userTemplate:remove")
    @Log(title = "删除用户的二维码模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userTemplateIds}")
    public AjaxResult remove(@PathVariable Long[] userTemplateIds)
    {
        return toAjax(dutpDiskUserTemplateService.deleteDutpDiskUserTemplateByUserTemplateIds(Arrays.asList(userTemplateIds)));
    }

    /**
     * 设置为默认模板
     */
    @Log(title = "设为默认", businessType = BusinessType.UPDATE)
    @PutMapping("isDefault")
    public AjaxResult isDefault(@RequestBody DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        return toAjax(dutpDiskUserTemplateService.changeDefault(dutpDiskUserTemplate));
    }

}
