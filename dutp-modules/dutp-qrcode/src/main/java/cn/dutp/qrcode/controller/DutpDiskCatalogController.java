package cn.dutp.qrcode.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.qrcode.domain.DutpDiskCatalog;
import cn.dutp.qrcode.service.IDutpDiskCatalogService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 智典云盘文件夹Controller
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/catalog")
public class DutpDiskCatalogController extends BaseController
{
    @Autowired
    private IDutpDiskCatalogService dutpDiskCatalogService;

/**
 * 查询智典云盘文件夹列表
 */
@GetMapping("/list")
    public TableDataInfo list(DutpDiskCatalog dutpDiskCatalog)
    {
        startPage();
        List<DutpDiskCatalog> list = dutpDiskCatalogService.selectDutpDiskCatalogList(dutpDiskCatalog);
        return getDataTable(list);
    }


    @GetMapping("/allList")
    public TableDataInfo allList(DutpDiskCatalog dutpDiskCatalog)
    {

        List<DutpDiskCatalog> list = dutpDiskCatalogService.selectDutpDiskCatalogList(dutpDiskCatalog);
        return getDataTable(list);
    }

    /**
     * 导出智典云盘文件夹列表
     */
    @RequiresPermissions("qrcode:catalog:export")
    @Log(title = "智典云盘文件夹", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpDiskCatalog dutpDiskCatalog)
    {
        List<DutpDiskCatalog> list = dutpDiskCatalogService.selectDutpDiskCatalogList(dutpDiskCatalog);
        ExcelUtil<DutpDiskCatalog> util = new ExcelUtil<DutpDiskCatalog>(DutpDiskCatalog.class);
        util.exportExcel(response, list, "智典云盘文件夹数据");
    }

    /**
     * 获取智典云盘文件夹详细信息
     */
    @RequiresPermissions("qrcode:catalog:query")
    @GetMapping(value = "/{catalogId}")
    public AjaxResult getInfo(@PathVariable("catalogId") Long catalogId)
    {
        return success(dutpDiskCatalogService.selectDutpDiskCatalogByCatalogId(catalogId));
    }

    /**
     * 新增智典云盘文件夹
     */
    @RequiresPermissions("qrcode:catalog:add")
    @Log(title = "智典云盘文件夹", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpDiskCatalog dutpDiskCatalog)
    {
        return success(dutpDiskCatalogService.insertDutpDiskCatalog(dutpDiskCatalog));
    }

    /**
     * 修改智典云盘文件夹
     */
    @RequiresPermissions("qrcode:catalog:edit")
    @Log(title = "智典云盘文件夹", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpDiskCatalog dutpDiskCatalog)
    {
        return toAjax(dutpDiskCatalogService.updateDutpDiskCatalog(dutpDiskCatalog));
    }

    /**
     * 删除智典云盘文件夹
     */
    @RequiresPermissions("qrcode:catalog:remove")
    @Log(title = "智典云盘文件夹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{catalogIds}")
    public AjaxResult remove(@PathVariable Long[] catalogIds)
    {
        return toAjax(dutpDiskCatalogService.deleteDutpDiskCatalogByCatalogIds(Arrays.asList(catalogIds)));
    }
}
