package cn.dutp.qrcode.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.qrcode.service.IDutpDiskQrcodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/openApi")
public class DutpDiskOpenApiController extends BaseController {

    @Autowired
    private IDutpDiskQrcodeService dutpDiskQrcodeService;


    /**
     * 获取智典云盘资源详细信息
     */
    @RequiresPermissions("qrcode:qrcode:query")
    @GetMapping(value = "/{qrcodeId}")
    public AjaxResult getInfo(@PathVariable("qrcodeId") Long qrcodeId)
    {
        return success(dutpDiskQrcodeService.selectDutpDiskQrcodeByQrcodeId(qrcodeId));
    }
    }
