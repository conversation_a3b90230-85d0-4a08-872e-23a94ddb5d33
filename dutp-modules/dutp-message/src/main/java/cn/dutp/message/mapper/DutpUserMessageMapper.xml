<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.message.mapper.DutpUserMessageMapper">
    
    <resultMap type="DutpUserMessage" id="DutpUserMessageResult">
        <result property="messageId"    column="message_id"    />
        <result property="content"    column="content"    />
        <result property="title"    column="title"    />
        <result property="fromUserId"    column="from_user_id"    />
        <result property="toUserId"    column="to_user_id"    />
        <result property="messageType"    column="message_type"    />
        <result property="fromUserType"    column="from_user_type"    />
        <result property="toUserType"    column="to_user_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpUserMessageVo">
        select message_id, content, title, from_user_id, to_user_id, message_type, from_user_type, to_user_type, del_flag, create_by, create_time, update_by, update_time from dutp_user_message
    </sql>

    <select id="selectDutpUserMessageList" parameterType="DutpUserMessage" resultMap="DutpUserMessageResult">
        <include refid="selectDutpUserMessageVo"/>
        <where>  
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="fromUserId != null "> and from_user_id = #{fromUserId}</if>
            <if test="toUserId != null "> and to_user_id = #{toUserId}</if>
            <if test="messageType != null "> and message_type = #{messageType}</if>
            <if test="fromUserType != null "> and from_user_type = #{fromUserType}</if>
            <if test="toUserType != null "> and to_user_type = #{toUserType}</if>
        </where>
    </select>
    
    <select id="selectDutpUserMessageByMessageId" parameterType="Long" resultMap="DutpUserMessageResult">
        <include refid="selectDutpUserMessageVo"/>
        where message_id = #{messageId}
    </select>
    <select id="pushBookList" resultType="cn.dutp.message.domain.DutpUserMessage">
        SELECT
            m.book_id,
            m.message_type,
            m.create_time,
            s.school_id,
            s.school_name,
            u.nick_name,
            u.user_name,
            u.user_type
        FROM
            dutp_user_message m
                INNER JOIN dutp_user u ON m.to_user_id = u.user_id
                left JOIN dutp_school s ON s.school_id = u.school_id
        WHERE
            m.message_type = 3
          AND m.del_flag = '0'
          AND m.book_id = #{bookId}
        <if test="userName != null and userName != ''">AND u.user_name = #{userName}</if>
        <if test="nickName != null and nickName != ''">AND u.nick_name = #{nickName}</if>
        <if test="userType != null">AND u.user_type = #{userType}</if>
        <if test="schoolId != null">AND s.school_id = #{schoolId}</if>
    </select>

    <insert id="insertDutpUserMessage" parameterType="DutpUserMessage" useGeneratedKeys="true" keyProperty="messageId">
        insert into dutp_user_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="content != null">content,</if>
            <if test="title != null">title,</if>
            <if test="fromUserId != null">from_user_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="messageType != null">message_type,</if>
            <if test="fromUserType != null">from_user_type,</if>
            <if test="toUserType != null">to_user_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="content != null">#{content},</if>
            <if test="title != null">#{title},</if>
            <if test="fromUserId != null">#{fromUserId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="messageType != null">#{messageType},</if>
            <if test="fromUserType != null">#{fromUserType},</if>
            <if test="toUserType != null">#{toUserType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpUserMessage" parameterType="DutpUserMessage">
        update dutp_user_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">content = #{content},</if>
            <if test="title != null">title = #{title},</if>
            <if test="fromUserId != null">from_user_id = #{fromUserId},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="messageType != null">message_type = #{messageType},</if>
            <if test="fromUserType != null">from_user_type = #{fromUserType},</if>
            <if test="toUserType != null">to_user_type = #{toUserType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where message_id = #{messageId}
    </update>

    <delete id="deleteDutpUserMessageByMessageId" parameterType="Long">
        delete from dutp_user_message where message_id = #{messageId}
    </delete>

    <delete id="deleteDutpUserMessageByMessageIds" parameterType="String">
        delete from dutp_user_message where message_id in 
        <foreach item="messageId" collection="array" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </delete>
</mapper>