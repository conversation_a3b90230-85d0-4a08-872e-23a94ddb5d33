package cn.dutp.message.mapper;

import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookGroup;
import cn.dutp.message.domain.DtbUserBookFeedback;
import cn.dutp.message.domain.vo.DtbUserBookFeedbackVo;
import cn.dutp.system.api.domain.SysUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 读者反馈/纠错Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Repository
public interface DtbUserBookFeedbackMapper extends BaseMapper<DtbUserBookFeedback> {
    /**
     * 通过用户名查询用户
     *
     * @param dtbUserBookFeedback 用户名
     * @return 用户对象信息
     */
    public List<DtbUserBookFeedbackVo> selectDtbUserBookFeedbackList(DtbUserBookFeedback dtbUserBookFeedback);

    List<DtbUserBookFeedback> listForAdmin(DtbUserBookFeedback dtbUserBookFeedback);

    DtbUserBookFeedback adminQueryInfo(Long feedBackId);

    List<DtbUserBookFeedback> checkHasFeedback(Long userId);

    @Select("SELECT book_name FROM dtb_book WHERE book_id = #{bookId} and del_flag = '0'")
    String queryBookName(Long bookId);

    @Select("SELECT book_name,book_no FROM dtb_book WHERE book_id = #{bookId} and del_flag = '0'")
    DtbBook queryBook(Long bookId);

    @Select("SELECT group_id,role_type,user_id FROM dtb_book_group WHERE book_id = #{bookId} and role_type in ('1','2','5','6') and del_flag = '0'")
    List<DtbBookGroup> selectBookGroup(@Param("bookId") Long bookId);

}
