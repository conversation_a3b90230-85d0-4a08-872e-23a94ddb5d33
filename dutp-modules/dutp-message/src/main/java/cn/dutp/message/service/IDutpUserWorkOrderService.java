package cn.dutp.message.service;

import java.util.List;

import cn.dutp.domain.DutpUserWorkOrder;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 反馈工单Service接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface IDutpUserWorkOrderService extends IService<DutpUserWorkOrder>
{
    /**
     * 查询反馈工单
     *
     * @param ticketId 反馈工单主键
     * @return 反馈工单
     */
    public DutpUserWorkOrder selectDutpUserWorkOrderByTicketId(Long ticketId);

    /**
     * 查询反馈工单列表
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 反馈工单集合
     */
    public List<DutpUserWorkOrder> selectDutpUserWorkOrderList(DutpUserWorkOrder dutpUserWorkOrder);

    /**
     * 新增反馈工单
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 结果
     */
    public boolean insertDutpUserWorkOrder(DutpUserWorkOrder dutpUserWorkOrder);

    /**
     * 修改反馈工单
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 结果
     */
    public boolean updateDutpUserWorkOrder(DutpUserWorkOrder dutpUserWorkOrder);

    /**
     * 批量删除反馈工单
     *
     * @param ticketIds 需要删除的反馈工单主键集合
     * @return 结果
     */
    public boolean deleteDutpUserWorkOrderByTicketIds(List<Long> ticketIds);

}
