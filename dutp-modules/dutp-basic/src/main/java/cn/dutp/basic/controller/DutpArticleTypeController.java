package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpArticleType;
import cn.dutp.basic.service.IDutpArticleTypeService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 文章类型Controller
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@RestController
@RequestMapping("/articleType")
public class DutpArticleTypeController extends BaseController
{
    @Autowired
    private IDutpArticleTypeService dutpArticleTypeService;

/**
 * 查询文章类型列表
 */
@RequiresPermissions("basic:articleType:list")
@GetMapping("/list")
    public TableDataInfo list(DutpArticleType dutpArticleType)
    {
        List<DutpArticleType> list = dutpArticleTypeService.selectDutpArticleTypeList(dutpArticleType);
        return getDataTable(list);
    }

    /**
     * 查询学生教师端文章类型列表
     */
    @GetMapping("/listEducation")
    public TableDataInfo listEducation(DutpArticleType dutpArticleType)
    {
        List<DutpArticleType> list = dutpArticleTypeService.selectDutpArticleTypeList(dutpArticleType);
        return getDataTable(list);
    }

    /**
     * 导出文章类型列表
     */
    @RequiresPermissions("basic:articleType:export")
    @Log(title = "导出文章类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpArticleType dutpArticleType)
    {
        List<DutpArticleType> list = dutpArticleTypeService.selectDutpArticleTypeList(dutpArticleType);
        ExcelUtil<DutpArticleType> util = new ExcelUtil<DutpArticleType>(DutpArticleType.class);
        util.exportExcel(response, list, "文章类型数据");
    }

    /**
     * 获取文章类型详细信息
     */
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
    {
        return success(dutpArticleTypeService.selectDutpArticleTypeByTypeId(typeId));
    }

    /**
     * 新增文章类型
     */
    @RequiresPermissions("basic:articleType:add")
    @Log(title = "新增文章类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpArticleType dutpArticleType)
    {
        return toAjax(dutpArticleTypeService.insertDutpArticleType(dutpArticleType));
    }

    /**
     * 修改文章类型
     */
    @RequiresPermissions("basic:articleType:edit")
    @Log(title = "修改文章类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpArticleType dutpArticleType)
    {
        return toAjax(dutpArticleTypeService.updateDutpArticleType(dutpArticleType));
    }

    /**
     * 删除文章类型
     */
    @RequiresPermissions("basic:articleType:remove")
    @Log(title = "删除文章类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{typeIds}")
    public AjaxResult remove(@PathVariable Long[] typeIds)
    {
        return toAjax(dutpArticleTypeService.deleteDutpArticleTypeByTypeIds(Arrays.asList(typeIds)));
    }
}
