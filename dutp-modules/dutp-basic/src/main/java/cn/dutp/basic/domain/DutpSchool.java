package cn.dutp.basic.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * DUTP-BASE-002学校表
 * </p>
 *
 * <AUTHOR> @since 2024-10-24
 */
@Data
@TableName("dutp_school")
public class DutpSchool extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 学校ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    @TableField(exist = false)
    private String schoolIdStr;

    @TableField(exist = false)
    private String academyIdStr;


    /**
     * 0学校1学院2院系
     */
    private Integer dataType;

    /**
     * 上级ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    @Excel(name = "所属院系")
    @TableField(exist = false)
    private String subjectName;

    /**
     * 上级名称
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 学校名称
     */
    @Excel(name = "专业名称")
    private String schoolName;

    /**
     * 学校代码
     */
    @Excel(name = "专业编码")
    private String schoolCode;

    /**
     * LOGO
     */
    private String logoUrl;

    /**
     * 是否是合作院校1不是2是
     */
    private Integer isPartner;

    /**
     * 教育类型
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long degreeId;

    /**
     * 学校排序
     */
    private Integer sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 教育类型
     */
    @TableField(exist = false)
    private String degreeName;

    @TableField(exist = false)
    private Integer bookCount;
    @Excel(name = "专业描述")
    private String description;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long bookId;

    /**
     * 默认折扣
     */
    private BigDecimal defaultDiscount;

    /**
     * 发票内容
     */
    private Integer applyType;

    /**
     * 发票人邮箱
     */
    private String userEmail;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 抬头类型
     */
    private Integer titleType;

    /**
     * 抬头名称
     */
    private String titleName;

    /**
     * 税号
     */
    private String taxNo;

    /**
     * 注册地址
     */
    private String registAddress;

    /**
     * 注册电话
     */
    private String registTel;

    /**
     * 开户银行
     */
    private String accountBank;

    /**
     * 银行账号
     */
    private String accountNo;


    @Override
    public String toString() {
        return "DutpSchool{" +
                "schoolId=" + schoolId +
                ", schoolName='" + schoolName + '\'' +
                ", schoolCode='" + schoolCode + '\'' +
                ", logoUrl='" + logoUrl + '\'' +
                ", isPartner=" + isPartner +
                ", degreeId=" + degreeId +
                ", sort=" + sort +
                ", delFlag='" + delFlag + '\'' +
                ", degreeName='" + degreeName + '\'' +
                ", bookCount=" + bookCount +
                '}';
    }
}
