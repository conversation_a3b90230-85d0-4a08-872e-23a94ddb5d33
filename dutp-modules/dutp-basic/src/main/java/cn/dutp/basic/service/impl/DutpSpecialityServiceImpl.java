package cn.dutp.basic.service.impl;

import java.util.List;

import cn.dutp.basic.domain.DutpSpeciality;
import cn.dutp.basic.mapper.DutpSpecialityMapper;
import cn.dutp.basic.service.IDutpSpecialityService;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DUTP-BASE-007专业Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Service
public class DutpSpecialityServiceImpl extends ServiceImpl<DutpSpecialityMapper, DutpSpeciality> implements IDutpSpecialityService
{
    @Autowired
    private DutpSpecialityMapper dutpSpecialityMapper;

    /**
     * 查询DUTP-BASE-007专业
     *
     * @param specialityId DUTP-BASE-007专业主键
     * @return DUTP-BASE-007专业
     */
    @Override
    public DutpSpeciality selectDutpSpecialityBySpecialityId(Long specialityId)
    {
        return this.getById(specialityId);
    }

    /**
     * 查询DUTP-BASE-007专业列表
     *
     * @param dutpSpeciality DUTP-BASE-007专业
     * @return DUTP-BASE-007专业
     */
    @Override
    public List<DutpSpeciality> selectDutpSpecialityList(DutpSpeciality dutpSpeciality)
    {
        LambdaQueryWrapper<DutpSpeciality> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpSpeciality.getSpecialityName())) {
                lambdaQueryWrapper.like(DutpSpeciality::getSpecialityName
                ,dutpSpeciality.getSpecialityName());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-BASE-007专业
     *
     * @param dutpSpeciality DUTP-BASE-007专业
     * @return 结果
     */
    @Override
    public boolean insertDutpSpeciality(DutpSpeciality dutpSpeciality)
    {
        return this.save(dutpSpeciality);
    }

    /**
     * 修改DUTP-BASE-007专业
     *
     * @param dutpSpeciality DUTP-BASE-007专业
     * @return 结果
     */
    @Override
    public boolean updateDutpSpeciality(DutpSpeciality dutpSpeciality)
    {
        return this.updateById(dutpSpeciality);
    }

    /**
     * 批量删除DUTP-BASE-007专业
     *
     * @param specialityIds 需要删除的DUTP-BASE-007专业主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpSpecialityBySpecialityIds(List<Long> specialityIds)
    {
        return this.removeByIds(specialityIds);
    }

    @Override
    public AjaxResult listForSelect(DutpSpeciality dutpSpeciality) {
        QueryWrapper<DutpSpeciality> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dutpSpeciality.getSpecialityName())) {
            queryWrapper.lambda().like(DutpSpeciality::getSpecialityName,dutpSpeciality.getSpecialityName());
        }
        if(ObjectUtil.isNotEmpty(dutpSpeciality.getSpecialityId())) {
            queryWrapper.lambda().eq(DutpSpeciality::getSpecialityId,dutpSpeciality.getSpecialityId());
        }
        return AjaxResult.success(dutpSpecialityMapper.selectList(queryWrapper));
    }

}
