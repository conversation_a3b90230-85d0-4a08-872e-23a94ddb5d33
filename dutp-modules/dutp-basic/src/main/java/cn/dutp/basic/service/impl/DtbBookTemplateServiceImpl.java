package cn.dutp.basic.service.impl;

import java.util.List;

import cn.dutp.basic.domain.DtbBookTemplate;
import cn.dutp.basic.mapper.DtbBookTemplateMapper;
import cn.dutp.basic.service.IDtbBookTemplateService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DUTP-DTB-029教材模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Service
public class DtbBookTemplateServiceImpl extends ServiceImpl<DtbBookTemplateMapper, DtbBookTemplate> implements IDtbBookTemplateService
{
    @Autowired
    private DtbBookTemplateMapper dtbBookTemplateMapper;

    /**
     * 查询DUTP-DTB-029教材模板
     *
     * @param templateId DUTP-DTB-029教材模板主键
     * @return DUTP-DTB-029教材模板
     */
    @Override
    public DtbBookTemplate selectDtbBookTemplateByTemplateId(Long templateId)
    {
        return this.getById(templateId);
    }

    /**
     * 查询DUTP-DTB-029教材模板列表
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return DUTP-DTB-029教材模板
     */
    @Override
    public List<DtbBookTemplate> selectDtbBookTemplateList(DtbBookTemplate dtbBookTemplate)
    {
        LambdaQueryWrapper<DtbBookTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getTemplateName())) {
                lambdaQueryWrapper.like(DtbBookTemplate::getTemplateName
                ,dtbBookTemplate.getTemplateName());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getCover())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getCover
                ,dtbBookTemplate.getCover());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getTemplateTypeId())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getTemplateTypeId
                ,dtbBookTemplate.getTemplateTypeId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getSpecialityId())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getSpecialityId
                ,dtbBookTemplate.getSpecialityId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getMainColor())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getMainColor
                ,dtbBookTemplate.getMainColor());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getFirstLevelTitle())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getFirstLevelTitle
                ,dtbBookTemplate.getFirstLevelTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getSecondLevelTitle())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getSecondLevelTitle
                ,dtbBookTemplate.getSecondLevelTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getThirdLevelTitle())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getThirdLevelTitle
                ,dtbBookTemplate.getThirdLevelTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getForthLevelTitle())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getForthLevelTitle
                ,dtbBookTemplate.getForthLevelTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getFifthLevelTitle())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getFifthLevelTitle
                ,dtbBookTemplate.getFifthLevelTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getSixthLevelTitle())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getSixthLevelTitle
                ,dtbBookTemplate.getSixthLevelTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getSeventhLevelTitle())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getSeventhLevelTitle
                ,dtbBookTemplate.getSeventhLevelTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getMainText())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getMainText
                ,dtbBookTemplate.getMainText());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getChapterHeader())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getChapterHeader
                ,dtbBookTemplate.getChapterHeader());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getSectionHeader())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getSectionHeader
                ,dtbBookTemplate.getSectionHeader());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getTableFill())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getTableFill
                ,dtbBookTemplate.getTableFill());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getResourceFill())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getResourceFill
                ,dtbBookTemplate.getResourceFill());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTemplate.getSort())) {
                lambdaQueryWrapper.eq(DtbBookTemplate::getSort
                ,dtbBookTemplate.getSort());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    @Override
    public boolean insertDtbBookTemplate(DtbBookTemplate dtbBookTemplate)
    {
        return this.save(dtbBookTemplate);
    }

    /**
     * 修改DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    @Override
    public boolean updateDtbBookTemplate(DtbBookTemplate dtbBookTemplate)
    {
        return this.updateById(dtbBookTemplate);
    }

    /**
     * 批量删除DUTP-DTB-029教材模板
     *
     * @param templateIds 需要删除的DUTP-DTB-029教材模板主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTemplateByTemplateIds(List<Long> templateIds)
    {
        return this.removeByIds(templateIds);
    }

}
