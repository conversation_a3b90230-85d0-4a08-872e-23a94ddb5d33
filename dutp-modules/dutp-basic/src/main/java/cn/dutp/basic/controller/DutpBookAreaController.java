package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpBookArea;
import cn.dutp.basic.domain.DutpFriendLink;
import cn.dutp.basic.service.IDutpBookAreaService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 专区类型
 *
 * @author: dutp
 * @date: 2024/10/28
 */
@RestController
@RequestMapping("/bookArea")
public class DutpBookAreaController extends BaseController {

    @Autowired
    private IDutpBookAreaService bookAreaService;

    /**
     * 查询类型专区列表
     * @param dutpBookArea 类型专区
     * @return 结果
     */
    @RequiresPermissions("basic:bookArea:list")
    @GetMapping("/list")
    public TableDataInfo list(DutpBookArea dutpBookArea){
        startPage();
        List<DutpBookArea> list = bookAreaService.areaList(dutpBookArea);
        return getDataTable(list);
    }

    /**
     * 查询类型专区列表 下拉框使用
     * @return 结果
     */
    @GetMapping("/listNotPage")
    public AjaxResult listNotPage(){
        List<DutpBookArea> list = bookAreaService.list(new LambdaQueryWrapper<DutpBookArea>()
                .select(DutpBookArea::getAreaId,DutpBookArea::getAreaName));
        return success(list);
    }

    /**
     * 学生教师端查询类型专区列表
     * @param dutpBookArea 类型专区
     * @return 结果
     */
    @GetMapping("/listEducation")
    public AjaxResult listEducation(DutpBookArea dutpBookArea){
        return success(bookAreaService.listEducation(dutpBookArea));
    }

    /**
     * 获取专区类型详情
     * @param areaId 专区类型id
     * @return 结果
     */
    @RequiresPermissions("basic:bookArea:query")
    @GetMapping(value = "/getInfo/{areaId}")
    public AjaxResult getInfo(@PathVariable("areaId") Long areaId){
        return success(bookAreaService.getAreaByAreaId(areaId));
    }

    /**
     * 导入专区类型
     * @param list 列表
     * @return 结果
     */
    @RequiresPermissions("basic:bookArea:import")
    @Log(title = "导入专区类型", businessType = BusinessType.EXPORT)
    @PostMapping("/import")
    public AjaxResult imports(@RequestBody List<DutpBookArea> list){
        return success(bookAreaService.imports(list));
    }

    /**
     * 新增专区类型
     * @param dutpBookArea 专区类型
     * @return 结果
     */
    @RequiresPermissions("basic:bookArea:add")
    @Log(title = "添加专区类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult addBookArea(@RequestBody DutpBookArea dutpBookArea){
        return toAjax(bookAreaService.addArea(dutpBookArea));
    }

    /**
     * 修改专区类型
     * @param dutpBookArea 专区类型
     * @return 结果
     */
    @RequiresPermissions("basic:bookArea:edit")
    @Log(title = "修改专区类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult editBookArea(@RequestBody DutpBookArea dutpBookArea){
        return toAjax(bookAreaService.editArea(dutpBookArea));
    }

    /**
     * 删除专区类型
     * @param areaId 专区类型id
     * @return 结果
     */
    @RequiresPermissions("basic:bookArea:remove")
    @Log(title = "删除专区类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaId}")
    public AjaxResult remove(@PathVariable("areaId") Long areaId){
        return toAjax(bookAreaService.deleteArea(areaId));
    }
}
