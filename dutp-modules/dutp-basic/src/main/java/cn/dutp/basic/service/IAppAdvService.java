package cn.dutp.basic.service;

import cn.dutp.basic.domain.AppAdv;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 移动端开屏图Service接口
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface IAppAdvService extends IService<AppAdv>
{
    /**
     * 查询移动端开屏图
     *
     * @param advId 移动端开屏图主键
     * @return 移动端开屏图
     */
    public AppAdv selectAppAdvByAdvId(Long advId);

    /**
     * 查询移动端开屏图列表
     *
     * @param appAdv 移动端开屏图
     * @return 移动端开屏图集合
     */
    public List<AppAdv> selectAppAdvList(AppAdv appAdv);

    /**
     * 新增移动端开屏图
     *
     * @param appAdv 移动端开屏图
     * @return 结果
     */
    public boolean insertAppAdv(AppAdv appAdv);

    /**
     * 修改移动端开屏图
     *
     * @param appAdv 移动端开屏图
     * @return 结果
     */
    public boolean updateAppAdv(AppAdv appAdv);

    /**
     * 批量删除移动端开屏图
     *
     * @param advIds 需要删除的移动端开屏图主键集合
     * @return 结果
     */
    public boolean deleteAppAdvByAdvIds(List<Long> advIds);

}
