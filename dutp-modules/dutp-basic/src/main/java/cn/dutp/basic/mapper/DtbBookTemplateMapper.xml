<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.basic.mapper.DtbBookTemplateMapper">
    
    <resultMap type="cn.dutp.basic.domain.DtbBookTemplate" id="DtbBookTemplateResult">
        <result property="templateId"    column="template_id"    />
        <result property="templateName"    column="template_name"    />
        <result property="cover"    column="cover"    />
        <result property="templateTypeId"    column="template_type_id"    />
        <result property="specialityId"    column="speciality_id"    />
        <result property="mainColor"    column="main_color"    />
        <result property="firstLevelTitle"    column="first_level_title"    />
        <result property="secondLevelTitle"    column="second_level_title"    />
        <result property="thirdLevelTitle"    column="third_level_title"    />
        <result property="forthLevelTitle"    column="forth_level_title"    />
        <result property="fifthLevelTitle"    column="fifth_level_title"    />
        <result property="sixthLevelTitle"    column="sixth_level_title"    />
        <result property="seventhLevelTitle"    column="seventh_level_title"    />
        <result property="mainText"    column="main_text"    />
        <result property="chapterHeader"    column="chapter_header"    />
        <result property="sectionHeader"    column="section_header"    />
        <result property="tableFill"    column="table_fill"    />
        <result property="resourceFill"    column="resource_fill"    />
        <result property="status"    column="status"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbBookTemplateVo">
        select template_id, template_name, cover, template_type_id, speciality_id, main_color, first_level_title, second_level_title, third_level_title, forth_level_title, fifth_level_title, sixth_level_title, seventh_level_title, main_text, chapter_header, section_header, table_fill, resource_fill, status, sort, create_by, create_time, update_by, update_time, del_flag from dtb_book_template
    </sql>

    <select id="selectDtbBookTemplateList" parameterType="DtbBookTemplate" resultMap="DtbBookTemplateResult">
        <include refid="selectDtbBookTemplateVo"/>
        <where>  
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="cover != null  and cover != ''"> and cover = #{cover}</if>
            <if test="templateTypeId != null "> and template_type_id = #{templateTypeId}</if>
            <if test="specialityId != null "> and speciality_id = #{specialityId}</if>
            <if test="mainColor != null  and mainColor != ''"> and main_color = #{mainColor}</if>
            <if test="firstLevelTitle != null  and firstLevelTitle != ''"> and first_level_title = #{firstLevelTitle}</if>
            <if test="secondLevelTitle != null  and secondLevelTitle != ''"> and second_level_title = #{secondLevelTitle}</if>
            <if test="thirdLevelTitle != null  and thirdLevelTitle != ''"> and third_level_title = #{thirdLevelTitle}</if>
            <if test="forthLevelTitle != null  and forthLevelTitle != ''"> and forth_level_title = #{forthLevelTitle}</if>
            <if test="fifthLevelTitle != null  and fifthLevelTitle != ''"> and fifth_level_title = #{fifthLevelTitle}</if>
            <if test="sixthLevelTitle != null  and sixthLevelTitle != ''"> and sixth_level_title = #{sixthLevelTitle}</if>
            <if test="seventhLevelTitle != null  and seventhLevelTitle != ''"> and seventh_level_title = #{seventhLevelTitle}</if>
            <if test="mainText != null  and mainText != ''"> and main_text = #{mainText}</if>
            <if test="chapterHeader != null  and chapterHeader != ''"> and chapter_header = #{chapterHeader}</if>
            <if test="sectionHeader != null  and sectionHeader != ''"> and section_header = #{sectionHeader}</if>
            <if test="tableFill != null  and tableFill != ''"> and table_fill = #{tableFill}</if>
            <if test="resourceFill != null  and resourceFill != ''"> and resource_fill = #{resourceFill}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDtbBookTemplateByTemplateId" parameterType="Long" resultMap="DtbBookTemplateResult">
        <include refid="selectDtbBookTemplateVo"/>
        where template_id = #{templateId}
    </select>

    <insert id="insertDtbBookTemplate" parameterType="DtbBookTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into dtb_book_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null">template_name,</if>
            <if test="cover != null">cover,</if>
            <if test="templateTypeId != null">template_type_id,</if>
            <if test="specialityId != null">speciality_id,</if>
            <if test="mainColor != null">main_color,</if>
            <if test="firstLevelTitle != null">first_level_title,</if>
            <if test="secondLevelTitle != null">second_level_title,</if>
            <if test="thirdLevelTitle != null">third_level_title,</if>
            <if test="forthLevelTitle != null">forth_level_title,</if>
            <if test="fifthLevelTitle != null">fifth_level_title,</if>
            <if test="sixthLevelTitle != null">sixth_level_title,</if>
            <if test="seventhLevelTitle != null">seventh_level_title,</if>
            <if test="mainText != null">main_text,</if>
            <if test="chapterHeader != null">chapter_header,</if>
            <if test="sectionHeader != null">section_header,</if>
            <if test="tableFill != null">table_fill,</if>
            <if test="resourceFill != null">resource_fill,</if>
            <if test="status != null">status,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null">#{templateName},</if>
            <if test="cover != null">#{cover},</if>
            <if test="templateTypeId != null">#{templateTypeId},</if>
            <if test="specialityId != null">#{specialityId},</if>
            <if test="mainColor != null">#{mainColor},</if>
            <if test="firstLevelTitle != null">#{firstLevelTitle},</if>
            <if test="secondLevelTitle != null">#{secondLevelTitle},</if>
            <if test="thirdLevelTitle != null">#{thirdLevelTitle},</if>
            <if test="forthLevelTitle != null">#{forthLevelTitle},</if>
            <if test="fifthLevelTitle != null">#{fifthLevelTitle},</if>
            <if test="sixthLevelTitle != null">#{sixthLevelTitle},</if>
            <if test="seventhLevelTitle != null">#{seventhLevelTitle},</if>
            <if test="mainText != null">#{mainText},</if>
            <if test="chapterHeader != null">#{chapterHeader},</if>
            <if test="sectionHeader != null">#{sectionHeader},</if>
            <if test="tableFill != null">#{tableFill},</if>
            <if test="resourceFill != null">#{resourceFill},</if>
            <if test="status != null">#{status},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbBookTemplate" parameterType="DtbBookTemplate">
        update dtb_book_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="cover != null">cover = #{cover},</if>
            <if test="templateTypeId != null">template_type_id = #{templateTypeId},</if>
            <if test="specialityId != null">speciality_id = #{specialityId},</if>
            <if test="mainColor != null">main_color = #{mainColor},</if>
            <if test="firstLevelTitle != null">first_level_title = #{firstLevelTitle},</if>
            <if test="secondLevelTitle != null">second_level_title = #{secondLevelTitle},</if>
            <if test="thirdLevelTitle != null">third_level_title = #{thirdLevelTitle},</if>
            <if test="forthLevelTitle != null">forth_level_title = #{forthLevelTitle},</if>
            <if test="fifthLevelTitle != null">fifth_level_title = #{fifthLevelTitle},</if>
            <if test="sixthLevelTitle != null">sixth_level_title = #{sixthLevelTitle},</if>
            <if test="seventhLevelTitle != null">seventh_level_title = #{seventhLevelTitle},</if>
            <if test="mainText != null">main_text = #{mainText},</if>
            <if test="chapterHeader != null">chapter_header = #{chapterHeader},</if>
            <if test="sectionHeader != null">section_header = #{sectionHeader},</if>
            <if test="tableFill != null">table_fill = #{tableFill},</if>
            <if test="resourceFill != null">resource_fill = #{resourceFill},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deleteDtbBookTemplateByTemplateId" parameterType="Long">
        delete from dtb_book_template where template_id = #{templateId}
    </delete>

    <delete id="deleteDtbBookTemplateByTemplateIds" parameterType="String">
        delete from dtb_book_template where template_id in 
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
</mapper>