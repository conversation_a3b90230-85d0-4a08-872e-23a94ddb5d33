package cn.dutp.basic.service;

import java.util.List;
import cn.dutp.basic.domain.DutpDegree;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教育类型Service接口
 *
 * <AUTHOR>
 * &#064;date  2024-10-28
 */
public interface IDutpDegreeService extends IService<DutpDegree>
{
    /**
     * 查询教育类型
     *
     * @param degreeId 教育类型主键
     * @return 教育类型
     */
    public DutpDegree selectDutpDegreeByDegreeId(Long degreeId);

    /**
     * 查询教育类型列表
     *
     * @param dutpDegree 教育类型
     * @return 教育类型集合
     */
    public List<DutpDegree> selectDutpDegreeList(DutpDegree dutpDegree);

    /**
     * 新增教育类型
     *
     * @param dutpDegree 教育类型
     * @return 结果
     */
    public boolean insertDutpDegree(DutpDegree dutpDegree);

    /**
     * 修改教育类型
     *
     * @param dutpDegree 教育类型
     * @return 结果
     */
    public boolean updateDutpDegree(DutpDegree dutpDegree);

    /**
     * 批量删除教育类型
     *
     * @param degreeIds 需要删除的教育类型主键集合
     * @return 结果
     */
    public boolean deleteDutpDegreeByDegreeIds(List<Long> degreeIds);

    /**
     * 导入用户数据
     *
     * @param degrees 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importUser(List<DutpDegree> degrees, Boolean isUpdateSupport, String operName);

    /**
     * 此函数用于另一个功能的下拉列表
     * @param dutpDegree 查询数据
     * @return 结果
     */
    AjaxResult listForSelect(DutpDegree dutpDegree);

}
