package cn.dutp.basic.service;

import java.util.List;
import cn.dutp.basic.domain.DutpArticle;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 文章管理Service接口
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
public interface IDutpArticleService extends IService<DutpArticle>
{
    /**
     * 查询文章管理
     *
     * @param id 文章管理主键
     * @return 文章管理
     */
    public DutpArticle selectDutpArticleById(Long id);

    /**
     * 查询文章管理列表
     *
     * @param dutpArticle 文章管理
     * @return 文章管理集合
     */
    public List<DutpArticle> selectDutpArticleList(DutpArticle dutpArticle);

    /**
     * 新增文章管理
     *
     * @param dutpArticle 文章管理
     * @return 结果
     */
    public boolean insertDutpArticle(DutpArticle dutpArticle);

    /**
     * 修改文章管理
     *
     * @param dutpArticle 文章管理
     * @return 结果
     */
    public boolean updateDutpArticle(DutpArticle dutpArticle);

    /**
     * 批量删除文章管理
     *
     * @param ids 需要删除的文章管理主键集合
     * @return 结果
     */
    public boolean deleteDutpArticleByIds(List<Long> ids);

}
