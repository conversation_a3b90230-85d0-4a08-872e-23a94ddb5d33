package cn.dutp.basic.mapper;

import cn.dutp.basic.domain.DutpBookArea;
import cn.dutp.basic.domain.DutpSchool;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 类型专区mapper
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Repository
public interface DutpBookAreaMapper extends BaseMapper<DutpBookArea> {
    @Update("UPDATE dutp_book_area set sort = #{sort},area_name = #{areaName} where area_id = #{areaId} ")
    boolean updateArea(DutpBookArea dutpBookArea);

    /**
     * 学生教师端查询类型专区列表
     * @param dutpBookArea 专区类型
     * @return 结果
     */
    List<DutpBookArea> selectDutpBookAreaListData (DutpBookArea dutpBookArea);
    /**
     * 学生教师端获取主编信息
     * @param bookId 教材ID
     * @return 结果
     */
    List<DutpBookArea> getEditorInChief (Long[] bookId);
}
