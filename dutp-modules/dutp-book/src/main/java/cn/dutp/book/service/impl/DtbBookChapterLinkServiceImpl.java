package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookChapterLink;
import cn.dutp.book.mapper.DtbBookChapterLinkMapper;
import cn.dutp.book.service.IDtbBookChapterLinkService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 外部链接扫描Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class DtbBookChapterLinkServiceImpl extends ServiceImpl<DtbBookChapterLinkMapper, DtbBookChapterLink> implements IDtbBookChapterLinkService {
    @Autowired
    private DtbBookChapterLinkMapper dtbBookChapterLinkMapper;

    /**
     * 查询外部链接扫描
     *
     * @param linkId 外部链接扫描主键
     * @return 外部链接扫描
     */
    @Override
    public DtbBookChapterLink selectDtbBookChapterLinkByLinkId(Long linkId) {
        return this.getById(linkId);
    }

    /**
     * 查询外部链接扫描列表
     *
     * @param dtbBookChapterLink 外部链接扫描
     * @return 外部链接扫描
     */
    @Override
    public List<DtbBookChapterLink> selectDtbBookChapterLinkList(DtbBookChapterLink dtbBookChapterLink) {
        return dtbBookChapterLinkMapper.selectDtbBookChapterLinkList(dtbBookChapterLink);
    }

    /**
     * 新增外部链接扫描
     *
     * @param dtbBookChapterLink 外部链接扫描
     * @return 结果
     */
    @Override
    public boolean insertDtbBookChapterLink(DtbBookChapterLink dtbBookChapterLink) {
        return this.save(dtbBookChapterLink);
    }

    /**
     * 修改外部链接扫描
     *
     * @param dtbBookChapterLink 外部链接扫描
     * @return 结果
     */
    @Override
    public boolean updateDtbBookChapterLink(DtbBookChapterLink dtbBookChapterLink) {
        return this.updateById(dtbBookChapterLink);
    }

    /**
     * 批量删除外部链接扫描
     *
     * @param linkIds 需要删除的外部链接扫描主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookChapterLinkByLinkIds(List<Long> linkIds) {
        return this.removeByIds(linkIds);
    }

}
