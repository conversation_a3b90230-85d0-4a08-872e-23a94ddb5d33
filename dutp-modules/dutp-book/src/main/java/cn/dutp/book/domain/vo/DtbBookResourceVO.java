package cn.dutp.book.domain.vo;

import cn.dutp.book.domain.DtbUserResource;
import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Data
public class DtbBookResourceVO
{
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long chapterResourceId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long bookId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long chapterId;
        private String fileName;
        private String fileUrl;
        private Integer fileType;
        private BigDecimal fileSize;

}
