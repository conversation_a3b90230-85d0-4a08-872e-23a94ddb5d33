package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.vo.BookQuestionFoloderVo;
import cn.dutp.book.domain.vo.QuestionFolderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookQuestionFolder;
import cn.dutp.book.service.IDtbBookQuestionFolderService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 题库目录Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bookQuestionFolder")
public class DtbBookQuestionFolderController extends BaseController
{
    @Autowired
    private IDtbBookQuestionFolderService dtbBookQuestionFolderService;

    /**
     * 查询题库目录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookQuestionFolder dtbBookQuestionFolder)
    {
        startPage();
        List<DtbBookQuestionFolder> list = dtbBookQuestionFolderService.selectDtbBookQuestionFolderList(dtbBookQuestionFolder);
        return getDataTable(list);
    }

    /**
     * 导出题库目录列表
     */
    @RequiresPermissions("book:folder:export")
    @Log(title = "导出题库目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookQuestionFolder dtbBookQuestionFolder)
    {
        List<DtbBookQuestionFolder> list = dtbBookQuestionFolderService.selectDtbBookQuestionFolderList(dtbBookQuestionFolder);
        ExcelUtil<DtbBookQuestionFolder> util = new ExcelUtil<DtbBookQuestionFolder>(DtbBookQuestionFolder.class);
        util.exportExcel(response, list, "题库目录数据");
    }

    /**
     * 获取题库目录详细信息
     */
    @RequiresPermissions("book:folder:query")
    @GetMapping(value = "/{folderId}")
    public AjaxResult getInfo(@PathVariable("folderId") Long folderId)
    {
        return success(dtbBookQuestionFolderService.selectDtbBookQuestionFolderByFolderId(folderId));
    }

    /**
     * 新增题库目录
     */
    @RequiresPermissions("book:folder:query")
    @Log(title = "新增题库目录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookQuestionFolder dtbBookQuestionFolder)
    {
        return toAjax(dtbBookQuestionFolderService.insertDtbBookQuestionFolder(dtbBookQuestionFolder));
    }

    /**
     * 修改题库目录
     */
    @RequiresPermissions("book:folder:query")
    @Log(title = "修改题库目录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookQuestionFolder dtbBookQuestionFolder)
    {
        return toAjax(dtbBookQuestionFolderService.updateDtbBookQuestionFolder(dtbBookQuestionFolder));
    }

    /**
     * 删除题库目录
     */
    @RequiresPermissions("book:folder:query")
    @Log(title = "删除题库目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{folderIds}")
    public AjaxResult remove(@PathVariable Long[] folderIds)
    {
        return toAjax(dtbBookQuestionFolderService.deleteDtbBookQuestionFolderByFolderIds(Arrays.asList(folderIds)));
    }

       /**
     * 获取个人全部的教材题库和个人题库，组合成一个列表返回
     */
    @GetMapping("/personalQuestionLibrary")
    public AjaxResult getPersonalQuestionLibrary()
    {
        Long userId = SecurityUtils.getUserId();
        List<BookQuestionFoloderVo> personalQuestionLibrary = dtbBookQuestionFolderService.getPersonalQuestionLibrary(userId);
        return AjaxResult.success(personalQuestionLibrary);
    }
}
