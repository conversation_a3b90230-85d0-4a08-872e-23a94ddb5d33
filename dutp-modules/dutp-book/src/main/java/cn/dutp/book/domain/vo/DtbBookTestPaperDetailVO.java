package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class DtbBookTestPaperDetailVO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answerId;

    private Integer score;

    private String answerContent;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    private String questionContent;

    private String analysis;

    private String rightAnswer;

    private String codeContent;

    private Integer questionScore;

}
