package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教材分享目录对象 dtb_book_share_chapter
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@TableName("dtb_book_share_chapter")
public class DtbBookShareChapter extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 分享目录id */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long shareChapterId;

    /** 分享id */
        @Excel(name = "分享id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long shareId;

    /** 章节id */
        @Excel(name = "章节id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("shareChapterId", getShareChapterId())
            .append("shareId", getShareId())
            .append("chapterId", getChapterId())
        .toString();
        }
        }
