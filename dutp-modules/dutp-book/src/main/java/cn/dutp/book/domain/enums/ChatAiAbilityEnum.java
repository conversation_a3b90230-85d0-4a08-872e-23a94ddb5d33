package cn.dutp.book.domain.enums;

import lombok.Getter;

@Getter
public enum ChatAiAbilityEnum {

    continuation(1,"续写"),
    abbreviation(2,"缩写"),
    expansion(3,"扩写"),
    polishing(4,"润色"),
    generate_questions(5,"生成试题"),
    generate_summary(6,"生成总结"),
    generate_learning_objectives(7,"生成学习目标"),
    generate_brain_maps(8,"生成脑图"),
    generate_outline(9,"生成大纲"),
    matching_case(10,"匹配案例"),
    generate_voice(11,"文生语音"),
    generate_picture(12,"文生图片"),
    image_enhancemen(13,"百度图片增强"),
    image_restoration(14,"百度图像修复"),
    generate_video(15,"百度视频生成"),
    text_correction(16,"文字纠错"),
    text_correction_list(17,"文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))"),
    video_compliance(18,"视频合规"),
    text_compliance(19,"文本合规"),
    text_compliance_list(20,"文本合规(上传黑白名单)"),
    image_compliance(21,"图片合规"),
    audio_compliance(22,"音频合规"),
    translation(23,"翻译");



    private final Integer code;
    private final String desc;

    ChatAiAbilityEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }
}
