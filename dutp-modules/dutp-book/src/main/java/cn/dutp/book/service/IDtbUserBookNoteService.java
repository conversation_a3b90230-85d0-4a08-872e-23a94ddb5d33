package cn.dutp.book.service;

import cn.dutp.book.domain.DtbUserBookNote;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * DUTP-DTB_022笔记/标注Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IDtbUserBookNoteService extends IService<DtbUserBookNote> {
    /**
     * 查询DUTP-DTB_022笔记/标注
     *
     * @param noteId DUTP-DTB_022笔记/标注主键
     * @return DUTP-DTB_022笔记/标注
     */
    public DtbUserBookNote selectDtbUserBookNoteByNoteId(Long noteId);

    /**
     * 查询DUTP-DTB_022笔记/标注列表
     *
     * @param dtbUserBookNote DUTP-DTB_022笔记/标注
     * @return DUTP-DTB_022笔记/标注集合
     */
    public List<DtbUserBookNote> selectDtbUserBookNoteList(DtbUserBookNote dtbUserBookNote);

    /**
     * 新增DUTP-DTB_022笔记/标注
     *
     * @param dtbUserBookNote DUTP-DTB_022笔记/标注
     * @return 结果
     */
    public AjaxResult insertDtbUserBookNote(DtbUserBookNote dtbUserBookNote);

    /**
     * 修改DUTP-DTB_022笔记/标注
     *
     * @param dtbUserBookNote DUTP-DTB_022笔记/标注
     * @return 结果
     */
    public AjaxResult updateDtbUserBookNote(DtbUserBookNote dtbUserBookNote);

    /**
     * 批量删除DUTP-DTB_022笔记/标注
     *
     * @param noteIds 需要删除的DUTP-DTB_022笔记/标注主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookNoteByNoteIds(List<Long> noteIds);

    AjaxResult selectReaderUserBookNoteList(ReaderCommonForm readerForm);

    void exportBookNote(HttpServletResponse response, DtbUserBookNote dtbUserBookNote);
}
