package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookTemplate;
import cn.dutp.book.domain.DtbBookTemplateImage;
import cn.dutp.book.mapper.DtbBookChapterMapper;
import cn.dutp.book.mapper.DtbBookTemplateImageMapper;
import cn.dutp.book.mapper.DtbBookTemplateMapper;
import cn.dutp.book.service.IDtbBookTemplateImageService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 教材模板图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Service
public class DtbBookTemplateImageServiceImpl extends ServiceImpl<DtbBookTemplateImageMapper, DtbBookTemplateImage> implements IDtbBookTemplateImageService {
    @Autowired
    private DtbBookTemplateImageMapper dtbBookTemplateImageMapper;

    @Autowired
    private DtbBookChapterMapper bookChapterMapper;

    @Autowired
    private DtbBookTemplateMapper bookTemplateMapper;

    /**
     * 查询教材模板图片列表
     *
     * @param dtbBookTemplateImage 教材模板图片
     * @return 教材模板图片
     */
    @Override
    public List<DtbBookTemplateImage> selectDtbBookTemplateImageList(DtbBookTemplateImage dtbBookTemplateImage) {
        LambdaQueryWrapper<DtbBookTemplateImage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbBookTemplateImage.getTemplateId())) {
            lambdaQueryWrapper.eq(DtbBookTemplateImage::getTemplateId, dtbBookTemplateImage.getTemplateId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplateImage.getUserId())) {
            lambdaQueryWrapper.eq(DtbBookTemplateImage::getUserId, dtbBookTemplateImage.getUserId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplateImage.getType())) {
            lambdaQueryWrapper.eq(DtbBookTemplateImage::getType, dtbBookTemplateImage.getType());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplateImage.getBelongTo())) {
            lambdaQueryWrapper.eq(DtbBookTemplateImage::getBelongTo, dtbBookTemplateImage.getBelongTo());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教材模板图片
     *
     * @param dtbBookTemplateImage 教材模板图片
     * @return 结果
     */
    @Override
    public boolean insertDtbBookTemplateImage(DtbBookTemplateImage dtbBookTemplateImage) {
        dtbBookTemplateImage.setCreateTime(new Date());
        if (dtbBookTemplateImage.getType() == 2) {
            dtbBookTemplateImage.setUserId(SecurityUtils.getUserId());
        }
        return this.save(dtbBookTemplateImage);
    }

    /**
     * 修改教材模板图片
     *
     * @param dtbBookTemplateImage 教材模板图片
     * @return 结果
     */
    @Override
    public boolean updateDtbBookTemplateImage(DtbBookTemplateImage dtbBookTemplateImage) {
        return this.updateById(dtbBookTemplateImage);
    }

    /**
     * 批量删除教材模板图片
     *
     * @param imageIds 需要删除的教材模板图片主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTemplateImageByImageIds(List<Long> imageIds) {
        return this.removeByIds(imageIds);
    }

    @Override
    public List<DtbBookTemplateImage> listForAuthor(DtbBookTemplateImage dtbBookTemplateImage) {
        Long chapterId = Optional.ofNullable(dtbBookTemplateImage)
                .map(DtbBookTemplateImage::getChapterId)
                .orElseThrow(() -> new RuntimeException("章节id不能为空"));

        Long templateId = bookChapterMapper.queryTemplateIdByChapterId(chapterId);
        dtbBookTemplateImage.setTemplateId(templateId);
        if (ObjectUtil.isNotEmpty(dtbBookTemplateImage.getFromTo()) && 2 == dtbBookTemplateImage.getFromTo()) {
            DtbBookTemplate dtbBookTemplate = bookTemplateMapper.selectOne(new LambdaQueryWrapper<DtbBookTemplate>()
                    .select(DtbBookTemplate::getThemeColor, DtbBookTemplate::getTemplateId)
                    .eq(DtbBookTemplate::getTemplateId, templateId));
            if (ObjectUtil.isNotEmpty(dtbBookTemplate.getThemeColor())) {
                dtbBookTemplateImage.setThemeColor(dtbBookTemplate.getThemeColor());
            }
        }

        return dtbBookTemplateImageMapper.listForAuthor(dtbBookTemplateImage);
    }

}
