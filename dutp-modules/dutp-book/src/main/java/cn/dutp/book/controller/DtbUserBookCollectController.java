package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbUserBookCollect;
import cn.dutp.book.domain.vo.DtbUserBookCollectVo;
import cn.dutp.book.service.IDtbUserBookCollectService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * DUTP-DTB_015收藏教材Controller
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@RestController
@RequestMapping("/collect")
public class DtbUserBookCollectController extends BaseController
{
    @Autowired
    private IDtbUserBookCollectService dtbUserBookCollectService;

    /**
     * 查询DUTP-DTB_015收藏教材列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserBookCollect dtbUserBookCollect)
    {
        startPage();
        List<DtbUserBookCollectVo> list = dtbUserBookCollectService.selectDtbUserBookCollectList(dtbUserBookCollect);
        return getDataTable(list);
    }

    /**
     * 获取DUTP-DTB_015收藏教材详细信息
     */
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(dtbUserBookCollectService.selectDtbUserBookCollectByUserId(userId));
    }

    /**
     * 新增DUTP-DTB_015收藏教材
     */
    @Log(title = "DUTP-DTB_015收藏教材", operatorType = OperatorType.READER,  businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBookCollect dtbUserBookCollect)
    {
        return toAjax(dtbUserBookCollectService.insertDtbUserBookCollect(dtbUserBookCollect));
    }

    /**
     * 修改DUTP-DTB_015收藏教材
     */
    @Log(title = "DUTP-DTB_015收藏教材", operatorType = OperatorType.READER,  businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBookCollect dtbUserBookCollect)
    {
        return toAjax(dtbUserBookCollectService.updateDtbUserBookCollect(dtbUserBookCollect));
    }

    /**
     * 删除DUTP-DTB_015收藏教材
     */
    @Log(title = "DUTP-DTB_015收藏教材", operatorType = OperatorType.READER, businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(dtbUserBookCollectService.deleteDtbUserBookCollectByUserIds(Arrays.asList(userIds)));
    }
    /**
     * 新增DUTP-DTB_015收藏教材
     */
    @Log(title = "DUTP-DTB_015收藏教材", operatorType = OperatorType.READER,  businessType = BusinessType.INSERT)
    @GetMapping("/addDtbUserBookCollect")
    public AjaxResult addDtbUserBookCollect(String bookId) {
        Long id = Long.valueOf(bookId);
        DtbUserBookCollect dtbUserBookCollect = new DtbUserBookCollect();
        // 获取用户Id
        dtbUserBookCollect.setUserId(SecurityUtils.getUserId());
        dtbUserBookCollect.setBookId(id);
        return success(dtbUserBookCollectService.insertDtbUserBookCollect(dtbUserBookCollect));
    }
    /**
     * 详情收藏教材删除
     */
    @Log(title = "详情收藏教材删除", operatorType = OperatorType.READER, businessType = BusinessType.DELETE)
    @GetMapping("/delDtbUserBookCollect")
    public AjaxResult delDtbUserBookCollect(String bookId) {
        Long id = Long.valueOf(bookId);
        DtbUserBookCollect dtbUserBookCollect = new DtbUserBookCollect();
        dtbUserBookCollect.setBookId(id);
        // 获取用户Id
        dtbUserBookCollect.setUserId(SecurityUtils.getUserId());
        return success(dtbUserBookCollectService.delDtbUserBookCollect(dtbUserBookCollect));
    }
}
