package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookFile;
import cn.dutp.book.service.IDtbBookFileService;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教材资源Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/bookFile")
public class DtbBookFileController extends BaseController {
    @Autowired
    private IDtbBookFileService dtbBookFileService;

    /**
     * 查询教材资源列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookFile dtbBookFile) {
        startPage();
        List<DtbBookFile> list = dtbBookFileService.selectDtbBookFileList(dtbBookFile);
        return getDataTable(list);
    }


    /**
     * 获取教材资源详细信息
     */
    @GetMapping(value = "/{bookFileId}")
    public AjaxResult getInfo(@PathVariable("bookFileId") Long bookFileId) {
        return success(dtbBookFileService.selectDtbBookFileByBookFileId(bookFileId));
    }

    /**
     * 新增教材资源
     */
    @RequiresPermissions("book:book:upload")
    @Log(title = "新增教材资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookFile dtbBookFile) {
        return toAjax(dtbBookFileService.insertDtbBookFile(dtbBookFile));
    }

    /**
     * 修改教材资源
     */
    @RequiresPermissions("book:book:resourceEdit")
    @Log(title = "修改教材资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookFile dtbBookFile) {
        return toAjax(dtbBookFileService.updateDtbBookFile(dtbBookFile));
    }

    /**
     * 批量修改教材资源下载权限
     */
    @RequiresPermissions("book:book:permission")
    @Log(title = "批量修改教材资源下载权限", businessType = BusinessType.UPDATE)
    @PutMapping("/batch")
    public AjaxResult editBatch(@RequestBody List<DtbBookFile> fileList) {
        return toAjax(dtbBookFileService.editBatch(fileList));
    }

    /**
     * 删除教材资源
     */
    @RequiresPermissions("book:book:delete")
    @Log(title = "删除教材资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bookFileIds}")
    public AjaxResult remove(@PathVariable Long[] bookFileIds) {
        return toAjax(dtbBookFileService.deleteDtbBookFileByBookFileIds(Arrays.asList(bookFileIds)));
    }
}
