package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookShare;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教材分享记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface IDtbBookShareService extends IService<DtbBookShare>
{
    /**
     * 查询教材分享记录
     *
     * @param shareId 教材分享记录主键
     * @return 教材分享记录
     */
    public DtbBookShare selectDtbBookShareByShareId(Long shareId);

    /**
     * 查询教材分享记录列表
     *
     * @param dtbBookShare 教材分享记录
     * @return 教材分享记录集合
     */
    public List<DtbBookShare> selectDtbBookShareList(DtbBookShare dtbBookShare);

    /**
     * 新增教材分享记录
     *
     * @param dtbBookShare 教材分享记录
     * @return 结果
     */
    public boolean insertDtbBookShare(DtbBookShare dtbBookShare);

    /**
     * 修改教材分享记录
     *
     * @param dtbBookShare 教材分享记录
     * @return 结果
     */
    public boolean updateDtbBookShare(DtbBookShare dtbBookShare);

    /**
     * 批量删除教材分享记录
     *
     * @param shareIds 需要删除的教材分享记录主键集合
     * @return 结果
     */
    public boolean deleteDtbBookShareByShareIds(List<Long> shareIds);

    AjaxResult getIsShareCode(Long shareId);

    /**
     * 验证分享码
     * @param dtbBookShare
     * @return
     */
    AjaxResult checkShareCode(DtbBookShare dtbBookShare);
}
