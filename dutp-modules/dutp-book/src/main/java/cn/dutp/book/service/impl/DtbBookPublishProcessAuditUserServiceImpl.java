package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookPublishProcessAuditUser;
import cn.dutp.book.mapper.DtbBookPublishProcessAuditUserMapper;
import cn.dutp.book.service.IDtbBookPublishProcessAuditUserService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发布流程审核人Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Service
public class DtbBookPublishProcessAuditUserServiceImpl extends ServiceImpl<DtbBookPublishProcessAuditUserMapper, DtbBookPublishProcessAuditUser> implements IDtbBookPublishProcessAuditUserService
{
    @Autowired
    private DtbBookPublishProcessAuditUserMapper dtbBookPublishProcessAuditUserMapper;

    /**
     * 查询发布流程审核人
     *
     * @param auditUserId 发布流程审核人主键
     * @return 发布流程审核人
     */
    @Override
    public DtbBookPublishProcessAuditUser selectDtbBookPublishProcessAuditUserByAuditUserId(Long auditUserId)
    {
        return this.getById(auditUserId);
    }

    /**
     * 查询发布流程审核人列表
     *
     * @param dtbBookPublishProcessAuditUser 发布流程审核人
     * @return 发布流程审核人
     */
    @Override
    public List<DtbBookPublishProcessAuditUser> selectDtbBookPublishProcessAuditUserList(DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser)
    {
        LambdaQueryWrapper<DtbBookPublishProcessAuditUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookPublishProcessAuditUser.getUserId())) {
                lambdaQueryWrapper.eq(DtbBookPublishProcessAuditUser::getUserId
                ,dtbBookPublishProcessAuditUser.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookPublishProcessAuditUser.getDeptId())) {
                lambdaQueryWrapper.eq(DtbBookPublishProcessAuditUser::getDeptId
                ,dtbBookPublishProcessAuditUser.getDeptId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookPublishProcessAuditUser.getState())) {
                lambdaQueryWrapper.eq(DtbBookPublishProcessAuditUser::getState
                ,dtbBookPublishProcessAuditUser.getState());
            }
                if(ObjectUtil.isNotEmpty(dtbBookPublishProcessAuditUser.getReason())) {
                lambdaQueryWrapper.eq(DtbBookPublishProcessAuditUser::getReason
                ,dtbBookPublishProcessAuditUser.getReason());
            }
                if(ObjectUtil.isNotEmpty(dtbBookPublishProcessAuditUser.getProcessId())) {
                lambdaQueryWrapper.eq(DtbBookPublishProcessAuditUser::getProcessId
                ,dtbBookPublishProcessAuditUser.getProcessId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增发布流程审核人
     *
     * @param dtbBookPublishProcessAuditUser 发布流程审核人
     * @return 结果
     */
    @Override
    public boolean insertDtbBookPublishProcessAuditUser(DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser)
    {
        return this.save(dtbBookPublishProcessAuditUser);
    }

    /**
     * 修改发布流程审核人
     *
     * @param dtbBookPublishProcessAuditUser 发布流程审核人
     * @return 结果
     */
    @Override
    public boolean updateDtbBookPublishProcessAuditUser(DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser)
    {
        return this.updateById(dtbBookPublishProcessAuditUser);
    }

    /**
     * 批量删除发布流程审核人
     *
     * @param auditUserIds 需要删除的发布流程审核人主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookPublishProcessAuditUserByAuditUserIds(List<Long> auditUserIds)
    {
        return this.removeByIds(auditUserIds);
    }

}
