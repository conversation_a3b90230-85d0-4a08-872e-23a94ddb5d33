package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookAttribute;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 数字教材简介Service接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface IDtbBookAttributeService extends IService<DtbBookAttribute> {

    /**
     * 查询数字教材简介
     *
     * @param bookId 数字教材简介主键
     * @return 数字教材简介
     */
    DtbBookAttribute selectDtbBookAttributeByBookId(Long bookId);

    /**
     * 新增数字教材简介
     *
     * @param dtbBookAttribute 数字教材简介
     * @return 结果
     */
    Long insertDtbBookAttribute(DtbBookAttribute dtbBookAttribute);

    /**
     * 修改数字教材简介
     *
     * @param dtbBookAttribute 数字教材简介
     * @return 结果
     */
    boolean updateDtbBookAttribute(DtbBookAttribute dtbBookAttribute);

    /**
     * 学生教师端获取推荐教材
     *
     * @param dtbBookAttribute 数字教材简介
     * @return 结果
     */
    List<DtbBookAttribute> getRecommendedTextbooks(DtbBookAttribute dtbBookAttribute);

}
