package cn.dutp.book.domain.vo;

import cn.dutp.book.domain.DtbBookTestPaper;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

@Data
public class BookPaperTreeListVO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    private String chapterName;

    private List<DtbBookTestPaper> dtbBookTestPaperList;
}
