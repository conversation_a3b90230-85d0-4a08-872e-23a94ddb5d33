package cn.dutp.book.service;

import java.util.List;

import cn.dutp.book.domain.DtbBookChapterData;
import cn.dutp.book.domain.vo.DtbBookApplicationUserDataVo;

import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;

/**
 * 章节数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface IDtbBookChapterDataService extends IService<DtbBookChapterData> {

    /**
     * 查询章节数据统计列表
     *
     * @param dtbBookChapterData 章节数据统计
     * @return 章节数据统计集合
     */
    public List<DtbBookChapterData> selectDtbBookChapterDataList(DtbBookChapterData dtbBookChapterData);

    /**
     * 新增章节数据统计
     *
     * @param dtbBookChapterData 章节数据统计
     * @return 结果
     */
    public boolean insertDtbBookChapterData(DtbBookChapterData dtbBookChapterData);

    /**
     * 修改章节数据统计
     *
     * @param dtbBookChapterData 章节数据统计
     * @return 结果
     */
    public boolean updateDtbBookChapterData(DtbBookChapterData dtbBookChapterData);

    void exportAppData(HttpServletResponse response, DtbBookChapterData dtbBookChapterData);

    DtbBookChapterData dataOverview(DtbBookChapterData dtbBookChapterData);

    void exportDataOverview(HttpServletResponse response, DtbBookChapterData dtbBookChapterData);

    /**
     * 查询教材应用用户数据
     * 
     * @param dtbBookApplicationUserData 查询参数
     * @return 用户数据
     */
    DtbBookApplicationUserDataVo selectBookApplicationUserData(DtbBookApplicationUserDataVo dtbBookApplicationUserData);
}
