package cn.dutp.book.mapper;

import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbArVrResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

/**
 * AR/VR资源库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Repository
public interface DtbArVrResourceMapper extends BaseMapper<DtbArVrResource>
{
    /**
     * 查询AR/VR资源库列表
     *
     * @param dtbArVrResource AR/VR资源库
     * @return AR/VR资源库集合
     */
    public List<DtbArVrResource> selectDtbArVrResourceList(DtbArVrResource dtbArVrResource);

    /**
     * 查询AR/VR资源库
     *
     * @param resourceId AR/VR资源库主键
     * @return AR/VR资源库
     */
    public DtbArVrResource selectDtbArVrResourceByResourceId(Long resourceId);

    /**
     * 新增AR/VR资源库
     *
     * @param dtbArVrResource AR/VR资源库
     * @return 结果
     */
    public int insertDtbArVrResource(DtbArVrResource dtbArVrResource);

    /**
     * 修改AR/VR资源库
     *
     * @param dtbArVrResource AR/VR资源库
     * @return 结果
     */
    public int updateDtbArVrResource(DtbArVrResource dtbArVrResource);

    /**
     * 删除AR/VR资源库
     *
     * @param resourceId AR/VR资源库主键
     * @return 结果
     */
    public int deleteDtbArVrResourceByResourceId(Long resourceId);

    /**
     * 批量删除AR/VR资源库
     *
     * @param resourceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDtbArVrResourceByResourceIds(String[] resourceIds);
}
