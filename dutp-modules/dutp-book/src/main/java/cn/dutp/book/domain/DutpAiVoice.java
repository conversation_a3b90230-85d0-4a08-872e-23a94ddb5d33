package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 语音音色对象 dutp_ai_voice
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("dutp_ai_voice")
public class DutpAiVoice extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long voiceId;

    /**
     * 发音人
     */
    @Excel(name = "发音人")
    private String name;

    /**
     * 语种 1普通话2英语
     */
    @Excel(name = "语种")
    private Integer type;

    /**
     * 参数
     */
    @Excel(name = "参数")
    private String vcn;

    /**
     * 指令id
     */
    @Excel(name = "指令id")
    private Long promptId;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String profilePicture;
    /**
     * 描述
     */
    @Excel(name = "描述")
    private String description;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("voiceId", getVoiceId())
                .append("name", getName())
                .append("type", getType())
                .append("vcn", getVcn())
                .append("promptId", getPromptId())
                .append("profilePicture", getProfilePicture())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
