package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookQuestionTemplate;
import cn.dutp.book.mapper.DtbBookQuestionTemplateMapper;
import cn.dutp.book.service.IDtbBookQuestionTemplateService;
import cn.dutp.common.core.exception.ServiceException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 题样式模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-25
 */
@Service
public class DtbBookQuestionTemplateServiceImpl extends ServiceImpl<DtbBookQuestionTemplateMapper, DtbBookQuestionTemplate> implements IDtbBookQuestionTemplateService {
    @Autowired
    private DtbBookQuestionTemplateMapper dtbBookQuestionTemplateMapper;

    /**
     * 查询题样式模板
     *
     * @param chapterId 题样式模板主键
     * @return 题样式模板
     */
    @Override
    public DtbBookQuestionTemplate selectDtbBookQuestionTemplateByQuestionTemplateId(Long chapterId) {
        return lambdaQuery().eq(DtbBookQuestionTemplate::getChapterId, chapterId).one();
    }

    /**
     * 修改题样式模板
     *
     * @param dtbBookQuestionTemplate 题样式模板
     * @return 结果
     */
    @Override
    public boolean updateDtbBookQuestionTemplate(DtbBookQuestionTemplate dtbBookQuestionTemplate) {
        Optional.ofNullable(dtbBookQuestionTemplate).map(DtbBookQuestionTemplate::getChapterId).orElseThrow(() -> new ServiceException("chapterId不能为空"));
        Optional.of(dtbBookQuestionTemplate).map(DtbBookQuestionTemplate::getBookId).orElseThrow(() -> new ServiceException("bookId不能为空"));
        Long questionTemplateId = dtbBookQuestionTemplate.getQuestionTemplateId();
        if (questionTemplateId == null) {
            Integer count = lambdaQuery().eq(DtbBookQuestionTemplate::getChapterId, dtbBookQuestionTemplate.getChapterId()).count();
            if (count >= 1) {
                return lambdaUpdate()
                        .eq(DtbBookQuestionTemplate::getChapterId, dtbBookQuestionTemplate.getChapterId())
                        .update(dtbBookQuestionTemplate);
            }
            return this.save(dtbBookQuestionTemplate);
        } else {
            return this.updateById(dtbBookQuestionTemplate);
        }
    }

}
