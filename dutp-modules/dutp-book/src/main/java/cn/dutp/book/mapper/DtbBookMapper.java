package cn.dutp.book.mapper;

import cn.dutp.domain.DtbBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB_002数字教材Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Repository
public interface DtbBookMapper extends BaseMapper<DtbBook>
{

    List<DtbBook> bookShopList(DtbBook dtbBook);

    Long selectLeaderIdByBookId(Long bookId);
}
