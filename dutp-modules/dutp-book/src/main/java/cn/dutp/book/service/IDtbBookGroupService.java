package cn.dutp.book.service;

import cn.dutp.domain.DtbBookGroup;
import cn.dutp.book.domain.vo.DtbBookGroupVo;
import cn.dutp.book.domain.vo.DtbBookPermissionsVo;
import cn.dutp.system.api.domain.SysUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 数字教材作者编辑团队Service接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface IDtbBookGroupService extends IService<DtbBookGroup> {
    /**
     * 查询数字教材作者编辑团队
     *
     * @param bookId 数字教材作者编辑团队主键
     * @return 数字教材作者编辑团队
     */
    DtbBookGroupVo selectDtbBookGroupByBookId(Long bookId);

    /**
     * 更新数字教材作者编辑团队
     *
     * @param dtbBookGroup 数字教材作者编辑团队
     * @return 结果
     */
    boolean updateDtbBookGroup(DtbBookGroupVo dtbBookGroup);

    List<SysUser> groupUserList(Long bookId);

    DtbBookPermissionsVo getPermissionsInfo(Long bookId);
}
