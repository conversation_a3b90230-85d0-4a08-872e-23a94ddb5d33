package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 用户答卷对象 dtb_book_test_paper_answer
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
@TableName("dtb_book_test_paper_answer")
public class DtbBookTestPaperAnswer extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperAnswerId;

    /**
     * dutp_user表中user_id
     */
    @Excel(name = "dutp_user表中user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * dtb_book_test_paper的id
     */
    @Excel(name = "dtb_book_test_paper的id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookPaperId;

    /**
     * 试卷id
     */
    @Excel(name = "试卷id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperId;

    /**
     * 总分
     */
    @Excel(name = "总分")
    private BigDecimal totalScore;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("paperAnswerId", getPaperAnswerId())
                .append("userId", getUserId())
                .append("bookPaperId", getBookPaperId())
                .append("paperId", getPaperId())
                .append("totalScore", getTotalScore())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
