package cn.dutp.book.service;

import cn.dutp.book.domain.MoocPsychologyHealthInstructions;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 心理健康量人格说明Service接口
 *
 * <AUTHOR>
 * @date 2025-09-24
 */
public interface IMoocPsychologyHealthInstructionsService extends IService<MoocPsychologyHealthInstructions>
{
    /**
     * 查询心理健康量人格说明
     *
     * @param instructionsId 心理健康量人格说明主键
     * @return 心理健康量人格说明
     */
    public MoocPsychologyHealthInstructions selectMoocPsychologyHealthInstructionsByInstructionsId(Long instructionsId);

    /**
     * 查询心理健康量人格说明列表
     *
     * @param moocPsychologyHealthInstructions 心理健康量人格说明
     * @return 心理健康量人格说明集合
     */
    public List<MoocPsychologyHealthInstructions> selectMoocPsychologyHealthInstructionsList(MoocPsychologyHealthInstructions moocPsychologyHealthInstructions);

    /**
     * 新增心理健康量人格说明
     *
     * @param moocPsychologyHealthInstructions 心理健康量人格说明
     * @return 结果
     */
    public boolean insertMoocPsychologyHealthInstructions(MoocPsychologyHealthInstructions moocPsychologyHealthInstructions);

    /**
     * 修改心理健康量人格说明
     *
     * @param moocPsychologyHealthInstructions 心理健康量人格说明
     * @return 结果
     */
    public boolean updateMoocPsychologyHealthInstructions(MoocPsychologyHealthInstructions moocPsychologyHealthInstructions);

    /**
     * 批量删除心理健康量人格说明
     *
     * @param instructionsIds 需要删除的心理健康量人格说明主键集合
     * @return 结果
     */
    public boolean deleteMoocPsychologyHealthInstructionsByInstructionsIds(List<Long> instructionsIds);

}
