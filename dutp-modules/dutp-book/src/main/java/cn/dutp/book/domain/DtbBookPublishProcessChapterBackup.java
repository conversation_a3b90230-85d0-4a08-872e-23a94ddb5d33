package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 发布流程章节备份对象 dtb_book_publish_process_chapter_backup
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@TableName("dtb_book_publish_process_chapter_backup")
public class DtbBookPublishProcessChapterBackup extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long backupId;

    /** 流程ID */
        @Excel(name = "流程ID")
    private Long processId;

    /** $column.columnComment */
        @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long stepId;

    /** 章节ID */
        @Excel(name = "章节ID")
    private Long chapterId;

    /** 章节保存的mongodb中的objectId */
        @Excel(name = "章节保存的mongodb中的objectId")
    private String chapterObjectId;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("backupId", getBackupId())
            .append("processId", getProcessId())
            .append("stepId", getStepId())
            .append("chapterId", getChapterId())
            .append("chapterObjectId", getChapterObjectId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
