<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbPurchaseCodeMapper">

    <select id="selectBookCodeList" resultType="cn.dutp.domain.DtbBookPurchaseCode" parameterType="cn.dutp.domain.DtbBookPurchaseCode">
        select
            b.book_id as 'bookId',
            b.book_name as 'bookName',
            b.isbn as 'isbn',
            b.issn as 'issn',
            b.price_sale as 'priceSale',
            b.current_version_id as 'currentVersionId',
            b.master_flag as 'masterFlag',
            b.publish_status as 'publishStatus',
            b.shelf_state as 'shelfState',
            sum(case when bpc.code_type = 1 then 1 else 0 end) as 'codeTotal',
            sum(case when bpc.code_type = 1 and bpc.state = 1 then 1 else 0 end) as longTermTotal,
            sum(case when bpc.code_type = 1 and bpc.state = 3 then 1 else 0 end) as useTotal,
            sum(case when bpc.code_type = 1 and bpc.state = 2 then 1 else 0 end) as occupyTotal,
            sum(case when bpc.code_type = 1 and bpc.state = 1 then 1 else 0 end) as notOccupyTotal,
            sum(case when bpc.code_type = 1 and bpc.state = 4 then 1 else 0 end) as expireTotal,
            sum(case when bpc.code_type = 1 and bpc.state = 5 then 1 else 0 end) as cancelTotal,
            b.create_time as 'createTime',
            bpp.process_date as 'publishDate'
        from
            dtb_book b
                left join dtb_book_purchase_code bpc on b.book_id = bpc.book_id  and bpc.del_flag = '0'
                left join dtb_book_publish_process bpp on b.book_id = bpp.book_id and b.last_version_id = bpp.version_id and bpp.step_id = 16 and bpp.state = 2 and bpp.addition_flag = 0
        where b.del_flag = '0' and b.publish_status = '2' and b.book_organize = '1' and b.master_flag != 3
        <if test="param.bookName != null and param.bookName != ''">
            and (b.book_name like concat('%', #{param.bookName}, '%') or b.isbn like concat('%', #{param.bookName}, '%')  or b.issn like concat('%', #{param.bookName}, '%') )
        </if>
        <if test="param.shelfState != null and param.shelfState != ''">
            and b.shelf_state = #{param.shelfState}
        </if>
        group by b.book_id,bpp.process_date order by bpp.process_date desc
    </select>

    <select id="selectBookCodeMasterFlagList" resultType="cn.dutp.domain.DtbBookPurchaseCode" parameterType="cn.dutp.domain.DtbBookPurchaseCode">
        select
        b.book_id as 'bookId',
        b.book_name as 'bookName',
        b.isbn as 'isbn',
        b.price_sale as 'priceSale',
        b.current_version_id as 'currentVersionId',
        b.master_flag as 'masterFlag',
        b.publish_status as 'publishStatus',
        b.shelf_state as 'shelfState',
        b.master_book_id as  'masterBookId',
        count(bpc.code_id) as 'codeTotal',
        sum(case when bpc.code_type = 1 and  bpc.state = 1 then 1 else 0 end) as longTermTotal,
        sum(case when bpc.state = 3 then 1 else 0 end) as useTotal,
        sum(case when bpc.state = 2 then 1 else 0 end) as occupyTotal,
        sum(case when bpc.state = 1 then 1 else 0 end) as notOccupyTotal,
        sum(case when bpc.state = 4 then 1 else 0 end) as expireTotal,
        sum(case when bpc.state = 5 then 1 else 0 end) as cancelTotal,
        b.create_time as 'createTime'
        from
        dtb_book b
        left join dtb_book_purchase_code bpc on b.book_id = bpc.book_id
        where b.del_flag = '0' and b.publish_status = '2' and b.book_organize = '1' and b.master_flag = 3
        <if test="param.bookName != null and param.bookName != ''">
            and (b.book_name like concat('%', #{param.bookName}, '%') or b.isbn like concat('%', #{param.bookName}, '%')  or b.issn like concat('%', #{param.bookName}, '%') )
        </if>
        <if test="param.shelfState != null and param.shelfState != ''">
            and b.shelf_state = #{param.shelfState}
        </if>
        group by b.book_id
    </select>

    <select id="selectCodeList" resultType="cn.dutp.domain.DtbBookPurchaseCode" parameterType="cn.dutp.domain.DtbBookPurchaseCode">
        select
            bpc.code_id as 'codeId',
            bpc.code,
            bpc.code_type as 'codeType',
            bpc.state,
            bpc.bind_date as 'bindDate',
            bpc.time_limit as 'timeLimit',
            bpc.exchange_date as 'exchangeDate',
            s1.user_id as 'userId',
            s1.user_name as 'userName',
            s1.nick_name as 'nickName',
            s1.schoolName,
            s1.academyName,
            s1.specialityName,
            boc.create_time as 'operateTime',
            boc.create_by as 'operateBy',
            s2.create_by as 'createBy'
        from dtb_book_purchase_code bpc
            left join (
                select
                    du.user_id,
                    du.user_name,
                    du.nick_name,
                    ds.school_name as schoolName,
                    an.school_name as academyName,
                    sn.school_name as specialityName
                from dutp_user du
                         left join dutp_school ds on du.school_id = ds.school_id
                         left join dutp_school an on du.academy_id = an.school_id
                         left join dutp_school sn on du.speciality_id = sn.school_id
            ) s1 on bpc.user_id = s1.user_id
            left join dtb_book_order_code boc on bpc.code_id = boc.code_id
            left join (
                SELECT boc.code_Id, boc.create_by
                FROM (
                    SELECT
                        MAX(boc.create_time) AS createTime,
                        boc.code_id AS codeId
                    FROM dtb_book_code_export_item boc
                    GROUP BY boc.code_id
                ) AS latest
                JOIN dtb_book_code_export_item boc
                ON latest.codeId = boc.code_id AND latest.createTime = boc.create_time
            ) s2 on bpc.code_id = s2.code_id
        where bpc.del_flag = '0' and bpc.book_id = #{param.bookId} and bpc.code_type = #{param.codeType}
        <if test="param.userName != null and param.userName != ''">
            and s1.user_name like concat('%', #{param.userName}, '%')
        </if>
        <if test="param.schoolName != null and param.schoolName != ''">
            and s1.schoolName like concat('%', #{param.schoolName}, '%')
        </if>
        <if test="param.state != null and param.state != ''">
            and bpc.state = #{param.state}
        </if>
        <if test="param.startTime != null">AND boc.create_time >= #{param.startTime}</if>
        <if test="param.endTime != null">AND boc.create_time &lt;= #{param.endTime}</if>
    </select>

</mapper>