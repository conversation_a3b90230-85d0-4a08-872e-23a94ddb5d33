package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookPublishProcess;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Repository
public interface DtbBookPublishProcessMapper extends BaseMapper<DtbBookPublishProcess>
{

    List<DtbBookPublishProcess> getBackupList(@Param("userId") Long userId,@Param("isAdmin") Boolean isAdmin,@Param("param") DtbBookPublishProcess dtbBookPublishProcess);

    List<DtbBookPublishProcess> getProcessDetail(@Param("param") DtbBookPublishProcess dtbBookPublishProcess);

    DtbBookPublishProcess queryBookLastProcess(Long bookId);

    DtbBookPublishProcess getPrevProcessInfo(@Param("bookId") Long bookId,@Param("stepId") Long stepId);

    DtbBookPublishProcess getProcessInfoLink(Long processId);

    List<DtbBookPublishProcess> selectProcessChapters(Long toProcessId);
}
