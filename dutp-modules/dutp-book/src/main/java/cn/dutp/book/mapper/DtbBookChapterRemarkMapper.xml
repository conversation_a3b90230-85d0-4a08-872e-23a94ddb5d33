<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookChapterRemarkMapper">
    
    <resultMap type="DtbBookChapterRemark" id="DtbBookChapterRemarkResult">
        <result property="remarkId"    column="remark_id"    />
        <result property="dataId"    column="data_id"    />
        <result property="remarkContent"    column="remark_content"    />
        <result property="bookId"    column="book_id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="authorId"    column="author_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbBookChapterRemarkVo">
        select remark_id, data_id, remark_content, book_id, chapter_id, author_id, del_flag, create_by, create_time, update_by, update_time from dtb_book_chapter_remark
    </sql>

    <select id="selectDtbBookChapterRemarkList" parameterType="DtbBookChapterRemark" resultMap="DtbBookChapterRemarkResult">
        <include refid="selectDtbBookChapterRemarkVo"/>
        <where>  
            <if test="dataId != null  and dataId != ''"> and data_id = #{dataId}</if>
            <if test="remarkContent != null  and remarkContent != ''"> and remark_content = #{remarkContent}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="authorId != null "> and author_id = #{authorId}</if>
        </where>
    </select>
    
    <select id="selectDtbBookChapterRemarkByRemarkId" parameterType="Long" resultMap="DtbBookChapterRemarkResult">
        <include refid="selectDtbBookChapterRemarkVo"/>
        where remark_id = #{remarkId}
    </select>

    <insert id="insertDtbBookChapterRemark" parameterType="DtbBookChapterRemark" useGeneratedKeys="true" keyProperty="remarkId">
        insert into dtb_book_chapter_remark
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null">data_id,</if>
            <if test="remarkContent != null">remark_content,</if>
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="authorId != null">author_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null">#{dataId},</if>
            <if test="remarkContent != null">#{remarkContent},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="authorId != null">#{authorId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbBookChapterRemark" parameterType="DtbBookChapterRemark">
        update dtb_book_chapter_remark
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="remarkContent != null">remark_content = #{remarkContent},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="authorId != null">author_id = #{authorId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where remark_id = #{remarkId}
    </update>

    <delete id="deleteDtbBookChapterRemarkByRemarkId" parameterType="Long">
        delete from dtb_book_chapter_remark where remark_id = #{remarkId}
    </delete>

    <delete id="deleteDtbBookChapterRemarkByRemarkIds" parameterType="String">
        delete from dtb_book_chapter_remark where remark_id in 
        <foreach item="remarkId" collection="array" open="(" separator="," close=")">
            #{remarkId}
        </foreach>
    </delete>
</mapper>