package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * AR/VR资源库对象 dtb_ar_vr_resource
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("dtb_ar_vr_resource")
public class DtbArVrResource extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** AR/VR资源ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resourceId;

    /** AR/VR资源名称 */
        @Excel(name = "AR/VR资源名称")
    private String name;

    /** 封面图片地址 */
        @Excel(name = "封面图片地址")
    private String coverUrl;

    /** AR/VR资源链接 */
        @Excel(name = "AR/VR资源链接")
    private String resourceUrl;

    /** 资源描述 */
        @Excel(name = "资源描述")
    private String description;

    /** 文件夹ID */
    @Excel(name = "文件夹ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("resourceId", getResourceId())
            .append("name", getName())
            .append("coverUrl", getCoverUrl())
            .append("resourceUrl", getResourceUrl())
            .append("description", getDescription())
            .append("folderId", getFolderId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
        }
