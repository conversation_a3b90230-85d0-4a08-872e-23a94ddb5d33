package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookTestPaperAnswerDetailMapper;
import cn.dutp.book.domain.DtbBookTestPaperAnswerDetail;
import cn.dutp.book.service.IDtbBookTestPaperAnswerDetailService;

/**
 * 试卷答题明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Service
public class DtbBookTestPaperAnswerDetailServiceImpl extends ServiceImpl<DtbBookTestPaperAnswerDetailMapper, DtbBookTestPaperAnswerDetail> implements IDtbBookTestPaperAnswerDetailService
{
    @Autowired
    private DtbBookTestPaperAnswerDetailMapper dtbBookTestPaperAnswerDetailMapper;

    /**
     * 查询试卷答题明细
     *
     * @param answerDetailId 试卷答题明细主键
     * @return 试卷答题明细
     */
    @Override
    public DtbBookTestPaperAnswerDetail selectDtbBookTestPaperAnswerDetailByAnswerDetailId(Long answerDetailId)
    {
        return this.getById(answerDetailId);
    }

    /**
     * 查询试卷答题明细列表
     *
     * @param dtbBookTestPaperAnswerDetail 试卷答题明细
     * @return 试卷答题明细
     */
    @Override
    public List<DtbBookTestPaperAnswerDetail> selectDtbBookTestPaperAnswerDetailList(DtbBookTestPaperAnswerDetail dtbBookTestPaperAnswerDetail)
    {
        LambdaQueryWrapper<DtbBookTestPaperAnswerDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookTestPaperAnswerDetail.getPaperAnswerId())) {
                lambdaQueryWrapper.eq(DtbBookTestPaperAnswerDetail::getPaperAnswerId
                ,dtbBookTestPaperAnswerDetail.getPaperAnswerId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTestPaperAnswerDetail.getQuestionAnswerId())) {
                lambdaQueryWrapper.eq(DtbBookTestPaperAnswerDetail::getQuestionAnswerId
                ,dtbBookTestPaperAnswerDetail.getQuestionAnswerId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTestPaperAnswerDetail.getScore())) {
                lambdaQueryWrapper.eq(DtbBookTestPaperAnswerDetail::getScore
                ,dtbBookTestPaperAnswerDetail.getScore());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增试卷答题明细
     *
     * @param dtbBookTestPaperAnswerDetail 试卷答题明细
     * @return 结果
     */
    @Override
    public boolean insertDtbBookTestPaperAnswerDetail(DtbBookTestPaperAnswerDetail dtbBookTestPaperAnswerDetail)
    {
        return this.save(dtbBookTestPaperAnswerDetail);
    }

    /**
     * 修改试卷答题明细
     *
     * @param dtbBookTestPaperAnswerDetail 试卷答题明细
     * @return 结果
     */
    @Override
    public boolean updateDtbBookTestPaperAnswerDetail(DtbBookTestPaperAnswerDetail dtbBookTestPaperAnswerDetail)
    {
        return this.updateById(dtbBookTestPaperAnswerDetail);
    }

    /**
     * 批量删除试卷答题明细
     *
     * @param answerDetailIds 需要删除的试卷答题明细主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTestPaperAnswerDetailByAnswerDetailIds(List<Long> answerDetailIds)
    {
        return this.removeByIds(answerDetailIds);
    }

}
