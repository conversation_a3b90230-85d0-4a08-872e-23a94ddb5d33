package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.MoocPsychologyHealthScale;
import cn.dutp.book.domain.MoocPsychologyHealthScaleFacet;
import cn.dutp.book.domain.MoocPsychologyHealthScaleQuestion;
import cn.dutp.book.domain.MoocPsychologyHealthUserResult;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 用户心理测试结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Repository
public interface MoocPsychologyHealthUserResultMapper extends BaseMapper<MoocPsychologyHealthUserResult>
{
    List<MoocPsychologyHealthUserResult> getTestResultList(MoocPsychologyHealthScale scale);

    List<MoocPsychologyHealthScaleQuestion> getTestResultDetail(Long resultId);

    List<MoocPsychologyHealthScaleFacet> getTestFacetResultDetail(Long resultId);
}
