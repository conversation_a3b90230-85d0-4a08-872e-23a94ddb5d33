package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookTestPaper;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 教材跟试卷关系Service接口
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
public interface IDtbBookTestPaperService extends IService<DtbBookTestPaper> {
    /**
     * 查询教材跟试卷关系
     *
     * @param bookPaperId 教材跟试卷关系主键
     * @return 教材跟试卷关系
     */
    public DtbBookTestPaper selectDtbBookTestPaperByBookPaperId(Long bookPaperId);

    /**
     * 查询教材跟试卷关系
     *
     * @param bookPaperId 教材跟试卷关系主键
     * @return 教材跟试卷关系
     */
    public AjaxResult getPaperAnswer(DtbBookTestPaper dtbBookTestPaper);

    /**
     * 查询教材跟试卷关系列表
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 教材跟试卷关系集合
     */
    public List<DtbBookTestPaper> selectDtbBookTestPaperList(DtbBookTestPaper dtbBookTestPaper);

    /**
     * 新增教材跟试卷关系
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 结果
     */
    public boolean addPaper(DtbBookTestPaper dtbBookTestPaper);

    /**
     * 考试保存接口
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 结果
     */
    public AjaxResult insertDtbBookTestPaper(DtbBookTestPaper dtbBookTestPaper);

    /**
     * 获取考试和作业列表 按章节id筛选
     *
     * @return 结果
     */
    public AjaxResult getPaperList(DtbBookTestPaper dtbBookTestPaper);

    /**
     * 修改教材跟试卷关系
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 结果
     */
    public boolean updateDtbBookTestPaper(DtbBookTestPaper dtbBookTestPaper);

    /**
     * 批量删除教材跟试卷关系
     *
     * @param bookPaperIds 需要删除的教材跟试卷关系主键集合
     * @return 结果
     */
    public boolean deleteDtbBookTestPaperByBookPaperIds(List<Long> bookPaperIds);

}
