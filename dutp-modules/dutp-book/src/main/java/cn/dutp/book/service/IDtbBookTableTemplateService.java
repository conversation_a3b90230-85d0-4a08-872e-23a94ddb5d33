package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookTableTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 数字教材表格模板Service接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface IDtbBookTableTemplateService extends IService<DtbBookTableTemplate>
{

    /**
     * 查询数字教材表格模板列表
     *
     * @param dtbBookTableTemplate 数字教材表格模板
     * @return 数字教材表格模板集合
     */
    public List<DtbBookTableTemplate> selectDtbBookTableTemplateList(DtbBookTableTemplate dtbBookTableTemplate);


}
