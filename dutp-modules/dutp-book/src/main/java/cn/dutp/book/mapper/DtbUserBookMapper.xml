<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookMapper">

    <resultMap type="DtbUserBook" id="DtbUserBookResult">
        <result property="userBookId"    column="user_book_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="versionId"    column="version_id"    />
        <result property="bookTypeId"    column="book_type_id"    />
        <result property="addWay"    column="add_way"    />
        <result property="readRate"    column="read_rate"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="lastSeeDate"    column="last_see_date"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <resultMap type="DtbUserBookVo" id="DtbUserBookVoResult">
        <result property="userBookId"    column="user_book_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="versionId"    column="version_id"    />
        <result property="bookTypeId"    column="book_type_id"    />
        <result property="addWay"    column="add_way"    />
        <result property="readRate"    column="read_rate"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="lastSeeDate"    column="last_see_date"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="bookName"    column="book_name"    />
        <result property="cover"    column="cover"    />
        <result property="shelfState"    column="shelf_state"    />
        <result property="currentVersionId"    column="current_version_id"    />
        <result property="lastVersionId"    column="last_version_id"    />
    </resultMap>
    <sql id="selectDtbUserBookVo">
        select user_book_id, user_id, book_id, version_id, book_type_id, add_way, read_rate, expire_date, last_see_date, sort, create_by, create_time, update_by, update_time, del_flag from dtb_user_book
    </sql>

    <select id="selectDtbUserBookList" parameterType="DtbUserBook" resultMap="DtbUserBookResult">
        <include refid="selectDtbUserBookVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="bootTypeId != null "> and boot_type_id = #{bootTypeId}</if>
            <if test="addWay != null "> and add_way = #{addWay}</if>
            <if test="readRate != null "> and read_rate = #{readRate}</if>
            <if test="expireDate != null "> and expire_date = #{expireDate}</if>
            <if test="lastSeeDate != null "> and last_see_date = #{lastSeeDate}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDtbUserBookByUserBookId" parameterType="Long" resultMap="DtbUserBookResult">
        <include refid="selectDtbUserBookVo"/>
        where user_book_id = #{userBookId}
    </select>

    <insert id="insertDtbUserBook" parameterType="DtbUserBook" useGeneratedKeys="true" keyProperty="userBookId">
        insert into dtb_user_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="bootTypeId != null">boot_type_id,</if>
            <if test="addWay != null">add_way,</if>
            <if test="readRate != null">read_rate,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="lastSeeDate != null">last_see_date,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="bootTypeId != null">#{bootTypeId},</if>
            <if test="addWay != null">#{addWay},</if>
            <if test="readRate != null">#{readRate},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="lastSeeDate != null">#{lastSeeDate},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBook" parameterType="DtbUserBook">
        update dtb_user_book
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="bootTypeId != null">boot_type_id = #{bootTypeId},</if>
            <if test="addWay != null">add_way = #{addWay},</if>
            <if test="readRate != null">read_rate = #{readRate},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="lastSeeDate != null">last_see_date = #{lastSeeDate},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where user_book_id = #{userBookId}
    </update>

    <delete id="deleteDtbUserBookByUserBookId" parameterType="Long">
        delete from dtb_user_book where user_book_id = #{userBookId}
    </delete>

    <delete id="deleteDtbUserBookByUserBookIds" parameterType="String">
        delete from dtb_user_book where user_book_id in 
        <foreach item="userBookId" collection="array" open="(" separator="," close=")">
            #{userBookId}
        </foreach>
    </delete>
    <select id="getInfoEducation" parameterType="DtbUserBook" resultMap="DtbUserBookVoResult">
        SELECT
        dub.user_book_id,
        dub.user_id,
        dub.book_id,
        dub.book_type_id,
        dub.add_way,
        dub.read_rate,
        dub.expire_date,
        dub.last_see_date,
        dub.sort,
        dub.version_id,
        dub.create_time,
        db.book_name,
        db.cover,
        db.shelf_state,
        db.current_version_id
        FROM
        dtb_user_book dub LEFT JOIN dtb_book db
        ON dub.book_id = db.book_id
        <where>
            dub.del_flag = '0'
            <if test="userId != null "> and dub.user_id = #{userId}</if>
            <if test="bookId != null "> and dub.book_id = #{bookId}</if>
            <if test="addWay != null "> and dub.add_way = #{addWay}</if>
        </where>
        ORDER BY dub.sort
    </select>

    <select id="selectUserCountByType" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ub.user_id)
        FROM dtb_user_book ub
        INNER JOIN dutp_user u ON u.user_id = ub.user_id
        <where>
            u.user_type = #{userType}
            AND ub.book_id = #{book.bookId}

            <if test="schoolId != null">
                AND EXISTS (SELECT 1 FROM dutp_user su WHERE su.user_id = ub.user_id AND su.school_id = #{schoolId})
            </if>
        </where>
    </select>
    <select id="selectByBookIdAndSchoolId" resultType="cn.dutp.book.domain.DtbUserBook">
        select
            dub.user_book_id
        from
            dtb_user_book dub
        left join
            edu_school_book esb on esb.book_id = dub.book_id
        left join
            dutp_user du on du.user_id = dub.user_id and du.school_id = esb.school_id and du.del_flag = 0
        where
            du.school_id = #{schoolId} and dub.book_id = #{bookId}
    </select>
</mapper>