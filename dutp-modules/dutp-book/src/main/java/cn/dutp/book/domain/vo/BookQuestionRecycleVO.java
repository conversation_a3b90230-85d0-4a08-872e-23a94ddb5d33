package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 习题回收站列表VO对象
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Data
public class BookQuestionRecycleVO {
    
    /** 习题ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookQuestionId;

    /** 题目类型 */
    private Integer questionType;

    /** 题目内容 */
    private String questionContent;

    /** 所属目录名称 */
    private String folderName;

    /** 删除时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 教材ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 章节ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /** 文件夹ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;
} 