<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookPublishProcessMapper">

    <resultMap type="cn.dutp.book.domain.DtbBookPublishProcess" id="DtbBookPublishProcessResult">
        <result property="processId" column="process_id"/>
        <result property="bookId" column="book_id"/>
        <result property="stepId" column="step_id"/>
        <result property="state" column="state"/>
        <result property="reason" column="reason"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDtbBookPublishProcess">
        select process_id,
               book_id,
               step_id,
               user_id,
               state,
               reason,
               create_by,
               create_time,
               update_by,
               update_time
        from dtb_book_publish_process
    </sql>

    <!--待办列表-->
    <select id="getBackupList" resultType="cn.dutp.book.domain.DtbBookPublishProcess">
        WITH RankedProcesses AS (
            SELECT
                book.book_id AS 'bookId',
                book.book_no AS 'bookNo',
                book.book_name AS 'bookName',
                book.book_organize AS 'bookOrganize',
                book.isbn,
                book.issn,
                book.master_flag AS 'masterFlag',
                book.master_book_id AS 'masterBookId',
                book.create_by AS 'createBy',
                bv.version_no AS 'versionNo',
                bv.version_id AS 'versionId',
                bps.step_id AS 'stepId',
                bps.step_name AS 'stepName',
                bpp.process_id AS 'processId',
                bpp.state,
                bpp.deal_user_id AS 'dealUserId',
                book.record_no AS 'recordNo',
                su.nick_name AS 'dealUserName',
                bpp.create_time AS 'createTime',
                bpau.user_id AS 'userId',
                bpau.audit_user_id AS 'auditUserId',
                ROW_NUMBER() OVER (PARTITION BY book.book_id ORDER BY bpp.create_time DESC) AS rn
            FROM
            dtb_book book
                INNER JOIN dtb_book_version bv ON book.last_version_id = bv.version_id
                INNER JOIN dtb_book_publish_step bps ON book.current_step_id = bps.step_id
                INNER JOIN dtb_book_publish_process bpp ON book.book_id = bpp.book_id
                INNER JOIN dtb_book_publish_process_audit_user bpau ON bpp.process_id = bpau.process_id
                <if test="isAdmin == false">
                    and (bpau.user_id = #{userId} or bpau.handle_user_id = #{userId})
                </if>
                LEFT JOIN sys_user su ON bpp.deal_user_id = su.user_id
            WHERE book.del_flag = '0'
            <if test="param.bookOrganize != null and param.bookOrganize != ''">
                and book.book_organize = #{param.bookOrganize}
            </if>
            <if test="param.bookName != null and param.bookName != ''">
                and book.book_name LIKE CONCAT('%',#{param.bookName},'%')
            </if>
            <if test="param.bookNo != null and param.bookNo != ''">
                and book.book_no LIKE CONCAT('%',#{param.bookNo},'%')
            </if>
            <if test="param.isbn != null and param.isbn != ''">
                and book.isbn LIKE CONCAT('%',#{param.isbn},'%')
            </if>
            <if test="param.issn != null and param.issn != ''">
                and book.issn LIKE CONCAT('%',#{param.issn},'%')
            </if>
            <if test="param.masterFlag != null and param.masterFlag != ''">
                and book.master_flag = #{param.masterFlag}
            </if>
            <if test="param.stepId != null and param.stepId != ''">
                and bps.step_id = #{param.stepId}
            </if>
            <if test="param.state != null and param.state != ''">
                and bpp.state = #{param.state}
            </if>
            order by bpp.create_time desc
        )
        SELECT *  FROM RankedProcesses
        WHERE rn = 1
    </select>

    <select id="getProcessDetail" resultType="cn.dutp.book.domain.DtbBookPublishProcess">
        select bps.step_id   as rootStepId,
               bps.step_name as rootStepName,
               su.nick_name as 'handleUserName',
               sd.dept_name as 'deptName',
               t.processId,
               t.additionFlag,
               t.versionId,
               t.stepId,
               t.state,
               t.processDate,
               t.handleUserId,
               t.reason
        from dtb_book_publish_step bps
                 left join (select  bpp.process_id as 'processId',
                                    bpp.addition_flag as 'additionFlag',
                                    bpp.version_id as 'versionId',
                                    bpp.step_id as 'stepId',
                                    bpp.state as 'state',
                                    bpp.process_date as 'processDate',
                                    COALESCE(bpau.handle_user_id,bpau.user_id,null) as 'handleUserId',
                                    bpp.reason as 'reason'
                            from dtb_book_publish_process bpp
                                     inner join dtb_book_publish_process_audit_user bpau
                                                on bpp.process_id = bpau.process_id
                            where bpp.book_id = #{param.bookId}) t on bps.step_id = t.stepId
                 left join sys_user su on t.handleUserId = su.user_id
                 left join sys_dept sd on su.dept_id = sd.dept_id
        WHERE  1 = 1
        <if test="param.bookOrganize == 2">
            AND bps.school_flag = 1
        </if>
        order by bps.step_id asc
    </select>

    <select id="queryBookLastProcess" resultType="cn.dutp.book.domain.DtbBookPublishProcess">
        SELECT b.book_id,
               b.book_no,
               b.book_name,
               p.step_id,
               b.current_version_id as version_id,
               p.process_id,
               p.state
        FROM dtb_book b
                 left JOIN dtb_book_publish_process p ON b.book_id = p.book_id
            AND p.version_id = b.current_version_id
        WHERE b.del_flag = '0'
          and b.book_id = #{bookId}
        ORDER BY p.create_time DESC LIMIT 1
    </select>
    <select id="getPrevProcessInfo" resultType="cn.dutp.book.domain.DtbBookPublishProcess">
        select bps.step_id,
               bps.step_name
        from dtb_book_publish_process bpp
                 inner join dtb_book_publish_step bps on bpp.step_id = bps.step_id
        where bpp.book_id = #{bookId} and bpp.state = 2 and bpp.addition_flag = 0  and bps.step_id &lt; #{stepId}
        order by bpp.create_time desc limit 1
    </select>

    <select id="getProcessInfoLink" resultType="cn.dutp.book.domain.DtbBookPublishProcess">
        select
            bpp.step_id,
            bpp.book_id,
            bpp.process_id,
            bb.master_flag,
            bb.book_organize,
            dau.audit_user_id
        from
            dtb_book_publish_process bpp
        left join
            dtb_book bb on bpp.book_id = bb.book_id and bb.del_flag = '0'
        left join
            dtb_book_publish_process_audit_user dau on dau.process_id = bpp.process_id
        where
            bpp.process_id = #{processId}
    </select>
    <select id="selectProcessChapters" resultType="cn.dutp.book.domain.DtbBookPublishProcess">
        select
            dbppc.chapter_id,
            dbppc.from_process_id,
            dbppc.to_process_id,
            step.step_name
        from dtb_book_publish_process_chapter dbppc
        left join dtb_book_publish_process dbpp on dbppc.from_process_id = dbpp.process_id
        left join dtb_book_publish_step step on dbpp.step_id = step.step_id
        where dbppc.to_process_id = #{processId}
    </select>
</mapper>