package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.vo.DtbBookShareCommentAttachmentVO;
import cn.dutp.book.domain.vo.DtbUserBookNoteAttachmentVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookShareCommentAttachment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 教材分享点评附件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Repository
public interface DtbBookShareCommentAttachmentMapper extends BaseMapper<DtbBookShareCommentAttachment>
{
    @Select("SELECT " +
            "attachment_id," +
            "comment_id," +
            "attachment_type," +
            "attachment_url," +
            "attachment_size," +
            "attachment_name " +
            "FROM " +
            "dtb_book_share_comment_attachment " +
            "WHERE " +
            "1 = 1 " +
            "AND comment_id = #{commentId}")
    List<DtbBookShareCommentAttachmentVO> selectBookShareCommentAttachmentList(@Param("commentId") Long commentId);
}
