package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.DtbBookChapterResource;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.DtbBookResourceTreeVO;
import cn.dutp.book.domain.vo.DtbUserQuestionVO;
import cn.dutp.book.domain.vo.DtbBookResourceVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 教材资源Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Repository
public interface DtbBookResourceMapper extends BaseMapper<DtbBookResource>
{
    List<DtbBookResourceVO> getReaderBookResource(ReaderCommonForm readerCommonForm);

    List<DtbUserQuestionVO> getReaderBookQuestion(ReaderCommonForm readerCommonForm);

    List<DtbBookResource> queryBookResource(@Param("bookId") Long bookId, @Param("folderId")Long folderId);

    List<DtbBookChapterResource> getReaderBookResourceByChapter(@Param("param") ReaderCommonForm readerCommonForm);
}
