package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbUserBookMark;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * DUTP-DTB_021书签Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IDtbUserBookMarkService extends IService<DtbUserBookMark>
{
    /**
     * 查询DUTP-DTB_021书签
     *
     * @param markId DUTP-DTB_021书签主键
     * @return DUTP-DTB_021书签
     */
    public DtbUserBookMark selectDtbUserBookMarkByMarkId(Long markId);

    /**
     * 查询DUTP-DTB_021书签列表
     *
     * @param dtbUserBookMark DUTP-DTB_021书签
     * @return DUTP-DTB_021书签集合
     */
    public List<DtbUserBookMark> selectDtbUserBookMarkList(DtbUserBookMark dtbUserBookMark);

    /**
     * 新增DUTP-DTB_021书签
     *
     * @param dtbUserBookMark DUTP-DTB_021书签
     * @return 结果
     */
    public AjaxResult insertDtbUserBookMark(DtbUserBookMark dtbUserBookMark);

    /**
     * 修改DUTP-DTB_021书签
     *
     * @param dtbUserBookMark DUTP-DTB_021书签
     * @return 结果
     */
    public boolean updateDtbUserBookMark(DtbUserBookMark dtbUserBookMark);

    /**
     * 批量删除DUTP-DTB_021书签
     *
     * @param markIds 需要删除的DUTP-DTB_021书签主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookMarkByMarkIds(List<Long> markIds);


    void deleteDtbUserBookMarkByPage(Integer pageNumber, Long chapterId);

    AjaxResult selectReaderUserBookMarkList(DtbUserBookMark dtbUserBookMark);

    AjaxResult deleteBookMark(Long markId);
}
