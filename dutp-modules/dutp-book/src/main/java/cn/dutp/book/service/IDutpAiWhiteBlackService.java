package cn.dutp.book.service;

import cn.dutp.book.domain.DutpAiWhiteBlack;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * ai黑白名单Service接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
public interface IDutpAiWhiteBlackService extends IService<DutpAiWhiteBlack>
{
    /**
     * 查询ai黑白名单
     *
     * @param listId ai黑白名单主键
     * @return ai黑白名单
     */
    public DutpAiWhiteBlack selectDutpAiWhiteBlackByListId(Long listId);

    /**
     * 查询ai黑白名单列表
     *
     * @param dutpAiWhiteBlack ai黑白名单
     * @return ai黑白名单集合
     */
    public List<DutpAiWhiteBlack> selectDutpAiWhiteBlackList(DutpAiWhiteBlack dutpAiWhiteBlack);

    /**
     * 新增ai黑白名单
     *
     * @param dutpAiWhiteBlack ai黑白名单
     * @return 结果
     */
    public boolean insertDutpAiWhiteBlack(DutpAiWhiteBlack dutpAiWhiteBlack);

    /**
     * 修改ai黑白名单
     *
     * @param dutpAiWhiteBlack ai黑白名单
     * @return 结果
     */
    public boolean updateDutpAiWhiteBlack(DutpAiWhiteBlack dutpAiWhiteBlack);

    /**
     * 批量删除ai黑白名单
     *
     * @param listIds 需要删除的ai黑白名单主键集合
     * @return 结果
     */
    public boolean deleteDutpAiWhiteBlackByListIds(List<Long> listIds);

    /**
     * 根据业务查询ai黑白名单
     *
     * @param type ai黑白名单主键
     * @return ai黑白名单
     */
    public DutpAiWhiteBlack selectDutpAiWhiteBlackByType(Integer type);

    /**
     * 根据编码查询ai黑白名单
     *
     * @param sid ai黑白名单主键
     * @return ai黑白名单
     */
    public DutpAiWhiteBlack selectDutpAiWhiteBlackByUid(String sid);

}
