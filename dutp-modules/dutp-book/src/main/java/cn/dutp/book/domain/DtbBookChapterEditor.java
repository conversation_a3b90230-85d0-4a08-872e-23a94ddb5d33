package cn.dutp.book.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 数字教材章节编辑对应关系对象 dtb_book_chapter_editor
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@TableName("dtb_book_chapter_editor")
public class DtbBookChapterEditor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterEditorId;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 1编写者2查看者
     */
    private Integer roleType;
}
