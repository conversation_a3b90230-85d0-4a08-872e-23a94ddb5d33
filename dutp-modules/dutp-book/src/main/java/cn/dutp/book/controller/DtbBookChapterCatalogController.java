package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookChapterCatalog;
import cn.dutp.book.service.IDtbBookChapterCatalogService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教材章节目录Controller
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@RestController
@RequestMapping("/catalog")
public class DtbBookChapterCatalogController extends BaseController
{
    @Autowired
    private IDtbBookChapterCatalogService dtbBookChapterCatalogService;

/**
 * 查询教材章节目录列表
 */
@GetMapping("/list")
    public TableDataInfo list(DtbBookChapterCatalog dtbBookChapterCatalog)
    {
        startPage();
        List<DtbBookChapterCatalog> list = dtbBookChapterCatalogService.selectDtbBookChapterCatalogList(dtbBookChapterCatalog);
        return getDataTable(list);
    }

    /**
     * 导出教材章节目录列表
     */
    @RequiresPermissions("book:catalog:export")
    @Log(title = "教材章节目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookChapterCatalog dtbBookChapterCatalog)
    {
        List<DtbBookChapterCatalog> list = dtbBookChapterCatalogService.selectDtbBookChapterCatalogList(dtbBookChapterCatalog);
        ExcelUtil<DtbBookChapterCatalog> util = new ExcelUtil<DtbBookChapterCatalog>(DtbBookChapterCatalog.class);
        util.exportExcel(response, list, "教材章节目录数据");
    }

    /**
     * 获取教材章节目录详细信息
     */
    @RequiresPermissions("book:catalog:query")
    @GetMapping(value = "/{catalogId}")
    public AjaxResult getInfo(@PathVariable("catalogId") Long catalogId)
    {
        return success(dtbBookChapterCatalogService.selectDtbBookChapterCatalogByCatalogId(catalogId));
    }

    /**
     * 新增教材章节目录
     */
    @RequiresPermissions("book:catalog:add")
    @Log(title = "教材章节目录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookChapterCatalog dtbBookChapterCatalog)
    {
        return toAjax(dtbBookChapterCatalogService.insertDtbBookChapterCatalog(dtbBookChapterCatalog));
    }

    /**
     * 修改教材章节目录
     */
    @RequiresPermissions("book:catalog:edit")
    @Log(title = "教材章节目录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookChapterCatalog dtbBookChapterCatalog)
    {
        return toAjax(dtbBookChapterCatalogService.updateDtbBookChapterCatalog(dtbBookChapterCatalog));
    }

    /**
     * 删除教材章节目录
     */
    @RequiresPermissions("book:catalog:remove")
    @Log(title = "教材章节目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{catalogIds}")
    public AjaxResult remove(@PathVariable Long[] catalogIds)
    {
        return toAjax(dtbBookChapterCatalogService.deleteDtbBookChapterCatalogByCatalogIds(Arrays.asList(catalogIds)));
    }
}
