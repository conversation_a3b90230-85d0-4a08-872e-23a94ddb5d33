package cn.dutp.book.controller;

import cn.dutp.book.domain.LearningStatisticsVo;
import cn.dutp.book.service.IDtbBookPurchaseCodeService;
import cn.dutp.book.service.IDtbBookService;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.InnerAuth;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.system.api.domain.DutpUser;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * DUTP-DTB_002数字教材Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("/book")
public class DtbBookController extends BaseController {
    @Autowired
    private IDtbBookService dtbBookService;

    // 发行管理接口
    @Autowired
    private IDtbBookPurchaseCodeService dtbBookPurchaseCodeService;

    /**
     * 查询DUTP-DTB_002数字教材列表 后台管理使用
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBook dtbBook) {
        startPage();
        List<DtbBook> list = dtbBookService.selectDtbBookList(dtbBook);
        return getDataTable(list);
    }

    /**
     * 查询DUTP-DTB_002数字教材列表 作者编辑端使用
     */
    @GetMapping("/listOfAuthorAndEditor")
    public TableDataInfo listOfAuthorAndEditor(DtbBook dtbBook) {
        startPage();
        dtbBook.setUserId(SecurityUtils.getUserId());
        List<DtbBook> list = dtbBookService.listOfAuthorAndEditor(dtbBook);
        return getDataTable(list);
    }

    /**
     * 查询数字教材列表 移动端获取缓存书籍用
     */
    @GetMapping("/getListByBookIds")
    public AjaxResult getListByBookIds(@RequestParam List<Long> bookIds) {
        List<DtbBook> list = dtbBookService.getListByBookIds(bookIds);
        return success(list);
    }

    @GetMapping("/listForResource")
    public TableDataInfo listForResource(DtbBook dtbBook) {
        startPage();
        List<DtbBook> list = dtbBookService.selectDtbBookListWithGroup(dtbBook);
        return getDataTable(list);
    }

    /**
     * 查询数字教材
     */
    @GetMapping("/searchOne")
    public AjaxResult searchOne(DtbBook dtbBook) {
        DtbBook book = dtbBookService.searchOne(dtbBook);
        return success(book);
    }

    /**
     * 查询数字教材题注样式
     */
    @GetMapping("/queryCaptionStyle")
    public AjaxResult queryCaptionStyle(DtbBook dtbBook) {
        DtbBook book = dtbBookService.queryCaptionStyle(dtbBook);
        return success(book);
    }

    /**
     * 修改数字教材题注
     */
    @RequiresPermissions("book:book:captionStyle")
    @Log(title = "修改数字教材题注", businessType = BusinessType.UPDATE)
    @PutMapping("/editCaptionStyle")
    public AjaxResult editCaptionStyle(@RequestBody DtbBook dtbBook) {
        return toAjax(dtbBookService.editCaptionStyle(dtbBook));
    }

    /**
     * 定稿提交
     */
    @RequiresPermissions("book:book:finalized")
    @Log(title = "定稿提交", businessType = BusinessType.UPDATE)
    @PutMapping("/finalizedSubmit")
    public AjaxResult finalizedSubmit(@RequestBody DtbBook dtbBook) {
        return toAjax(dtbBookService.finalizedSubmit(dtbBook));
    }

    /**
     * 查询数字教材简介
     */
    @GetMapping("/queryProfile")
    public AjaxResult queryProfile(DtbBook dtbBook) {
        DtbBook book = dtbBookService.queryProfile(dtbBook);
        return success(book);
    }

    /**
     * 导出数字教材列表
     */
    @RequiresPermissions("book:book:export")
    @Log(title = "导出数字教材", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody DtbBook dtbBook) {
        // 导出功能待完善
        dtbBookService.exportBook(dtbBook);
    }

    /**
     * 获取DUTP-DTB_002数字教材详细信息
     */
    @GetMapping(value = "/{bookId}")
    public AjaxResult getInfo(@PathVariable("bookId") Long bookId) {
        return success(dtbBookService.selectDtbBookByBookId(bookId));
    }

    /**
     * 新增数字教材
     */
    @RequiresPermissions("book:book:add")
    @Log(title = "新增数字教材", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBook dtbBook) {
        Long bookId = dtbBookService.insertDtbBook(dtbBook);
        if (ObjectUtil.isEmpty(bookId)) {
            return error("新增失败");
        }
        return AjaxResult.success("新增成功", String.valueOf(bookId));
    }

    /**
     * 修改数字教材
     */
    @RequiresPermissions("book:book:edit")
    @Log(title = "修改数字教材", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBook dtbBook) {
        return toAjax(dtbBookService.updateDtbBook(dtbBook));
    }

    /**
     * 删除数字教材
     */
    @RequiresPermissions("book:book:remove")
    @Log(title = "删除数字教材", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bookId}")
    public AjaxResult remove(@PathVariable Long bookId) {
        return toAjax(dtbBookService.deleteDtbBookByBookId(bookId));
    }

    /**
     * 复制数字教材
     */
    @RequiresPermissions("book:book:copy")
    @Log(title = "复制数字教材", businessType = BusinessType.UPDATE)
    @PutMapping("/copy")
    public AjaxResult copy(@RequestBody DtbBook dtbBook) {
        return toAjax(dtbBookService.copyDtbBook(dtbBook));
    }

    /**
     * 修正数字教材
     */
    @RequiresPermissions("book:book:amend")
    @Log(title = "修正数字教材", businessType = BusinessType.UPDATE)
    @PutMapping("/amend")
    public AjaxResult amend(@RequestBody DtbBook dtbBook) {
        return toAjax(dtbBookService.amendDtbBook(dtbBook));
    }

    /**
     * 更新数字教材封面
     */
    @Log(title = "更新数字教材封面", businessType = BusinessType.UPDATE)
    @PutMapping("/updateCover")
    public AjaxResult updateCover(@RequestBody DtbBook dtbBook) {
        DtbBook book = new DtbBook();
        book.setBookId(dtbBook.getBookId());
        book.setCover(dtbBook.getCover());
        return toAjax(dtbBookService.updateById(book));
    }

    /**
     * 根据bookId 查询出所有副教材 （发型管理)
     */
    @GetMapping("/listBookNature")
    public TableDataInfo listBookNature(@RequestParam List<Long> bookIds) {
        List<DtbBook> list = dtbBookService.listBookNature(bookIds);
        return getDataTable(list);
    }


    /**
     * 召回数字教材
     */
    @RequiresPermissions("book:purchaseCode:recall")
    @Log(title = "召回数字教材", businessType = BusinessType.UPDATE)
    @PostMapping("/recall")
    public AjaxResult recall(@RequestBody DtbBook dtbBook) {
        return toAjax(dtbBookService.recallDtbBook(dtbBook));
    }

    /**
     * 教材上下架
     */
    @Log(title = "上下架数字教材", businessType = BusinessType.UPDATE)
    @PostMapping("/toggleShelf")
    public AjaxResult toggleShelf(@RequestBody DtbBook dtbBook) {
        return toAjax(dtbBookService.shelfDtbBook(dtbBook));
    }

    /**
     * 教材批量上下架（电商中心教材管理用）
     */
    @RequiresPermissions("book:book:toggleBatchShelf")
    @Log(title = "批量上下架数字教材", businessType = BusinessType.UPDATE)
    @PostMapping("/toggleBatchShelf")
    public AjaxResult toggleBatchShelf(@RequestBody DtbBook dtbBook)
    {
        dtbBookService.toggleBatchShelf(dtbBook);
        return AjaxResult.success();
    }

    /**
     * 导入数字教材章节内容
     */
    // @RequiresPermissions("book:book:importBook")
    @Log(title = "导入数字教材章节内容", businessType = BusinessType.IMPORT)
    @PostMapping("/importBook")
    public AjaxResult importBook(MultipartFile file, Long bookId) {

        if (ObjectUtil.isEmpty(file) || ObjectUtil.isEmpty(bookId)) {
            throw new ServiceException("文件或者bookId为空");
        }
        String fileName = file.getOriginalFilename();
        if (fileName.endsWith(".docx")) {
            return toAjax(dtbBookService.importBook(file, bookId));
        } else if (fileName.endsWith(".pdf")) {
            return toAjax(dtbBookService.importChapterPdf(file, bookId));
        }else {
            throw new ServiceException("文件格式不正确，请上传.docx或者.pdf文件");
        }
    }

    /**
     * 学习统计查询
     */
    @PostMapping("/getLearningStatistics")
    public R<LearningStatisticsVo> getLearningStatistics()
    {
        return R.ok(dtbBookService.getLearningStatistics());
    }
}
