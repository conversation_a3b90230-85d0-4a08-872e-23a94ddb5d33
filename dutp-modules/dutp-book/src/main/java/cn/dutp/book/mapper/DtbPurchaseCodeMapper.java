package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.domain.DtbBookPurchaseCode;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 发行管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Repository
public interface DtbPurchaseCodeMapper extends BaseMapper<DtbBookPurchaseCode> {

    List<DtbBookPurchaseCode> selectBookCodeList(@Param("param") DtbBookPurchaseCode dtbBookPurchaseCode);
    List<DtbBookPurchaseCode> selectBookCodeMasterFlagList(@Param("param") DtbBookPurchaseCode dtbBookPurchaseCode);

    /**
     * 查看购书吗页面的购书码列表
     * @param dtbBookPurchaseCode
     * @return
     */
    List<DtbBookPurchaseCode> selectCodeList(@Param("param") DtbBookPurchaseCode dtbBookPurchaseCode);

}
