package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookPublishProcess;
import cn.dutp.book.domain.vo.DtbBookPublishProcessVO;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】Service接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface IDtbBookPublishProcessService extends IService<DtbBookPublishProcess> {
    /**
     * 查询DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param processId DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】主键
     * @return DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     */
    public DtbBookPublishProcess selectDtbBookPublishProcessByProcessId(Long processId);

    /**
     * 查询DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】列表
     *
     * @param dtbBookPublishProcess DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     * @return DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】集合
     */
    public List<DtbBookPublishProcess> selectDtbBookPublishProcessList(DtbBookPublishProcess dtbBookPublishProcess);

    /**
     * 新增DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param dtbBookPublishProcess DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     * @return 结果
     */
    public boolean insertDtbBookPublishProcess(DtbBookPublishProcess dtbBookPublishProcess);


    /**
     * 节点审批 -- 补录保存
     */
    public boolean addProcessAddition(DtbBookPublishProcess dtbBookPublishProcess);

    public boolean editProcessAddition(DtbBookPublishProcess dtbBookPublishProcess);

    /**
     * 节点审批 -- 补录详情
     */
    public DtbBookPublishProcess getProcessAddition(DtbBookPublishProcess dtbBookPublishProcess);

    /**
     * 修改DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param dtbBookPublishProcess DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     * @return 结果
     */
    public boolean updateDtbBookPublishProcess(DtbBookPublishProcess dtbBookPublishProcess);

    /**
     * 批量删除DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param processIds 需要删除的DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】主键集合
     * @return 结果
     */
    public boolean deleteDtbBookPublishProcessByProcessIds(List<Long> processIds);

    /**
     * 获取审批详情
     */
    public List<DtbBookPublishProcessVO> processDataList(DtbBookPublishProcess dtbBookPublishProcess);

    /**
     * 获取上次流程的节点信息
     */
    public DtbBookPublishProcess getPrevProcessInfo(DtbBookPublishProcess dtbBookPublishProcess);


    DtbBookPublishProcess getProcessInfoLink(Long processId);

    AjaxResult updateChapterBackup(DtbBookPublishProcess dtbBookPublishProcess);

    /**
     * 获取当前流程被驳回的章节
     */
    AjaxResult getProcessChapters(Long processId);

    List<DtbBookPublishProcess> getProcessChaptersByBookId(Long bookId);
}
