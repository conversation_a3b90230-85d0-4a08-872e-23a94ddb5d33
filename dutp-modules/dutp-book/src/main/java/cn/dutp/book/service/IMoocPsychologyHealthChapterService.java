package cn.dutp.book.service;

import java.util.List;

import cn.dutp.book.domain.MoocPsychologyHealthChapter;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 心理健康量与章节关系Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IMoocPsychologyHealthChapterService extends IService<MoocPsychologyHealthChapter>
{
    /**
     * 新增心理健康量与章节关系
     *
     * @param moocPsychologyHealthChapter 心理健康量与章节关系
     * @return 结果
     */
    public boolean insertMoocPsychologyHealthChapter(MoocPsychologyHealthChapter moocPsychologyHealthChapter);

    /**
     * 批量删除心理健康量与章节关系
     *
     * @param healthChapterIds 需要删除的心理健康量与章节关系主键集合
     * @return 结果
     */
    public boolean deleteMoocPsychologyHealthChapterByHealthChapterIds(List<Long> healthChapterIds);
}
