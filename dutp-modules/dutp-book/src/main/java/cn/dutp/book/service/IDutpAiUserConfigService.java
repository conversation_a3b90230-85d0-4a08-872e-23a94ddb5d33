package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DutpAiUserConfig;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface IDutpAiUserConfigService extends IService<DutpAiUserConfig>
{
    /**
     * 查询【请填写功能名称】
     *
     * @param aiConfigId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DutpAiUserConfig selectDutpAiUserConfigByAiConfigId(Long aiConfigId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dutpAiUserConfig 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DutpAiUserConfig> selectDutpAiUserConfigList(DutpAiUserConfig dutpAiUserConfig);

    /**
     * 新增【请填写功能名称】
     *
     * @param dutpAiUserConfig 【请填写功能名称】
     * @return 结果
     */
    public boolean insertDutpAiUserConfig(DutpAiUserConfig dutpAiUserConfig);

    /**
     * 修改【请填写功能名称】
     *
     * @param dutpAiUserConfig 【请填写功能名称】
     * @return 结果
     */
    public boolean updateDutpAiUserConfig(DutpAiUserConfig dutpAiUserConfig);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param aiConfigIds 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public boolean deleteDutpAiUserConfigByAiConfigIds(List<Long> aiConfigIds);

}
