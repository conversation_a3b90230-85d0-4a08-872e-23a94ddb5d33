package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookPublishProcessChapter;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 流程章节关系表Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IDtbBookPublishProcessChapterService extends IService<DtbBookPublishProcessChapter>
{
    /**
     * 查询流程章节关系表
     *
     * @param processChapterId 流程章节关系表主键
     * @return 流程章节关系表
     */
    public DtbBookPublishProcessChapter selectDtbBookPublishProcessChapterByProcessChapterId(Long processChapterId);

    /**
     * 查询流程章节关系表列表
     *
     * @param dtbBookPublishProcessChapter 流程章节关系表
     * @return 流程章节关系表集合
     */
    public List<DtbBookPublishProcessChapter> selectDtbBookPublishProcessChapterList(DtbBookPublishProcessChapter dtbBookPublishProcessChapter);

    /**
     * 新增流程章节关系表
     *
     * @param dtbBookPublishProcessChapter 流程章节关系表
     * @return 结果
     */
    public boolean insertDtbBookPublishProcessChapter(DtbBookPublishProcessChapter dtbBookPublishProcessChapter);

    /**
     * 修改流程章节关系表
     *
     * @param dtbBookPublishProcessChapter 流程章节关系表
     * @return 结果
     */
    public boolean updateDtbBookPublishProcessChapter(DtbBookPublishProcessChapter dtbBookPublishProcessChapter);

    /**
     * 批量删除流程章节关系表
     *
     * @param processChapterIds 需要删除的流程章节关系表主键集合
     * @return 结果
     */
    public boolean deleteDtbBookPublishProcessChapterByProcessChapterIds(List<Long> processChapterIds);

    /**
     * 查询可以驳回的章节 下拉列表
     */
    List<DtbBookPublishProcessChapter> pulishChapterList(DtbBookPublishProcessChapter dtbBookPublishProcessChapter);

}
