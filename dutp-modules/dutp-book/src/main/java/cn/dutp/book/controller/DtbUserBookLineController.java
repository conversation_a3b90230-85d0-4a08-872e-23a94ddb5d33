package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbUserBookLine;
import cn.dutp.book.service.IDtbUserBookLineService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_020划线Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/book/line")
public class DtbUserBookLineController extends BaseController
{
    @Autowired
    private IDtbUserBookLineService dtbUserBookLineService;

    /**
     * 查询DUTP-DTB_020划线列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserBookLine dtbUserBookLine)
    {
        startPage();
        List<DtbUserBookLine> list = dtbUserBookLineService.selectDtbUserBookLineList(dtbUserBookLine);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_020划线列表
     */
    @RequiresPermissions("book:line:export")
    @Log(title = "导出DUTP-DTB_020划线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserBookLine dtbUserBookLine)
    {
        List<DtbUserBookLine> list = dtbUserBookLineService.selectDtbUserBookLineList(dtbUserBookLine);
        ExcelUtil<DtbUserBookLine> util = new ExcelUtil<DtbUserBookLine>(DtbUserBookLine.class);
        util.exportExcel(response, list, "DUTP-DTB_020划线数据");
    }

    /**
     * 获取DUTP-DTB_020划线详细信息
     */
    @RequiresPermissions("book:line:query")
    @GetMapping(value = "/{lineId}")
    public AjaxResult getInfo(@PathVariable("lineId") Long lineId)
    {
        return success(dtbUserBookLineService.selectDtbUserBookLineByLineId(lineId));
    }

    /**
     * 新增DUTP-DTB_020划线
     */
    @RequiresPermissions("book:line:add")
    @Log(title = "新增DUTP-DTB_020划线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBookLine dtbUserBookLine)
    {
        return dtbUserBookLineService.insertDtbUserBookLine(dtbUserBookLine);
    }

    /**
     * 修改DUTP-DTB_020划线
     */
    @RequiresPermissions("book:line:edit")
    @Log(title = "修改DUTP-DTB_020划线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBookLine dtbUserBookLine)
    {
        return toAjax(dtbUserBookLineService.updateDtbUserBookLine(dtbUserBookLine));
    }

    /**
     * 删除DUTP-DTB_020划线
     */
    @RequiresPermissions("book:line:remove")
    @Log(title = "删除DUTP-DTB_020划线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{lineIds}")
    public AjaxResult remove(@PathVariable Long[] lineIds)
    {
        return toAjax(dtbUserBookLineService.deleteDtbUserBookLineByLineIds(Arrays.asList(lineIds)));
    }
}
