package cn.dutp.book.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.dutp.book.domain.MoocPsychologyHealthScale;
import cn.dutp.book.domain.MoocPsychologyHealthScaleFacet;
import cn.dutp.book.domain.MoocPsychologyHealthScaleQuestion;
import cn.dutp.book.domain.MoocPsychologyHealthScaleQuestionOption;
import cn.dutp.book.domain.*;
import cn.dutp.book.domain.enums.PsychologyQuestionSortEnum;
import cn.dutp.book.mapper.*;
import cn.dutp.book.domain.vo.MoocPsychologyHealthScaleResultVo;
import cn.dutp.book.mapper.DtbBookMapper;
import cn.dutp.book.mapper.MoocPsychologyHealthChapterMapper;
import cn.dutp.book.service.*;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 心理测试量表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class MoocPsychologyHealthScaleServiceImpl extends ServiceImpl<MoocPsychologyHealthScaleMapper, MoocPsychologyHealthScale> implements IMoocPsychologyHealthScaleService {
    @Autowired
    private MoocPsychologyHealthScaleMapper moocPsychologyHealthScaleMapper;

    @Autowired
    private MoocPsychologyHealthScaleFacetServiceImpl facetServiceImpl;

    @Autowired
    private MoocPsychologyHealthScaleQuestionServiceImpl questionServiceImpl;

    @Autowired
    private MoocPsychologyHealthScaleQuestionOptionServiceImpl optionServiceImpl;

    @Autowired
    private MoocPsychologyHealthChapterMapper moocPsychologyHealthChapterMapper;

    @Autowired
    private MoocPsychologyHealthUserResultMapper moocPsychologyHealthUserResultMapper;
    @Autowired
    private MoocPsychologyHealthScaleQuestionMapper questionMapper;

    @Autowired
    private MoocPsychologyHealthUserResultMapper userResultMapper;

    @Autowired
    private IMoocPsychologyHealthTestAnswerService answerService;

    @Autowired
    private DtbBookMapper dtbBookMapper;
    @Autowired
    private MoocPsychologyHealthScaleFacetMapper moocPsychologyHealthScaleFacetMapper;

    @Autowired
    private MoocPsychologyHealthScaleQuestionMapper moocPsychologyHealthScaleQuestionMapper;

    @Autowired
    private MoocPsychologyHealthScaleQuestionOptionMapper moocPsychologyHealthScaleQuestionOptionMapper;

    @Autowired
    private MoocPsychologyHealthTestAnswerMapper moocPsychologyHealthTestAnswerMapper;

    @Autowired
    private IMoocPsychologyHealthInstructionsService moocPsychologyHealthInstructionsService;

    @Autowired
    private  MoocPsychologyHealthInstructionsMapper moocPsychologyHealthInstructionsMapper;

    @Override
    public void  addPsychologyHealth(MoocPsychologyHealthScale scale) {
        Long userId = SecurityUtils.getUserId();
        scale.setUserId(userId);
        //查询当前用户的量表
        List<String> scaleNameList = moocPsychologyHealthScaleMapper.selectScaleNameByUserId(userId);
        LambdaQueryWrapper<MoocPsychologyHealthScale> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MoocPsychologyHealthScale::getScaleName, scale.getScaleName());
        List<MoocPsychologyHealthScale> moocPsychologyHealthScales = moocPsychologyHealthScaleMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(moocPsychologyHealthScales) && scaleNameList.contains(scale.getScaleName())) {
            if (moocPsychologyHealthScales.size() > 0) {
                throw new ServiceException("量表名称不能重复");
            }
        }
        // 保存量表
        scale.setUserId(SecurityUtils.getUserId());
        this.save(scale);

        //保存十六种人格
        if (scale.getScaleType() == 3){
            Map<String, String> mbtiData = scale.getMbtiData();
            mbtiData.forEach((mbtiName, mbtiRemark) -> {
               MoocPsychologyHealthInstructions instructions = new MoocPsychologyHealthInstructions();
               instructions.setInstructionsName(mbtiName);
               instructions.setInstructionsRemark(mbtiRemark);
               instructions.setScaleId(scale.getScaleId());
                moocPsychologyHealthInstructionsService.save(instructions);
            });
        }
        if (scale.getQuestionSort() == PsychologyQuestionSortEnum.multi_dimension.getCode()) {
            // 保存维度
            List<MoocPsychologyHealthScaleFacet> dimensionList = scale.getMoocPsychologyHealthScaleFacet();
            dimensionList.stream().forEach(dimension -> {
                dimension.setScaleId(scale.getScaleId());
                dimension.setCreateBy(SecurityUtils.getUsername());
                dimension.setCreateTime(new Date());
            });
            facetServiceImpl.saveBatch(dimensionList);
            // 保存题目和选项
            dimensionList.stream().forEach(dimension -> {
                saveQuestionAndOption(dimension.getMoocPsychologyHealthScaleQuestion(), scale.getScaleId(), dimension.getFacetId());
            });
        } else {
            // 保存题目和选项
            saveQuestionAndOption(scale.getMoocPsychologyHealthScaleQuestion(), scale.getScaleId(), null);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editPsychologyHealth(MoocPsychologyHealthScale scale) {
        Long userId = SecurityUtils.getUserId();
        scale.setUserId(userId);
        //量表名称去重
        if (StringUtils.isNotBlank(scale.getScaleName())) {
            LambdaQueryWrapper<MoocPsychologyHealthScale> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MoocPsychologyHealthScale::getScaleName, scale.getScaleName())
                    .eq(MoocPsychologyHealthScale::getUserId, userId);

            if (scale.getScaleId() != null) {
                wrapper.ne(MoocPsychologyHealthScale::getScaleId, scale.getScaleId());
            }
            int count = moocPsychologyHealthScaleMapper.selectCount(wrapper);
            if (count > 0) {
                throw new ServiceException("量表名称已存在");
            }
        }

        //判断是否绑定教材
        List<Long> scaleIds = Collections.singletonList(scale.getScaleId());
        checkBoundBooksShelfStatus(scaleIds);
        scale.setUpdateBy(SecurityUtils.getUsername());
        scale.setUpdateTime(new Date());
        boolean result = this.updateById(scale);
        //保存mbti十六种人格
        if (scale.getScaleType() == 3){
            Long scaleId = scale.getScaleId();
            moocPsychologyHealthInstructionsMapper.deleteByScale(scaleId); //先删除
            Map<String, String> mbtiData = scale.getMbtiData();
            mbtiData.forEach((mbtiName, mbtiRemark) -> {
                MoocPsychologyHealthInstructions instructions = new MoocPsychologyHealthInstructions();
                instructions.setInstructionsName(mbtiName);
                instructions.setInstructionsRemark(mbtiRemark);
                instructions.setScaleId(scale.getScaleId());
                moocPsychologyHealthInstructionsService.save(instructions);
            });
        }
        if (scale.getQuestionSort() == PsychologyQuestionSortEnum.multi_dimension.getCode()) {
            //多维度
            List<MoocPsychologyHealthScaleFacet> scaleFacetList = scale.getMoocPsychologyHealthScaleFacet();

            List<Long> oldQuestionIds = questionServiceImpl.listObjs(
                    new QueryWrapper<MoocPsychologyHealthScaleQuestion>()
                            .select("question_id")
                            .eq("scale_id", scale.getScaleId()),
                    id -> Long.parseLong(id.toString())
            );

            // 2. 先删除所有旧选项
            if (CollectionUtils.isNotEmpty(oldQuestionIds)) {
                optionServiceImpl.remove(new QueryWrapper<MoocPsychologyHealthScaleQuestionOption>()
                        .in("question_id", oldQuestionIds));
            }

            // 3. 删除所有旧题目
            questionServiceImpl.remove(new QueryWrapper<MoocPsychologyHealthScaleQuestion>()
                    .eq("scale_id", scale.getScaleId()));

            // 4. 删除所有旧维度
            facetServiceImpl.remove(new QueryWrapper<MoocPsychologyHealthScaleFacet>()
                    .eq("scale_id", scale.getScaleId()));


            // 保存新维度
            scaleFacetList.forEach(facet -> {
                facet.setFacetId(null);
                facet.setScaleId(scale.getScaleId());
                facet.setCreateBy(SecurityUtils.getUsername());
                facet.setCreateTime(new Date());
            });
            facetServiceImpl.saveBatch(scaleFacetList);
            //保存题目和选项
            scaleFacetList.forEach(facet -> {
                if (CollectionUtils.isNotEmpty(facet.getMoocPsychologyHealthScaleQuestion())) {
                    // 保留原题目的oldQuestionId用于选项跳转关联
                    facet.getMoocPsychologyHealthScaleQuestion().forEach(q -> {
                        q.setOldQuestionId(q.getQuestionId()); // 保存旧ID用于选项跳转关联
                        q.setQuestionId(null); // 置空以生成新ID
                    });
                    saveQuestionAndOption(
                            facet.getMoocPsychologyHealthScaleQuestion(),
                            scale.getScaleId(),
                            facet.getFacetId()  // 注意这里要传入facetId
                    );
                }
            });

        } else {
            //  单维度处理

            List<Long> oldQuestionIds = questionServiceImpl.listObjs(
                    new QueryWrapper<MoocPsychologyHealthScaleQuestion>()
                            .select("question_id")
                            .eq("scale_id", scale.getScaleId())
                            .isNull("facet_id"),
                    id -> Long.parseLong(id.toString())
            );

            // 2. 先删除所有旧选项
            if (CollectionUtils.isNotEmpty(oldQuestionIds)) {
                optionServiceImpl.remove(new QueryWrapper<MoocPsychologyHealthScaleQuestionOption>()
                        .in("question_id", oldQuestionIds));
            }

            // 3. 删除所有旧题目
            questionServiceImpl.remove(new QueryWrapper<MoocPsychologyHealthScaleQuestion>()
                    .eq("scale_id", scale.getScaleId())
                    .isNull("facet_id"));
            // 保存新题目和选项（调用独立方法）
            if (CollectionUtils.isNotEmpty(scale.getMoocPsychologyHealthScaleQuestion())) {
                // 保留原题目的oldQuestionId用于选项跳转关联
                scale.getMoocPsychologyHealthScaleQuestion().forEach(q -> {
                    q.setOldQuestionId(q.getQuestionId()); // 保存旧ID用于选项跳转关联
                    q.setQuestionId(null); // 置空以生成新ID
                });
                saveQuestionAndOption(
                        scale.getMoocPsychologyHealthScaleQuestion(),
                        scale.getScaleId(),
                        null
                );
            }
        }

        return result;
    }

    private void checkBoundBooksShelfStatus(List<Long> scaleIds) {
        QueryWrapper<MoocPsychologyHealthChapter> chapterQueryWrapper = new QueryWrapper<>();
        chapterQueryWrapper.lambda()
                .in(MoocPsychologyHealthChapter::getScaleId, scaleIds)
                .eq(MoocPsychologyHealthChapter::getDelFlag, 0);
        List<MoocPsychologyHealthChapter> chapterList = moocPsychologyHealthChapterMapper.selectList(chapterQueryWrapper);

        if (ObjectUtil.isNotEmpty(chapterList)) {
            List<Long> bookIds = chapterList.stream()
                    .map(MoocPsychologyHealthChapter::getBookId)
                    .collect(Collectors.toList());

            QueryWrapper<DtbBook> bookQueryWrapper = new QueryWrapper<>();
            bookQueryWrapper.lambda()
                    .in(DtbBook::getBookId, bookIds)
                    .eq(DtbBook::getShelfState, 1)
                    .eq(DtbBook::getDelFlag, 0);

            if (dtbBookMapper.selectCount(bookQueryWrapper) > 0) {
                throw new ServiceException("绑定的教材已上架，不可编辑量表！");
            }
        }
    }

    @Override
    public boolean copyPsychologyHealth(MoocPsychologyHealthScale scale) {
        //当前心理健康数据
        Map<String, Object> data = getPsychologyHealthById(scale.getScaleId());
        //取出量表信息
        MoocPsychologyHealthScale scaleMessage =(MoocPsychologyHealthScale)data.get("scale");
        MoocPsychologyHealthScale scaleCopy = new MoocPsychologyHealthScale();
        scaleCopy.setScaleName(scaleMessage.getScaleName() + "-附表");
        scaleCopy.setUserId(scaleMessage.getUserId());
        scaleCopy.setScanQuestion(scaleMessage.getScanQuestion());
        scaleCopy.setEvaluateReference(scaleMessage.getEvaluateReference());
        scaleCopy.setEvaluationMethod(scaleMessage.getEvaluationMethod());
        scaleCopy.setScaleType(scaleMessage.getScaleType());
        scaleCopy.setQuestionSort( scaleMessage.getQuestionSort() );
        scaleCopy.setShowSumScore(scaleMessage.getShowSumScore());
        scaleCopy.setShowSortType(scaleMessage.getShowSortType());
        scaleCopy.setStatus(scaleMessage.getStatus());
        scaleCopy.setCreateBy(SecurityUtils.getUsername());
        scaleCopy.setCreateTime(new Date());

        // 保存副本量表基本信息
        this.save(scaleCopy);

        //复制十六种人格
        if (scale.getScaleType() == 3) {
            if (scaleMessage == null) {
                throw new IllegalArgumentException("源量表信息为空，无法复制MBTI数据");
            }
            List<Map<String, String>> mbtiDataList = scaleMessage.getMbtiDataList();
            for (Map<String, String> mbtiMap : mbtiDataList) {
                if (mbtiMap == null || mbtiMap.isEmpty()) {
                    continue; // 跳过空Map
                }
                MoocPsychologyHealthInstructions mbti = new MoocPsychologyHealthInstructions();
                // 并设置默认空字符串，避免null
                String instructionsName = mbtiMap.getOrDefault("instructions_name", "");
                String instructionsRemark = mbtiMap.getOrDefault("instructions_remark", "");
                mbti.setInstructionsName(instructionsName);
                mbti.setInstructionsRemark(instructionsRemark);
                mbti.setScaleId(scaleCopy.getScaleId());
                moocPsychologyHealthInstructionsService.save(mbti);
            }
        }

        //复制题目和选项
        if (scaleMessage.getQuestionSort() == PsychologyQuestionSortEnum.multi_dimension.getCode()) {

            //多维度
            Object rawQuestionList = data.get("questionList"); //从map中获取value，返回的对象是object
//            if (!(rawQuestionList instanceof List)) {
//                throw new IllegalArgumentException("questionList必须是List类型");
//            }

            List<?> rawList = (List<?>) rawQuestionList; // List<?>任意类型List ->将object转换成list
            if (!rawList.isEmpty() && rawList.get(0) instanceof MoocPsychologyHealthScaleFacet) {
                // 处理维度列表
                List<MoocPsychologyHealthScaleFacet> facetList = (List<MoocPsychologyHealthScaleFacet>) rawList;

                // 存储旧维度ID到新维度ID的映射
                Map<Long, Long> facetIdMap = new HashMap<>();

                // 复制维度信息
                List<MoocPsychologyHealthScaleFacet> newFacets = new ArrayList<>();
                for (MoocPsychologyHealthScaleFacet originalFacet : facetList) {
                    MoocPsychologyHealthScaleFacet newFacet = new MoocPsychologyHealthScaleFacet();
                    newFacet.setScaleId(scaleCopy.getScaleId());
                    newFacet.setFacetName(originalFacet.getFacetName());
                    newFacet.setSort(originalFacet.getSort());
                    newFacet.setCreateBy(SecurityUtils.getUsername());
                    newFacet.setCreateTime(new Date());
                    newFacet.setDelFlag("0");
                    newFacets.add(newFacet);
                }

                // 批量保存新维度
                facetServiceImpl.saveBatch(newFacets);

                // 建立旧维度ID到新维度ID的映射
                for (int i = 0; i < facetList.size(); i++) {
                    facetIdMap.put(facetList.get(i).getFacetId(), newFacets.get(i).getFacetId());
                }

                // 复制每个维度的题目和选项
                for (int i = 0; i < facetList.size(); i++) {
                    MoocPsychologyHealthScaleFacet originalFacet = facetList.get(i);
                    MoocPsychologyHealthScaleFacet newFacet = newFacets.get(i);

                    List<MoocPsychologyHealthScaleQuestion> originalQuestions = originalFacet.getMoocPsychologyHealthScaleQuestion();
                    if (CollectionUtils.isNotEmpty(originalQuestions)) {
                        // 存储旧题目ID到新题目ID的映射
                        Map<Long, Long> questionIdMap = new HashMap<>();

                        // 复制题目
                        List<MoocPsychologyHealthScaleQuestion> newQuestions = new ArrayList<>();
                        for (MoocPsychologyHealthScaleQuestion q : originalQuestions) {
                            MoocPsychologyHealthScaleQuestion newQuestion = new MoocPsychologyHealthScaleQuestion();
                            newQuestion.setScaleId(newFacet.getScaleId());
                            newQuestion.setFacetId(newFacet.getFacetId());
                            newQuestion.setSort(q.getSort());
                            newQuestion.setQuestionContent(q.getQuestionContent());
                            newQuestion.setQuestionSort(q.getQuestionSort());
                            newQuestion.setCreateBy(SecurityUtils.getUsername());
                            newQuestion.setCreateTime(new Date());
                            newQuestion.setDelFlag("0");
                            newQuestions.add(newQuestion);
                        }

                        // 批量保存新题目
                        questionServiceImpl.saveBatch(newQuestions);

                        // 建立旧题目ID到新题目ID的映射
                        for (int j = 0; j < originalQuestions.size(); j++) {
                            questionIdMap.put(originalQuestions.get(j).getQuestionId(), newQuestions.get(j).getQuestionId());
                        }

                        // 复制选项
                        List<MoocPsychologyHealthScaleQuestionOption> newOptions = new ArrayList<>();
                        for (MoocPsychologyHealthScaleQuestion q : originalQuestions) {
                            List<MoocPsychologyHealthScaleQuestionOption> originalOptions = q.getMoocPsychologyHealthScaleQuestionOption();
                            if (CollectionUtils.isNotEmpty(originalOptions)) {
                                for (MoocPsychologyHealthScaleQuestionOption option : originalOptions) {
                                    MoocPsychologyHealthScaleQuestionOption newOption = new MoocPsychologyHealthScaleQuestionOption();
                                    newOption.setQuestionId(questionIdMap.get(q.getQuestionId()));
                                    newOption.setOptionContent(option.getOptionContent());
                                    newOption.setSort(option.getSort());
                                    newOption.setScore(option.getScore());
                                    newOption.setMbtiCode(option.getMbtiCode());

                                    // 处理跳转逻辑
                                    if (option.getJumpQuestionId() != null) {
                                        newOption.setJumpQuestionId(questionIdMap.get(option.getJumpQuestionId()));
                                    }

                                    newOption.setCreateBy(SecurityUtils.getUsername());
                                    newOption.setCreateTime(new Date());
                                    newOption.setDelFlag("0");
                                    newOptions.add(newOption);
                                }
                            }
                        }

                        // 批量保存新选项
                        if (CollectionUtils.isNotEmpty(newOptions)) {
                            optionServiceImpl.saveBatch(newOptions);
                        }
                    }
                }
            }
        } else {
            // 单维度量表处理
            List<MoocPsychologyHealthScaleQuestion> questionList = (List<MoocPsychologyHealthScaleQuestion>) data.get("questionList");
            if (CollectionUtils.isNotEmpty(questionList)) {
                // 存储旧题目ID到新题目ID的映射
                Map<Long, Long> questionIdMap = new HashMap<>();

                // 复制题目
                List<MoocPsychologyHealthScaleQuestion> newQuestions = new ArrayList<>();
                for (MoocPsychologyHealthScaleQuestion originalQuestion : questionList) {
                    MoocPsychologyHealthScaleQuestion newQuestion = new MoocPsychologyHealthScaleQuestion();
                    newQuestion.setScaleId(scaleCopy.getScaleId());
                    newQuestion.setSort(originalQuestion.getSort());
                    newQuestion.setQuestionSort(originalQuestion.getQuestionSort());
                    newQuestion.setQuestionContent(originalQuestion.getQuestionContent());
                    newQuestion.setCreateBy(SecurityUtils.getUsername());
                    newQuestion.setCreateTime(new Date());
                    newQuestion.setDelFlag("0");
                    newQuestions.add(newQuestion);
                }
                // 批量保存新题目
                questionServiceImpl.saveBatch(newQuestions);

                // 建立旧题目ID到新题目ID的映射
                for (int i = 0; i < questionList.size(); i++) {
                    questionIdMap.put(questionList.get(i).getQuestionId(), newQuestions.get(i).getQuestionId());
                }

                // 复制选项
                List<MoocPsychologyHealthScaleQuestionOption> newOptions = new ArrayList<>();
                for (MoocPsychologyHealthScaleQuestion originalQuestion : questionList) {
                    List<MoocPsychologyHealthScaleQuestionOption> originalOptions = originalQuestion.getMoocPsychologyHealthScaleQuestionOption();
                    if (CollectionUtils.isNotEmpty(originalOptions)) {
                        for (MoocPsychologyHealthScaleQuestionOption originalOption : originalOptions) {
                            MoocPsychologyHealthScaleQuestionOption newOption = new MoocPsychologyHealthScaleQuestionOption();
                            newOption.setQuestionId(questionIdMap.get(originalQuestion.getQuestionId()));
                            newOption.setOptionContent(originalOption.getOptionContent());
                            newOption.setSort(originalOption.getSort());
                            newOption.setScore(originalOption.getScore());
                            newOption.setMbtiCode(originalOption.getMbtiCode());
                            // 处理跳转逻辑
                            if (originalOption.getJumpId() != null) {
                                newOption.setJumpQuestionId(questionIdMap.get(originalOption.getJumpId()));
                            }
                            newOption.setCreateBy(SecurityUtils.getUsername());
                            newOption.setCreateTime(new Date());
                            newOption.setDelFlag("0");
                            newOptions.add(newOption);
                        }
                    }
                }
                // 批量保存新选项
                if (CollectionUtils.isNotEmpty(newOptions)) {
                    optionServiceImpl.saveBatch(newOptions);
                }
            }
        }

        return true;
    }




    @Override
    public List<MoocPsychologyHealthScale> selectMoocPsychologyHealthScaleList(MoocPsychologyHealthScale scale) {
        scale.setUserId(SecurityUtils.getUserId());
        return moocPsychologyHealthScaleMapper.selectMoocPsychologyHealthScaleList(scale);
    }


    @Override
    public boolean deletedByScaleIds(List<Long> list) {
        QueryWrapper<MoocPsychologyHealthChapter> chapterQueryWrapper = new QueryWrapper<>();
        chapterQueryWrapper.lambda().in(MoocPsychologyHealthChapter::getScaleId, list)
                .eq(MoocPsychologyHealthChapter::getDelFlag, 0);
        List<MoocPsychologyHealthChapter> chapterList = moocPsychologyHealthChapterMapper.selectList(chapterQueryWrapper);
        if (ObjectUtil.isNotEmpty(chapterList)) {
            List<Long> bookIds = chapterList.stream().map(MoocPsychologyHealthChapter::getBookId).collect(Collectors.toList());
            QueryWrapper<DtbBook> bookQueryWrapper = new QueryWrapper<>();
            bookQueryWrapper.lambda().in(DtbBook::getBookId, bookIds).eq(DtbBook::getShelfState, 1)
                    .eq(DtbBook::getDelFlag, 0);
            List<DtbBook> bookList = dtbBookMapper.selectList(bookQueryWrapper);
            if (ObjectUtil.isNotEmpty(bookList)) {
                throw new ServiceException("绑定的教材已上架，不可删除！");
            } else {
                // 删除维度
                UpdateWrapper<MoocPsychologyHealthScaleFacet> facetUpdateWrapper = new UpdateWrapper<>();
                facetUpdateWrapper.lambda().in(MoocPsychologyHealthScaleFacet::getScaleId, list)
                       .set(MoocPsychologyHealthScaleFacet::getDelFlag, 2)
                        .set(MoocPsychologyHealthScaleFacet::getUpdateBy, SecurityUtils.getUsername())
                       .set(MoocPsychologyHealthScaleFacet::getCreateTime, new Date());
                moocPsychologyHealthScaleFacetMapper.update(null, facetUpdateWrapper);
                // 删除题目
                UpdateWrapper<MoocPsychologyHealthScaleQuestion> questionUpdateWrapper = new UpdateWrapper<>();
                questionUpdateWrapper.lambda().in(MoocPsychologyHealthScaleQuestion::getScaleId, list)
                      .set(MoocPsychologyHealthScaleQuestion::getDelFlag, 2).set(MoocPsychologyHealthScaleQuestion::getUpdateBy, SecurityUtils.getUsername())
                        .set(MoocPsychologyHealthScaleQuestion::getCreateTime, new Date());
                moocPsychologyHealthScaleQuestionMapper.update(null, questionUpdateWrapper);
                // 删除选项
                QueryWrapper<MoocPsychologyHealthScaleQuestion> questionQueryWrapper = new QueryWrapper<>();
                questionQueryWrapper.lambda().in(MoocPsychologyHealthScaleQuestion::getScaleId, list)
                      .eq(MoocPsychologyHealthScaleQuestion::getDelFlag, 0);
                List<MoocPsychologyHealthScaleQuestion> questionList = moocPsychologyHealthScaleQuestionMapper.selectList(questionQueryWrapper);
                if (CollectionUtils.isNotEmpty(questionList)) {
                    UpdateWrapper<MoocPsychologyHealthScaleQuestionOption> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().in(MoocPsychologyHealthScaleQuestionOption::getQuestionId, questionList.stream().map(MoocPsychologyHealthScaleQuestion::getQuestionId).collect(Collectors.toList()))
                           .set(MoocPsychologyHealthScaleQuestionOption::getDelFlag, 2)
                            .set(MoocPsychologyHealthScaleQuestionOption::getUpdateBy, SecurityUtils.getUsername())
                            .set(MoocPsychologyHealthScaleQuestionOption::getCreateTime, new Date());
                    moocPsychologyHealthScaleQuestionOptionMapper.update(null, updateWrapper);
                }
                // 删除量表
                return this.removeByIds(list);
            }

        } else {
            UpdateWrapper<MoocPsychologyHealthScale> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(MoocPsychologyHealthScale::getScaleId, list)
                    .set(MoocPsychologyHealthScale::getDelFlag, 2)
                    .set(MoocPsychologyHealthScale::getCreateTime, new Date())
                    .set(MoocPsychologyHealthScale::getCreateBy, SecurityUtils.getUsername());
            return moocPsychologyHealthScaleMapper.update(null, updateWrapper) > 0;
        }
    }

    @Override
    public List<MoocPsychologyHealthUserResult> getTestResultList(MoocPsychologyHealthScale scale) {
        return moocPsychologyHealthUserResultMapper.getTestResultList(scale);
    }

    @Override
    public Map<String, Object> getTestResultDetail(Long resultId) {
        Map<String, Object> res = new HashMap<>();
        MoocPsychologyHealthScale scale = moocPsychologyHealthScaleMapper.getByResultId(resultId);
        if (ObjectUtil.isEmpty(scale)) {
            throw new ServiceException("量表不存在");
        } else {
            res.put("scale", scale);
            //获取用户答案
            QueryWrapper<MoocPsychologyHealthTestAnswer> answerQueryWrapper = new QueryWrapper<>();
            answerQueryWrapper.lambda().eq(MoocPsychologyHealthTestAnswer::getResultId, resultId);
            //用户的答案
            List<MoocPsychologyHealthTestAnswer> answerList = moocPsychologyHealthTestAnswerMapper.selectList(answerQueryWrapper);
            if (scale.getQuestionSort() == PsychologyQuestionSortEnum.multi_dimension.getCode()) {
                // 多维度量表
                List<MoocPsychologyHealthScaleFacet> facetList = moocPsychologyHealthUserResultMapper.getTestFacetResultDetail(resultId);
                if (CollectionUtils.isNotEmpty(answerList)) {
                    facetList.forEach(f -> {
                        List<MoocPsychologyHealthScaleQuestion> questionList = f.getMoocPsychologyHealthScaleQuestion();
                        if (CollectionUtils.isNotEmpty(questionList)) {
                            questionList.forEach(q -> {
                                MoocPsychologyHealthTestAnswer option = answerList.stream().filter(e -> e.getQuestionId().equals(q.getQuestionId())).findFirst().orElse(null);
                                if (ObjectUtil.isNotEmpty(option)) {
                                    q.setChooseOptionId(option.getOptionId());
                                }
                            });
                        }
                    });
                }
                res.put("questionList", facetList);
            } else {
                // 单维度量表
                List<MoocPsychologyHealthScaleQuestion> questionList = moocPsychologyHealthUserResultMapper.getTestResultDetail(resultId);
                if (CollectionUtils.isNotEmpty(answerList)) {
                    questionList.forEach(q -> {
                        MoocPsychologyHealthTestAnswer option = answerList.stream().filter(e -> e.getQuestionId().equals(q.getQuestionId())).findFirst().orElse(null);
                        if (ObjectUtil.isNotEmpty(option)) {
                            q.setChooseOptionId(option.getOptionId());
                        }
                    });
                }
                res.put("questionList", questionList);
            }
        }
        return res;
    }

    @Override
    public List<MoocPsychologyHealthScale> listForEditor(MoocPsychologyHealthScale scale) {
        scale.setUserId(SecurityUtils.getUserId());
        return moocPsychologyHealthScaleMapper.listForEditor(scale);
    }

    @Override
    public Map<String, Object> getPsychologyHealthById(Long scaleId) {
        Map<String, Object> res = new HashMap<>();
        QueryWrapper<MoocPsychologyHealthScale> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MoocPsychologyHealthScale::getScaleId, scaleId);
        MoocPsychologyHealthScale moocPsychologyHealthScale = moocPsychologyHealthScaleMapper.selectOne(queryWrapper);
        List<Map<String,String>> mbtiData = moocPsychologyHealthScaleMapper.getMbtiDataByScaleId(scaleId);
        moocPsychologyHealthScale.setMbtiDataList(mbtiData);
        if (ObjectUtil.isNotEmpty(moocPsychologyHealthScale)) {
            res.put("scale", moocPsychologyHealthScale);
            if (moocPsychologyHealthScale.getQuestionSort() == PsychologyQuestionSortEnum.multi_dimension.getCode()) {
                // 多维度量表
                List<MoocPsychologyHealthScaleFacet> facetList = moocPsychologyHealthScaleMapper.getPsychologyHealthFacetById(scaleId,moocPsychologyHealthScale.getShowSortType());
                res.put("questionList", facetList);
            } else {
                // 单维度量表
                List<MoocPsychologyHealthScaleQuestion> questionList = moocPsychologyHealthScaleMapper.getPsychologyHealthById(scaleId);
                res.put("questionList", questionList);
            }
        } else {
            throw new ServiceException("量表不存在");
        }
        return res;
    }

    @Override
    public Boolean savePsychologyHealthAnswer(MoocPsychologyHealthScale scale) {
        // 保存结果
        // 获取healthChapterId
        QueryWrapper<MoocPsychologyHealthChapter> chapterQueryWrapper = new QueryWrapper<>();
        chapterQueryWrapper.lambda().eq(MoocPsychologyHealthChapter::getScaleId, scale.getScaleId())
                .eq(MoocPsychologyHealthChapter::getChapterId, scale.getChapterId())
                .eq(MoocPsychologyHealthChapter::getDomId, scale.getDomId());
        MoocPsychologyHealthChapter chapter = moocPsychologyHealthChapterMapper.selectOne(chapterQueryWrapper);
        if (ObjectUtil.isNotEmpty(chapter)) {
            MoocPsychologyHealthUserResult result = new MoocPsychologyHealthUserResult();
            result.setScaleId(scale.getScaleId());
            result.setUserId(SecurityUtils.getUserId());
            result.setScore(scale.getScore().toString());
            result.setHealthChapterId(chapter.getHealthChapterId());
            userResultMapper.insert(result);
            List<MoocPsychologyHealthTestAnswer> list = new ArrayList<>();
            scale.getMoocPsychologyHealthScaleQuestion().stream().forEach(e -> {
                MoocPsychologyHealthTestAnswer answer = new MoocPsychologyHealthTestAnswer();
                answer.setResultId(result.getResultId());
                answer.setUserId(SecurityUtils.getUserId());
                answer.setQuestionId(e.getQuestionId());
                answer.setOptionId(e.getOptionId());
                list.add(answer);
            });
            // 保存明细
            if (CollectionUtils.isNotEmpty(list)) {
                answerService.saveBatch(list);
            }
            return true;
        } else {
            throw new ServiceException("量表不存在");
        }

    }

    @Override
    public MoocPsychologyHealthScale getPsychologyById(Long scaleId) {
        return moocPsychologyHealthScaleMapper.selectPsychologyHeathScale(scaleId);
    }

    private void saveQuestionAndOption(List<MoocPsychologyHealthScaleQuestion> questionList, Long scaleId, Long facetId) {
        List<MoocPsychologyHealthScaleQuestionOption> saveOptionList = new ArrayList<>();

        // 1. 保存问题列表（questionId 由数据库自动生成）
        questionList.forEach(question -> {
            question.setQuestionId(null);  // 清空旧ID
            question.setScaleId(scaleId);
            if (facetId != null) {
                question.setFacetId(facetId);
            }
            question.setCreateBy(SecurityUtils.getUsername());
            question.setCreateTime(new Date());
        });
        questionServiceImpl.saveBatch(questionList);

        // 2. 建立旧问题ID到新问题ID的映射
        Map<Long, Long> oldToNewIdMap = questionList.stream()
                .filter(q -> q.getOldQuestionId() != null)
                .collect(Collectors.toMap(
                        MoocPsychologyHealthScaleQuestion::getOldQuestionId,
                        MoocPsychologyHealthScaleQuestion::getQuestionId,
                        (existing, replacement) -> existing));

        // 3. 处理选项列表
        questionList.forEach(question -> {
            List<MoocPsychologyHealthScaleQuestionOption> optionList = question.getMoocPsychologyHealthScaleQuestionOption();
            optionList.forEach(option -> {
                option.setOptionId(null);  // +++ 关键修改：清空选项主键 +++
                option.setQuestionId(question.getQuestionId());

                // 处理选项跳转逻辑
                if (option.getJumpId() != null) {
                    if (option.getJumpId() == -1) {
                        option.setJumpQuestionId(option.getJumpId());
                    }else{
                        Long newQuestionId = oldToNewIdMap.get(option.getJumpId());
                        if (newQuestionId != null) {
                            option.setJumpQuestionId(newQuestionId);
                        }
                    }
                }

                option.setCreateBy(SecurityUtils.getUsername());
                option.setCreateTime(new Date());
                saveOptionList.add(option);
            });
        });

        // 4. 批量保存选项
        optionServiceImpl.saveBatch(saveOptionList);
    }
}
