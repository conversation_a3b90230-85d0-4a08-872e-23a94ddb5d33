package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookTemplate;
import cn.dutp.book.domain.DtbBookTemplateType;
import cn.dutp.book.mapper.DtbBookTemplateTypeMapper;
import cn.dutp.book.service.IDtbBookTemplateService;
import cn.dutp.book.service.IDtbBookTemplateTypeService;
import cn.dutp.common.core.exception.ServiceException;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教材模板分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class DtbBookTemplateTypeServiceImpl extends ServiceImpl<DtbBookTemplateTypeMapper, DtbBookTemplateType> implements IDtbBookTemplateTypeService {
    @Autowired
    private DtbBookTemplateTypeMapper dtbBookTemplateTypeMapper;
    @Autowired
    private IDtbBookTemplateService bookTemplateService;

    /**
     * 查询教材模板分类
     *
     * @param typeId 教材模板分类主键
     * @return 教材模板分类
     */
    @Override
    public DtbBookTemplateType selectDtbBookTemplateTypeByTypeId(Long typeId) {
        return this.getById(typeId);
    }

    /**
     * 查询教材模板分类列表
     *
     * @param dtbBookTemplateType 教材模板分类
     * @return 教材模板分类
     */
    @Override
    public List<DtbBookTemplateType> selectDtbBookTemplateTypeList(DtbBookTemplateType dtbBookTemplateType) {
        LambdaQueryWrapper<DtbBookTemplateType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbBookTemplateType.getTypeName())) {
            lambdaQueryWrapper.like(DtbBookTemplateType::getTypeName
                    , dtbBookTemplateType.getTypeName());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教材模板分类
     *
     * @param dtbBookTemplateType 教材模板分类
     * @return 结果
     */
    @Override
    public boolean insertDtbBookTemplateType(DtbBookTemplateType dtbBookTemplateType) {
        return this.save(dtbBookTemplateType);
    }

    /**
     * 修改教材模板分类
     *
     * @param dtbBookTemplateType 教材模板分类
     * @return 结果
     */
    @Override
    public boolean updateDtbBookTemplateType(DtbBookTemplateType dtbBookTemplateType) {
        return this.updateById(dtbBookTemplateType);
    }

    /**
     * 批量删除教材模板分类
     *
     * @param typeIds 需要删除的教材模板分类主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTemplateTypeByTypeIds(List<Long> typeIds) {
        for (Long typeId : typeIds) {
            Integer count = bookTemplateService.lambdaQuery().eq(DtbBookTemplate::getType, typeId).count();
            if (count > 0) {
                throw new ServiceException("教材模板分类下存在教材模板，不能删除");
            }
        }
        return this.removeByIds(typeIds);
    }

}
