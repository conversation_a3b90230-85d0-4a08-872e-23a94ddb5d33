package cn.dutp.book.service.impl;

import cn.dutp.book.domain.*;
import cn.dutp.book.domain.vo.BookPaperTreeListVO;
import cn.dutp.book.domain.vo.DtbBookTestPaperDetailVO;
import cn.dutp.book.domain.vo.DtbBookTestPaperVO;
import cn.dutp.book.mapper.DtbBookBookMapper;
import cn.dutp.book.mapper.DtbBookTestPaperMapper;
import cn.dutp.book.mapper.DtbUserBookMapper;
import cn.dutp.book.service.*;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教材跟试卷关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Service
public class DtbBookTestPaperServiceImpl extends ServiceImpl<DtbBookTestPaperMapper, DtbBookTestPaper> implements IDtbBookTestPaperService {
    @Autowired
    private DtbBookTestPaperMapper dtbBookTestPaperMapper;

    @Autowired
    private IDtbBookQuestionAnswerService dtbBookQuestionAnswerService;

    @Autowired
    private IDtbBookTestPaperAnswerService dtbBookTestPaperAnswerService;

    @Autowired
    private IDtbBookTestPaperAnswerDetailService dtbBookTestPaperAnswerDetailService;

    @Autowired
    private IDtbBookQuestionService dtbBookQuestionService;

    @Autowired
    private IDtbUserBookService dtbUserBookService;

    @Autowired
    private DtbBookBookMapper bookMapper;

    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;

    /**
     * 查询教材跟试卷关系
     *
     * @param bookPaperId 教材跟试卷关系主键
     * @return 教材跟试卷关系
     */
    @Override
    public DtbBookTestPaper selectDtbBookTestPaperByBookPaperId(Long bookPaperId) {
        return this.getById(bookPaperId);
    }

    /**
     * 查询教材跟试卷关系
     *
     * @param bookPaperId 教材跟试卷关系主键
     * @return 教材跟试卷关系
     */
    @Override
    public AjaxResult getPaperAnswer(DtbBookTestPaper dtbBookTestPaper) {
        // 返回对象
        DtbBookTestPaperVO dtbBookTestPaperVO = new DtbBookTestPaperVO();

        // 获取登录用户id
        Long userId = SecurityUtils.getUserId();
        Long paperId = dtbBookTestPaper.getPaperId();
        Long chapterId = dtbBookTestPaper.getChapterId();
        Long bookId = dtbBookTestPaper.getBookId();
        // 获取教材跟试卷关系数据
        LambdaQueryWrapper<DtbBookTestPaper> wrapper = new LambdaQueryWrapper<DtbBookTestPaper>();
        if (ObjectUtil.isNotEmpty(dtbBookTestPaper.getBookPaperId())) {
            wrapper.eq(DtbBookTestPaper::getBookPaperId, dtbBookTestPaper.getBookPaperId());
        }
        wrapper.eq(DtbBookTestPaper::getBookId, dtbBookTestPaper.getBookId());
        wrapper.eq(DtbBookTestPaper::getPaperId, dtbBookTestPaper.getPaperId());
        wrapper.eq(DtbBookTestPaper::getChapterId, dtbBookTestPaper.getChapterId());
        if (ObjectUtil.isNotEmpty(dtbBookTestPaper.getDomId())) {
            wrapper.eq(DtbBookTestPaper::getDomId, dtbBookTestPaper.getDomId());
        }
        dtbBookTestPaper = this.getOne(wrapper);
        if (ObjectUtil.isEmpty(dtbBookTestPaper)) {
            return AjaxResult.success();
        }

        // 获取用户答卷数据
        DtbBookTestPaperAnswer dtbBookTestPaperAnswer = dtbBookTestPaperAnswerService.getOne(new LambdaQueryWrapper<DtbBookTestPaperAnswer>().eq(DtbBookTestPaperAnswer::getUserId, userId)
                .eq(DtbBookTestPaperAnswer::getBookPaperId, dtbBookTestPaper.getBookPaperId())
                .eq(DtbBookTestPaperAnswer::getPaperId, paperId)
                .orderByDesc(DtbBookTestPaperAnswer::getCreateTime)
                .last("LIMIT 1"));
        if (ObjectUtil.isEmpty(dtbBookTestPaperAnswer)) {
            return AjaxResult.success();
        }

        dtbBookTestPaperVO.setPaperId(paperId);
        dtbBookTestPaperVO.setTotalScore(dtbBookTestPaperAnswer.getTotalScore());

        Long paperAnswerId = dtbBookTestPaperAnswer.getPaperAnswerId();

        // 获取用户答卷信息
        List<DtbBookTestPaperDetailVO> dtbBookTestPaperDetailVOS = this.baseMapper.getInfoByPaperId(paperId, paperAnswerId);

        dtbBookTestPaperVO.setDtbBookTestPaperDetailList(dtbBookTestPaperDetailVOS);
        return AjaxResult.success(dtbBookTestPaperVO);
    }

    /**
     * 查询教材跟试卷关系列表
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 教材跟试卷关系
     */
    @Override
    public List<DtbBookTestPaper> selectDtbBookTestPaperList(DtbBookTestPaper dtbBookTestPaper) {
        LambdaQueryWrapper<DtbBookTestPaper> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbBookTestPaper.getBookId())) {
            lambdaQueryWrapper.eq(DtbBookTestPaper::getBookId
                    , dtbBookTestPaper.getBookId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTestPaper.getPaperId())) {
            lambdaQueryWrapper.eq(DtbBookTestPaper::getPaperId
                    , dtbBookTestPaper.getPaperId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTestPaper.getChapterId())) {
            lambdaQueryWrapper.eq(DtbBookTestPaper::getChapterId
                    , dtbBookTestPaper.getChapterId());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教材跟试卷关系
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 结果
     */
    @Override
    public boolean addPaper(DtbBookTestPaper dtbBookTestPaper) {
        return this.save(dtbBookTestPaper);
    }

    /**
     * 考试保存接口
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertDtbBookTestPaper(DtbBookTestPaper dtbBookTestPaper) {
        // 获取登录用户id
        Long userId = SecurityUtils.getUserId();

        // 查询教材试卷关系表
        DtbBookTestPaper bookTestPaper = new DtbBookTestPaper();
        LambdaQueryWrapper<DtbBookTestPaper> wrapper = new LambdaQueryWrapper<DtbBookTestPaper>();
        if (ObjectUtil.isNotEmpty(dtbBookTestPaper.getBookPaperId())) {
            wrapper.eq(DtbBookTestPaper::getBookPaperId, dtbBookTestPaper.getBookPaperId());
        }
        wrapper.eq(DtbBookTestPaper::getBookId, dtbBookTestPaper.getBookId());
        wrapper.eq(DtbBookTestPaper::getPaperId, dtbBookTestPaper.getPaperId());
        wrapper.eq(DtbBookTestPaper::getChapterId, dtbBookTestPaper.getChapterId());
        if (ObjectUtil.isNotEmpty(dtbBookTestPaper.getDomId())) {
            wrapper.eq(DtbBookTestPaper::getDomId, dtbBookTestPaper.getDomId());
        }
        bookTestPaper = this.getOne(wrapper);
        if (ObjectUtil.isEmpty(bookTestPaper)){
            return AjaxResult.error("试卷不存在");
        }

        // 保存用户答卷表
        DtbBookTestPaperAnswer dtbBookTestPaperAnswer = new DtbBookTestPaperAnswer();
        dtbBookTestPaperAnswer.setUserId(userId);
        dtbBookTestPaperAnswer.setBookPaperId(bookTestPaper.getBookPaperId());
        dtbBookTestPaperAnswer.setPaperId(bookTestPaper.getPaperId());

        // 保存问题答案表
        List<DtbBookQuestionAnswer> dtbBookQuestionAnswerList = dtbBookTestPaper.getDtbBookQuestionAnswerList();
        DtbBookQuestionAnswer dtbBookQuestionAnswer = new DtbBookQuestionAnswer();

        BigDecimal totalScore = new BigDecimal(0);
        for (DtbBookQuestionAnswer bookQuestionAnswer : dtbBookQuestionAnswerList) {
            bookQuestionAnswer.setUserId(bookQuestionAnswer.getUserId());
            bookQuestionAnswer.setChapterId(bookTestPaper.getChapterId());
            bookQuestionAnswer.setBookId(bookTestPaper.getBookId());
            bookQuestionAnswer.setUserQuestionId(bookQuestionAnswer.getQuestionId());
            // 计算总分
            // 确保 getScore() 返回的不是 null
            if (bookQuestionAnswer.getScore() != null) {
                totalScore = totalScore.add(new BigDecimal(bookQuestionAnswer.getScore()));
            }
        }

        // 计算总分后答卷表 save
        dtbBookTestPaperAnswer.setTotalScore(totalScore);
        dtbBookTestPaperAnswerService.save(dtbBookTestPaperAnswer);

        // 问题答案表save
        dtbBookQuestionAnswerService.saveBatch(dtbBookQuestionAnswerList);

        // 保存用户答题表
        List<DtbBookTestPaperAnswerDetail> dtbBookTestPaperAnswerDetails = new ArrayList<>();
        DtbBookTestPaperAnswerDetail dtbBookTestPaperAnswerDetail = new DtbBookTestPaperAnswerDetail();
        for (DtbBookQuestionAnswer bookQuestionAnswer : dtbBookQuestionAnswerList) {
            dtbBookTestPaperAnswerDetail = new DtbBookTestPaperAnswerDetail();

            dtbBookTestPaperAnswerDetail.setPaperAnswerId(dtbBookTestPaperAnswer.getPaperAnswerId());
            dtbBookTestPaperAnswerDetail.setScore(bookQuestionAnswer.getScore());
            dtbBookTestPaperAnswerDetail.setQuestionAnswerId(bookQuestionAnswer.getAnswerId());

            dtbBookTestPaperAnswerDetails.add(dtbBookTestPaperAnswerDetail);
        }
        dtbBookTestPaperAnswerDetailService.saveBatch(dtbBookTestPaperAnswerDetails);
        return AjaxResult.success();
    }

    /**
     * 获取考试和作业列表 按章节id筛选
     *
     * @return 结果
     */
    @Override
    public AjaxResult getPaperList(DtbBookTestPaper dtbBookTestPaper) {
        // 查询书籍是否已购买
        Long bookId = dtbBookTestPaper.getBookId();
        Long userId = SecurityUtils.getUserId();
        DtbBook dtbBook = bookMapper.selectById(bookId);
        LambdaQueryWrapper<DtbUserBook> dtbUserBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 如果是副教材 查询关联的主教材是否购买
        if (dtbBook.getMasterFlag() == 3) {
            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId, dtbBook.getMasterBookId())
                    .eq(DtbUserBook::getUserId, userId)
                    .gt(DtbUserBook::getExpireDate, new Date());
        } else {
            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId, bookId)
                    .eq(DtbUserBook::getUserId, userId)
                    .gt(DtbUserBook::getExpireDate, new Date());
        }
        DtbUserBook dtbUserBook = dtbUserBookMapper.selectOne(dtbUserBookLambdaQueryWrapper);
        Long versionId;
        // 没买
        if (ObjectUtil.isNotEmpty(userId) && userId.longValue() != 0l && ObjectUtil.isNotNull(dtbUserBook)) {
            if (dtbBook.getMasterFlag() == 3) {
                versionId = dtbBook.getCurrentVersionId();
            } else {
                versionId = dtbUserBook.getVersionId();
            }
            dtbBookTestPaper.setIsFree(0);
        } else {
            versionId = dtbBook.getCurrentVersionId();
            dtbBookTestPaper.setIsFree(2);
        }
        dtbBookTestPaper.setVersionId(versionId);

        List<DtbBookTestPaper> paperList = dtbBookTestPaperMapper.getPaperList(userId,dtbBookTestPaper);
        // 按 chapterId 分组
        Map<Long, List<DtbBookTestPaper>> grouped = paperList.stream()
                .collect(Collectors.groupingBy(DtbBookTestPaper::getChapterId));

        // 转换为 VO 列表
        List<BookPaperTreeListVO> voList = grouped.entrySet().stream()
                .map(entry -> {
                    Long chapterId = entry.getKey();
                    List<DtbBookTestPaper> groupDtos = entry.getValue();
                    BookPaperTreeListVO vo = new BookPaperTreeListVO();
                    vo.setChapterId(chapterId);
                    vo.setChapterName(groupDtos.get(0).getChapterName()); // 同一章节名称相同

                    // 转换为 DtbBookTestPaper 列表
                    List<DtbBookTestPaper> papers = groupDtos.stream()
                            .map(dto -> {
                                DtbBookTestPaper paper = new DtbBookTestPaper();
                                // 复制字段，例如使用 BeanUtils 或手动设置
                                paper.setBookPaperId(dto.getBookPaperId());
                                paper.setPaperTitle(dto.getPaperTitle());
                                paper.setQuestionQuantity(dto.getQuestionQuantity());
                                paper.setState(dto.getState());
                                paper.setTotalScore(dto.getTotalScore());
                                paper.setChapterId(dto.getChapterId());
                                paper.setChapterName(dto.getChapterName());
                                paper.setPaperId(dto.getPaperId());
                                paper.setPageNumber(dto.getPageNumber());
                                paper.setDomId(dto.getDomId());
                                return paper;
                            })
                            .collect(Collectors.toList());

                    vo.setDtbBookTestPaperList(papers);
                    return vo;
                })
                .collect(Collectors.toList());

        return AjaxResult.success(voList);
    }

    /**
     * 修改教材跟试卷关系
     *
     * @param dtbBookTestPaper 教材跟试卷关系
     * @return 结果
     */
    @Override
    public boolean updateDtbBookTestPaper(DtbBookTestPaper dtbBookTestPaper) {
        return this.updateById(dtbBookTestPaper);
    }

    /**
     * 批量删除教材跟试卷关系
     *
     * @param bookPaperIds 需要删除的教材跟试卷关系主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTestPaperByBookPaperIds(List<Long> bookPaperIds) {
        return this.removeByIds(bookPaperIds);
    }

}
