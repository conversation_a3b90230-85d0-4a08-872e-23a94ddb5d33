package cn.dutp.book.controller;


import cn.dutp.book.domain.DtbBookChapterContent;
import cn.dutp.book.service.IDtbBookChapterContentService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 章节内容Controller
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@RestController
@RequestMapping("/chapterContent")
public class DtbChapterContentController extends BaseController {
    @Autowired
    private IDtbBookChapterContentService dtbBookChapterContentService;


    /**
     * 查询章节内容
     */
    @GetMapping(value = "/{chapterId}")
    public AjaxResult getChapterContentInfo(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookChapterContentService.getChapterContentInfo(chapterId));
    }

    /**
     * 检测章节是否有内容
     */
    @GetMapping(value = "/checkIsHasContent/{chapterId}")
    public AjaxResult checkIsHasContent(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookChapterContentService.checkIsHasContent(chapterId));
    }

    /**
     * 清空章节内容
     */
    @GetMapping(value = "/clearChapterContent/{chapterId}")
    public AjaxResult clearChapterContent(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookChapterContentService.clearChapterContent(chapterId));
    }

    /**
     * 更新教材章节内容
     */
    @PostMapping("/updateChapterContentInfo")
    public AjaxResult updateChapterContentInfo(@RequestBody DtbBookChapterContent dtbBookChapterContent) {
        return toAjax(dtbBookChapterContentService.updateChapterContentInfo(dtbBookChapterContent));
    }

    /**
     * 切换教材章节版本内容 --根据mongodb的id
     */
    @PostMapping("/changeChapterContentInfo")
    public AjaxResult changeChapterContentInfo(@RequestBody DtbBookChapterContent dtbBookChapterContent) {
        return toAjax(dtbBookChapterContentService.changeChapterContentInfo(dtbBookChapterContent));
    }

    /**
     * 获取当前章节当前用户的保存版本历史 --最近十条
     */
    @GetMapping("/queryCurUserSaveVersionHistory/{chapterId}")
    public AjaxResult queryCurUserSaveVersionHistory(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookChapterContentService.queryCurUserSaveVersionHistory(chapterId));
    }

    /**
     * 旧数据生成原始稿
     */
    @GetMapping("/generateOriginalDraft")
    public AjaxResult generateOriginalDraft() {
        return success(dtbBookChapterContentService.generateOriginalDraft());
    }

}
