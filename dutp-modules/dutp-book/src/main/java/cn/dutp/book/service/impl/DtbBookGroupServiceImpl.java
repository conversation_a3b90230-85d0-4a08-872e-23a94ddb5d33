package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookChapterEditor;
import cn.dutp.book.domain.vo.DtbBookGroupVo;
import cn.dutp.book.domain.vo.DtbBookPermissionsVo;
import cn.dutp.book.mapper.DtbBookChapterEditorMapper;
import cn.dutp.book.mapper.DtbBookCommonMapper;
import cn.dutp.book.mapper.DtbBookGroupMapper;
import cn.dutp.book.service.IDtbBookGroupService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBookGroup;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数字教材作者编辑团队Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class DtbBookGroupServiceImpl extends ServiceImpl<DtbBookGroupMapper, DtbBookGroup> implements IDtbBookGroupService {
    @Autowired
    private DtbBookGroupMapper dtbBookGroupMapper;

    @Autowired
    private DtbBookChapterEditorMapper bookChapterEditorMapper;

    @Autowired
    private DtbBookCommonMapper commonMapper;

    /**
     * 查询数字教材作者编辑团队
     *
     * @param bookId 数字教材作者编辑团队主键
     * @return 数字教材作者编辑团队
     */
    @Override
    public DtbBookGroupVo selectDtbBookGroupByBookId(Long bookId) {
        List<DtbBookGroup> dtbBookGroupList = this.list(new LambdaQueryWrapper<DtbBookGroup>()
                .select(DtbBookGroup::getBookId, DtbBookGroup::getGroupId, DtbBookGroup::getRoleType,
                        DtbBookGroup::getUserId)
                .eq(DtbBookGroup::getBookId, bookId));
        DtbBookGroupVo bookGroupVo = new DtbBookGroupVo();
        bookGroupVo.setBookId(bookId);
        bookGroupVo.setContact(null);
        bookGroupVo.setEditor(Collections.emptyList());
        bookGroupVo.setAssociateEditor(Collections.emptyList());
        bookGroupVo.setParticipateCompilation(Collections.emptyList());
        bookGroupVo.setEditorInCharge(Collections.emptyList());
        bookGroupVo.setPlanningEditor(null);
        bookGroupVo.setProofreader(Collections.emptyList());
        if (ObjectUtil.isNotEmpty(dtbBookGroupList)) {
            Map<Integer, List<DtbBookGroup>> groupCollect = dtbBookGroupList.stream().collect(Collectors.groupingBy(DtbBookGroup::getRoleType));

            for (Map.Entry<Integer, List<DtbBookGroup>> groupListEntry : groupCollect.entrySet()) {
                // 1书稿联系人2主编3副主编4参编5策划编辑6责任编辑7编校人员

                Integer roleType = groupListEntry.getKey();
                List<Long> userIdList = groupListEntry.getValue().stream().map(DtbBookGroup::getUserId).collect(Collectors.toList());
                if (roleType == 1) {
                    bookGroupVo.setContact(userIdList.size() > 0 ? userIdList.get(0) : null);
                } else if (roleType == 2) {
                    bookGroupVo.setEditor(userIdList);
                } else if (roleType == 3) {
                    bookGroupVo.setAssociateEditor(userIdList);
                } else if (roleType == 4) {
                    bookGroupVo.setParticipateCompilation(userIdList);
                } else if (roleType == 5) {
                    bookGroupVo.setPlanningEditor(userIdList.size() > 0 ? userIdList.get(0) : null);
                } else if (roleType == 6) {
                    bookGroupVo.setEditorInCharge(userIdList);
                } else if (roleType == 7) {
                    bookGroupVo.setProofreader(userIdList);
                }
            }
        }

        return bookGroupVo;
    }


    /**
     * 修改数字教材作者编辑团队
     *
     * @param bookGroupVo 数字教材作者编辑团队
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateDtbBookGroup(DtbBookGroupVo bookGroupVo) {

        Long bookId = bookGroupVo.getBookId();

        Long contact = bookGroupVo.getContact();
        List<Long> editor = bookGroupVo.getEditor();
        List<Long> associateEditor = bookGroupVo.getAssociateEditor();
        List<Long> participateCompilation = bookGroupVo.getParticipateCompilation();
        List<Long> editorInCharge = bookGroupVo.getEditorInCharge();
        List<Long> proofreader = bookGroupVo.getProofreader();
        Long planningEditor = bookGroupVo.getPlanningEditor();

        // 1书稿联系人 2主编 3副主编 4参编 5策划编辑 6责任编辑 7编校人员
        if (ObjectUtil.isEmpty(contact)) {
            throw new ServiceException("书稿联系人不能为空");
        }
        if (ObjectUtil.isEmpty(planningEditor)) {
            throw new ServiceException("策划编辑不能为空");
        }
        List<Long> userIdList = new ArrayList<>();

        List<DtbBookGroup> groupList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(contact)) {
            userIdList.add(contact);
            groupList.add(DtbBookGroup.builder()
                    .bookId(bookId)
                    .userId(contact).roleType(1)
                    .build());
        }
        if (ObjectUtil.isNotEmpty(editor)) {
            userIdList.addAll(editor);
            editor.forEach(e -> groupList.add(DtbBookGroup.builder()
                    .bookId(bookId)
                    .userId(e).roleType(2)
                    .build()));
        }
        if (ObjectUtil.isNotEmpty(associateEditor)) {
            userIdList.addAll(associateEditor);
            associateEditor.forEach(e -> groupList.add(DtbBookGroup.builder()
                    .bookId(bookId)
                    .userId(e).roleType(3)
                    .build()));
        }
        if (ObjectUtil.isNotEmpty(participateCompilation)) {
            userIdList.addAll(participateCompilation);
            participateCompilation.forEach(e -> groupList.add(DtbBookGroup.builder()
                    .bookId(bookId)
                    .userId(e).roleType(4)
                    .build()));

        }
        if (ObjectUtil.isNotEmpty(planningEditor)) {
            userIdList.add(planningEditor);
            groupList.add(DtbBookGroup.builder()
                    .bookId(bookId)
                    .userId(planningEditor).roleType(5)
                    .build());

        }
        if (ObjectUtil.isNotEmpty(editorInCharge)) {
            userIdList.addAll(editorInCharge);
            editorInCharge.forEach(e -> groupList.add(DtbBookGroup.builder()
                    .bookId(bookId)
                    .userId(e).roleType(6)
                    .build()));
        }
        if (ObjectUtil.isNotEmpty(proofreader)) {
            userIdList.addAll(proofreader);
            proofreader.forEach(e -> groupList.add(DtbBookGroup.builder()
                    .bookId(bookId)
                    .userId(e).roleType(7)
                    .build()));
        }
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());

        List<DtbBookGroup> bookGroupList = dtbBookGroupMapper.selectList(new LambdaQueryWrapper<DtbBookGroup>()
                .select(DtbBookGroup::getUserId)
                .eq(DtbBookGroup::getBookId, bookId)
        );
        List<Long> oldUserIdList = bookGroupList.stream().map(DtbBookGroup::getUserId).distinct().collect(Collectors.toList());
        Map<Long, List<Long>> delChapterEditor = new HashMap<>();
        if (ObjectUtil.isNotEmpty(oldUserIdList)) {
            // 判断从团队中删除的成员是否是唯一的编辑者
            List<Long> finalUserIdList = userIdList;
            List<Long> delUserIdList = oldUserIdList.stream()
                    .filter(e -> !finalUserIdList.contains(e))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(delUserIdList)) {
                List<DtbBookChapterEditor> chapterEditorList = bookChapterEditorMapper.queryUserList(bookId);

                Map<Long, List<DtbBookChapterEditor>> collect = chapterEditorList.stream().collect(Collectors.groupingBy(DtbBookChapterEditor::getChapterId));
                collect.forEach((k, v) -> {
                    if (v != null) {
                        List<Long> curUserIdList = v.stream().map(DtbBookChapterEditor::getUserId).collect(Collectors.toList());
                        // 当前章节还有其他的编辑者
                        List<Long> curHasUserIdList = curUserIdList.stream().filter(e -> !delUserIdList.contains(e)).collect(Collectors.toList());
                        List<Long> curDelUserIdList = curUserIdList.stream().filter(e -> delUserIdList.contains(e)).collect(Collectors.toList());
                        if (ObjectUtil.isEmpty(curHasUserIdList)) {
                            String userName = commonMapper.queryNickNameByUserId(curUserIdList.get(0));
                            throw new ServiceException(userName + "已关联章节，不允许删除");
                        }
                        if (ObjectUtil.isNotEmpty(curDelUserIdList)) {
                            delChapterEditor.put(k, curDelUserIdList);
                        }
                    }
                });
            }
        }
        dtbBookGroupMapper.delAllByBookId(bookGroupVo.getBookId());
        boolean success = this.saveBatch(groupList);
        if (success && ObjectUtil.isNotEmpty(delChapterEditor)) {
            delChapterEditor.forEach((k, v) -> {
                for (Long userId : v) {
                    bookChapterEditorMapper.deleteByChapterIdAndUserId(k, userId);
                }
            });
        }
        return success;
    }

    @Override
    public List<SysUser> groupUserList(Long bookId) {
        return dtbBookGroupMapper.groupUserList(bookId);
    }

    @Override
    public DtbBookPermissionsVo getPermissionsInfo(Long bookId) {
        // 获取当前登录人权限
        Long userId = SecurityUtils.getUserId();
        List<DtbBookGroup> groupList = dtbBookGroupMapper.selectList(new LambdaQueryWrapper<DtbBookGroup>()
                .select(DtbBookGroup::getRoleType)
                .eq(DtbBookGroup::getBookId, bookId)
                .eq(DtbBookGroup::getUserId, userId));
        DtbBookPermissionsVo dtbBookPermissionsVo = new DtbBookPermissionsVo();
        List<Integer> permissions = groupList.stream().map(DtbBookGroup::getRoleType).collect(Collectors.toList());
        dtbBookPermissionsVo.setBookId(bookId);
        dtbBookPermissionsVo.setPermissions(permissions);
        // 1书稿联系人 2主编 3副主编 4参编 5策划编辑 6责任编辑 7编校人员
        dtbBookPermissionsVo.setIsEditor(permissions.stream().anyMatch(Arrays.asList(5, 6, 7)::contains));
        dtbBookPermissionsVo.setIsAuthor(permissions.stream().anyMatch(Arrays.asList(1, 2, 3, 4)::contains));
        return dtbBookPermissionsVo;
    }
}
