package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 题样式模板对象 dtb_book_question_template
 *
 * <AUTHOR>
 * @date 2025-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dtb_book_question_template")
public class DtbBookQuestionTemplate extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 题模板ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionTemplateId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    private Long bookId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    private Long chapterId;

    /**
     * 字体颜色
     */
    @Excel(name = "字体颜色")
    private String fontColor;

    /**
     * 字体大小
     */
    @Excel(name = "字体大小")
    private String fontSize;

    /**
     * 字体
     */
    @Excel(name = "字体")
    private String fontStyle;

}
