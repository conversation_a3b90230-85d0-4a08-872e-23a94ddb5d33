package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookChapterRemark;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 章节标注Service接口
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
public interface IDtbBookChapterRemarkService extends IService<DtbBookChapterRemark>
{
    /**
     * 查询章节标注
     *
     * @param remarkId 章节标注主键
     * @return 章节标注
     */
    public DtbBookChapterRemark selectDtbBookChapterRemarkByRemarkId(Long remarkId);

    /**
     * 查询章节标注列表
     *
     * @param dtbBookChapterRemark 章节标注
     * @return 章节标注集合
     */
    public List<DtbBookChapterRemark> selectDtbBookChapterRemarkList(DtbBookChapterRemark dtbBookChapterRemark);

    /**
     * 新增章节标注
     *
     * @param dtbBookChapterRemark 章节标注
     * @return 结果
     */
    public boolean insertDtbBookChapterRemark(DtbBookChapterRemark dtbBookChapterRemark);

    /**
     * 修改章节标注
     *
     * @param dtbBookChapterRemark 章节标注
     * @return 结果
     */
    public boolean updateDtbBookChapterRemark(DtbBookChapterRemark dtbBookChapterRemark);

    /**
     * 批量删除章节标注
     *
     * @param remarkIds 需要删除的章节标注主键集合
     * @return 结果
     */
    public boolean deleteDtbBookChapterRemarkByRemarkIds(List<Long> remarkIds);

}
