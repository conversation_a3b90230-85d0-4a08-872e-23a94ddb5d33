package cn.dutp.book.service.impl;


import java.util.List;

import cn.dutp.book.domain.DtbUserQuestion;
import cn.dutp.book.domain.vo.BookQuestionRecycleVO;
import cn.dutp.book.domain.vo.DtbBookQuestionDetailVo;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookQuestionMapper;
import cn.dutp.book.domain.DtbBookQuestion;
import cn.dutp.book.service.IDtbBookQuestionService;
import cn.dutp.book.service.IDtbUserQuestionService;



/**
 * 数字教材习题Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class DtbBookQuestionServiceImpl extends ServiceImpl<DtbBookQuestionMapper, DtbBookQuestion> implements IDtbBookQuestionService
{
    @Autowired
    private DtbBookQuestionMapper dtbBookQuestionMapper;

    @Autowired
    private IDtbUserQuestionService userQuestionService;


    /**
     * 查询数字教材习题
     *
     * @param bookQuestionId 数字教材习题主键
     * @return 数字教材习题
     */
    @Override
    public DtbBookQuestion selectDtbBookQuestionByBookQuestionId(Long bookQuestionId)
    {
        DtbBookQuestion bookQuestion = this.getById(bookQuestionId);
        if (bookQuestion != null && bookQuestion.getUserQuestionId() != null) {
            // 直接通过 userQuestionService 获取关联的用户习题
            DtbUserQuestion userQuestion = userQuestionService.selectDtbUserQuestionByQuestionId(bookQuestion.getUserQuestionId());
            if (userQuestion != null) {
                // 设置用户习题相关信息
                bookQuestion.setUserQuestion(userQuestion);
            }
        }
        return bookQuestion;
    }

    /**
     * 查询数字教材习题列表
     *
     * @param dtbBookQuestion 数字教材习题
     * @return 数字教材习题
     */
    @Override
    public List<DtbBookQuestion> selectDtbBookQuestionList(DtbBookQuestion dtbBookQuestion)
    {
        LambdaQueryWrapper<DtbBookQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookQuestion.getQuestionType())) {
                lambdaQueryWrapper.eq(DtbBookQuestion::getQuestionType
                ,dtbBookQuestion.getQuestionType());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestion.getChapterId())) {
                lambdaQueryWrapper.eq(DtbBookQuestion::getChapterId
                ,dtbBookQuestion.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestion.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookQuestion::getBookId
                ,dtbBookQuestion.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestion.getSort())) {
                lambdaQueryWrapper.eq(DtbBookQuestion::getSort
                ,dtbBookQuestion.getSort());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestion.getFolderId())) {
                lambdaQueryWrapper.eq(DtbBookQuestion::getFolderId
                ,dtbBookQuestion.getFolderId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestion.getUserQuestionId())) {
                lambdaQueryWrapper.eq(DtbBookQuestion::getUserQuestionId
                ,dtbBookQuestion.getUserQuestionId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增数字教材习题
     *
     * @param dtbBookQuestion 数字教材习题
     * @return 结果
     */
    @Override
    public DtbBookQuestion insertDtbBookQuestion(DtbBookQuestion dtbBookQuestion) {
        // 如果包含用户习题信息
        if (dtbBookQuestion.getUserQuestion() != null) {
            DtbUserQuestion userQuestion = dtbBookQuestion.getUserQuestion();
            // 如果有userQuestionId，说明是已存在的题目，需要更新
            if (dtbBookQuestion.getUserQuestionId() != null) {
                userQuestion.setQuestionId(dtbBookQuestion.getUserQuestionId());
                userQuestionService.updateDtbUserQuestion(userQuestion);
            } else {
                // 新题目，执行插入
                boolean userQuestionResult = userQuestionService.insertDtbUserQuestion(userQuestion);
                if (userQuestionResult) {
                    dtbBookQuestion.setUserQuestionId(userQuestion.getQuestionId());
                }
            }
        }
         this.save(dtbBookQuestion);
        return dtbBookQuestion;
    }

    /**
     * 修改数字教材习题
     *
     * @param dtbBookQuestion 数字教材习题
     * @return 结果
     */
    @Override
    public DtbBookQuestion updateDtbBookQuestion(DtbBookQuestion dtbBookQuestion) {
        // 如果包含用户习题信息
        if (dtbBookQuestion.getUserQuestion() != null) {
            DtbUserQuestion userQuestion = dtbBookQuestion.getUserQuestion();
            // 如果有userQuestionId，执行更新
            if (dtbBookQuestion.getUserQuestionId() != null) {
                userQuestion.setQuestionId(dtbBookQuestion.getUserQuestionId());
                userQuestionService.updateDtbUserQuestion(userQuestion);
            } else {
                // 没有ID，说明是新增的用户习题
                boolean userQuestionResult = userQuestionService.insertDtbUserQuestion(userQuestion);
                if (userQuestionResult) {
                    dtbBookQuestion.setUserQuestionId(userQuestion.getQuestionId());
                }
            }
        }
        this.updateById(dtbBookQuestion);
        return dtbBookQuestion;
    }

    /**
     * 批量删除数字教材习题
     *
     * @param bookQuestionIds 需要删除的数字教材习题主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookQuestionByBookQuestionIds(List<Long> bookQuestionIds)
    {
        return dtbBookQuestionMapper.updateDelFlagToTwo(bookQuestionIds);
    }



    /**
     * 查询数字教材习题列表(包含选项)
     *
     * @return 数字教材习题
     */
    @Override
    public List<DtbBookQuestionDetailVo> selectDtbUserQuestionListWithOptions(DtbBookQuestion dtbBookQuestion) {
        return dtbBookQuestionMapper.selectDtbUserQuestionListWithOptions(dtbBookQuestion);
    }

    /**
     * 将数字教材习题移入回收站
     *
     * @param bookQuestionIds 需要移入回收站的数字教材习题主键集合
     * @return 结果
     */
    @Override
    public boolean moveToRecycleBin(List<Long> bookQuestionIds)
    {
        // 使用 MyBatis-Plus 的逻辑删除功能
        return dtbBookQuestionMapper.moveToRecycleBin(bookQuestionIds);
    }

    /**
     * 从回收站恢复数字教材习题
     *
     * @param bookQuestionIds 需要恢复的数字教材习题主键集合
     * @return 结果
     */
    @Override
    public boolean restoreFromRecycleBin(List<Long> bookQuestionIds)
    {
        // 恢复逻辑删除的数据
        return dtbBookQuestionMapper.restoreByIds(bookQuestionIds);
    }

    @Override
    public List<BookQuestionRecycleVO> recycleBinList(DtbBookQuestion dtbBookQuestion) {
        return dtbBookQuestionMapper.recycleBinList(dtbBookQuestion);
    }

    @Override
    public List<DtbBookQuestion> importQuestions(List<DtbBookQuestion> dtbBookQuestions) {
        int successCount = 0;
        int failCount = 0;
        
        if (StringUtils.isEmpty(dtbBookQuestions)) {
            throw new ServiceException("导入数据为空");
        }

        for (DtbBookQuestion question : dtbBookQuestions) {
            try {
                // 如果包含用户习题信息，需要先处理用户习题
                if (question.getUserQuestion() != null) {
                    DtbUserQuestion userQuestion = question.getUserQuestion();
                    // 保存用户习题基本信息
                    userQuestionService.insertDtbUserQuestion(userQuestion);
                    // 设置关联ID
                    question.setUserQuestionId(userQuestion.getQuestionId());

                    // 保存教材习题基本信息
                    this.save(question);
                    successCount++;
                }
            } catch (Exception e) {
                log.error("导入题目失败", e);
                failCount++;
            }
        }

        if (successCount == 0) {
            throw new ServiceException("导入失败，请检查导入模板是否正确");
        }

        String format = String.format("导入完成：成功%d道题目，失败%d道题目", successCount, failCount);
        return dtbBookQuestions;
    }
}
