package cn.dutp.book.service.impl;

import cn.dutp.book.domain.MoocPsychologyHealthChapter;
import cn.dutp.book.mapper.MoocPsychologyHealthChapterMapper;
import cn.dutp.book.mapper.MoocPsychologyHealthUserResultMapper;
import cn.dutp.book.service.IMoocPsychologyHealthChapterService;
import com.alibaba.druid.sql.visitor.functions.If;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 心理健康量与章节关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class MoocPsychologyHealthChapterServiceImpl extends ServiceImpl<MoocPsychologyHealthChapterMapper, MoocPsychologyHealthChapter> implements IMoocPsychologyHealthChapterService
{
    @Autowired
    private MoocPsychologyHealthChapterMapper moocPsychologyHealthChapterMapper;
    @Autowired
    private MoocPsychologyHealthUserResultMapper moocPsychologyHealthUserResultMapper;

    /**
     * 新增心理健康量与章节关系
     *
     * @param moocPsychologyHealthChapter 心理健康量与章节关系
     * @return 结果
     */
    @Override
    public boolean insertMoocPsychologyHealthChapter(MoocPsychologyHealthChapter moocPsychologyHealthChapter)
    {
        List<MoocPsychologyHealthChapter> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(moocPsychologyHealthChapter.getScaleIds())) {
            moocPsychologyHealthChapter.getScaleIds().stream().forEach(e -> {
                MoocPsychologyHealthChapter moocPsychologyHealth = new MoocPsychologyHealthChapter();
                moocPsychologyHealth.setChapterId(moocPsychologyHealthChapter.getChapterId());
                moocPsychologyHealth.setBookId(moocPsychologyHealthChapter.getBookId());
                moocPsychologyHealth.setScaleId(e);
                list.add(moocPsychologyHealth);
            });
            return this.saveBatch(list);
        }
        return false;
    }

    /**
     * 批量删除心理健康量与章节关系
     *
     * @param healthChapterIds 需要删除的心理健康量与章节关系主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocPsychologyHealthChapterByHealthChapterIds(List<Long> healthChapterIds)
    {
        return this.removeByIds(healthChapterIds);
    }

}
