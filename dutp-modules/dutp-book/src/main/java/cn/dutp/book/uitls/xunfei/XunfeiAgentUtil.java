package cn.dutp.book.uitls.xunfei;

import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.JsonArray;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;

import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class XunfeiAgentUtil {

    public static String formulaRecognition(String apiKey, String apiSecret, String flowId, String formulaRecognitionUrl, JSONObject parameters) throws IOException {
        URL url = new URL(formulaRecognitionUrl);
        HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();

        // 配置 SSL（如果需要跳过证书验证，可以配置 TrustManager）
        conn.setSSLSocketFactory((HttpsURLConnection.getDefaultSSLSocketFactory()));
        conn.setRequestMethod("POST");
        conn.setConnectTimeout(120_000);
        conn.setReadTimeout(120_000);

        // 设置 headers
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("Accept", "text/event-stream");
        conn.setRequestProperty("Authorization", "Bearer " + apiKey + ":" + apiSecret);
        conn.setDoOutput(true);

        // 构造 JSON 数据
        String payload = getParam(flowId, parameters);
        System.out.println("payload = " + payload);

        // 发送请求体
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = payload.getBytes(StandardCharsets.UTF_8);
            os.write(input);
        }
        String response = null;
        // 读取 stream 参数，决定是否分段读取
        boolean stream = false; // 你可以改成 true 看效果
        if (stream) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = br.readLine()) != null) {
                    System.out.println(line);
                    return line;
                }
            }
        } else {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                response = br.readLine();
                System.out.println(response);
            }
        }
        conn.disconnect();
        return response;
    }

    private static String getParam(String flowId, JSONObject parameters) throws IOException {
        JSONObject payload = new JSONObject();
        payload.put("flow_id", flowId);
//        payload.put("uid", "123");

//        JSONObject parameters = new JSONObject();
//        parameters.put("AGENT_USER_INPUT", "识别图中公式，将公式以LaTeX字符串返回");
//        parameters.put("question", "识别图中公式，将公式以LaTeX字符串返回");
//        parameters.put("fileUrl", "http://sgw-dx.xf-yun.com/api/v1/spkdesk2/85cc5926-524d-4d5a-a274-cc64e43d26f3.png?authorization=c2ltcGxlLWp3dCBhaz1zcGtkZXNrMmQ0YzM1YjBjO2V4cD0xOTE2MDM3NTE5O2FsZ289aG1hYy1zaGEyNTY7c2lnPTdIUzBBd0JUdmpIMmNmdmsyTjdwdkxzek9ZUzNMYlErbk50dWxma0pjODA9&x_location=7YfQJjZB7uKtx2GYyYUlf6Fn");
        payload.put("parameters", parameters);

        payload.put("stream", false);

        String jsonString = payload.toString();
        System.out.println("rucan: " + jsonString);
        return jsonString;
    }

    public static String parseResponseContent(String jsonResponse) {
        try {
            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(jsonResponse, JsonObject.class);

            // 检查响应是否成功
            if (jsonObject.get("code").getAsInt() != 0) {
                throw new RuntimeException("API响应错误: " + jsonObject.get("message").getAsString());
            }

            // 获取choices数组
            JsonArray choices = jsonObject.getAsJsonArray("choices");
            if (choices.size() == 0) {
                throw new RuntimeException("响应中没有choices数据");
            }

            // 获取第一个choice
            JsonObject firstChoice = choices.get(0).getAsJsonObject();
            JsonObject delta = firstChoice.getAsJsonObject("delta");

            // 提取content字段
            String content = delta.get("content").getAsString();

            // 处理转义字符（将双反斜杠转换为单反斜杠）
            content = content.replace("\\\\", "\\");

            return content;
        } catch (Exception e) {
            throw new RuntimeException("解析API响应失败: " + e.getMessage(), e);
        }
    }
}
