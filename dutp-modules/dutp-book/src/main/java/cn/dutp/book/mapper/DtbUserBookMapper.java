package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.domain.vo.DtbUserBookVo;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * DUTP-DTB_014学生/教师书架Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Repository
public interface DtbUserBookMapper extends BaseMapper<DtbUserBook>
{
    /**
     * 学生教师端我的教材查询
     *
     * @param dtbUserBook
     * @return 查询结果
     */
    public List<DtbUserBookVo> getInfoEducation(DtbUserBook dtbUserBook);

    Long selectUserCountByType(@Param("book") DtbUserBook book, @Param("userType") String userType, @Param("schoolId") Long schoolId);

    List<DtbUserBook> selectByBookIdAndSchoolId(@Param("bookId") Long bookId, @Param("schoolId") Long schoolId);
}
