package cn.dutp.book.mapper;


import cn.dutp.book.domain.DtbBookChapterData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 章节数据统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Repository
public interface DtbBookChapterDataMapper extends BaseMapper<DtbBookChapterData> {

    List<DtbBookChapterData> selectDtbBookChapterDataList(DtbBookChapterData dtbBookChapterData);

    List<DtbBookChapterData> dataOverview(DtbBookChapterData dtbBookChapterData);

    DtbBookChapterData dataOverviewOne(DtbBookChapterData dtbBookChapterData);

    DtbBookChapterData queryChapterDataByChapterSortAndBookId(@Param("chapterId") Long chapterId);

    DtbBookChapterData queryChapterDataByChapterId(Long chapterId);
}
