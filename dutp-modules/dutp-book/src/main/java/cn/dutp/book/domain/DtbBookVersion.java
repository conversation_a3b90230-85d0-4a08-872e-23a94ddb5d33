package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 电子教材版本对象 dtb_book_version
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@TableName("dtb_book_version")
public class DtbBookVersion extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long versionId;

    /**
     * 版本号第一个版本默认1.0.0
     */
    @Excel(name = "版本号第一个版本默认1.0.0")
    private String versionNo;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 版本说明
     */
    @Excel(name = "版本说明")
    private String versionIntroduce;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 0不需处理1未处理2通过3驳回
     */
    @TableField(exist = false)
    private Integer state;

    /**
     * 步骤名称
     */
    @TableField(exist = false)
    private String stepName;

    /**
     * 步骤名称
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stepId;

    /**
     * 录排编号
     */
    @TableField(exist = false)
    private String recordNo;

    /**
     * 发起人Id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long promoterUserId;

    /**
     * 发起人姓名
     */
    @TableField(exist = false)
    private String promoterUserName;

    /**
     * 1其他2主教材3副教材
     */
    @TableField(exist = false)
    private Integer masterFlag;

    /**
     * 1公开教材2校本教材
     */
    @Excel(name = "1公开教材2校本教材")
    @TableField(exist = false)
    private Integer bookOrganize;
    /**
     * 流程ID
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "流程ID")
    private Long processId;

    /**
     * 审核人ID
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditUserId;

}
