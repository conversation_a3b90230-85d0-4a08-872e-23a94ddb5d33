<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookTestPaperMapper">
    
    <resultMap type="DtbBookTestPaper" id="DtbBookTestPaperResult">
        <result property="bookPaperId"    column="book_paper_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbBookTestPaperVo">
        select book_paper_id, book_id, paper_id, chapter_id, create_by, create_time, update_by, update_time from dtb_book_test_paper
    </sql>

    <select id="selectDtbBookTestPaperList" parameterType="DtbBookTestPaper" resultMap="DtbBookTestPaperResult">
        <include refid="selectDtbBookTestPaperVo"/>
        <where>  
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="paperId != null "> and paper_id = #{paperId}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
        </where>
    </select>
    
    <select id="selectDtbBookTestPaperByBookPaperId" parameterType="Long" resultMap="DtbBookTestPaperResult">
        <include refid="selectDtbBookTestPaperVo"/>
        where book_paper_id = #{bookPaperId}
    </select>


    <insert id="insertDtbBookTestPaper" parameterType="DtbBookTestPaper" useGeneratedKeys="true" keyProperty="bookPaperId">
        insert into dtb_book_test_paper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="paperId != null">paper_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="paperId != null">#{paperId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbBookTestPaper" parameterType="DtbBookTestPaper">
        update dtb_book_test_paper
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where book_paper_id = #{bookPaperId}
    </update>

    <delete id="deleteDtbBookTestPaperByBookPaperId" parameterType="Long">
        delete from dtb_book_test_paper where book_paper_id = #{bookPaperId}
    </delete>

    <delete id="deleteDtbBookTestPaperByBookPaperIds" parameterType="String">
        delete from dtb_book_test_paper where book_paper_id in 
        <foreach item="bookPaperId" collection="array" open="(" separator="," close=")">
            #{bookPaperId}
        </foreach>
    </delete>

    <select id="getPaperList" resultType="cn.dutp.book.domain.DtbBookTestPaper">
        select
            a.book_paper_id,
            a.chapter_id,
            a.chapter_name,
            a.paper_id,
            a.paper_title,
            a.question_quantity,
            a.total_score,
            a.state,
            a.page_number,
            a.dom_id
        from (
            select
            btp.book_paper_id,
            btp.chapter_id,
            bc.chapter_name,
            tp.paper_id,
            tp.paper_title,
            tp.question_quantity,
            tp.total_score,
            btp.page_number,
            btp.dom_id,
            case when GROUP_CONCAT(tap.paper_answer_id) is null then 0 else 1 end state
            from dtb_book_test_paper btp
            inner join dtb_test_paper tp on btp.paper_id = tp.paper_id
            inner join dtb_book_chapter bc on btp.chapter_id = bc.chapter_id
            <if test="param.isFree == 2">
                and bc.free = 2
            </if>
            left join dtb_book_test_paper_answer tap on btp.book_paper_id = tap.book_paper_id and tap.user_id = #{userId}
            where tp.del_flag = 0 and btp.book_id = #{param.bookId} and tp.paper_type = #{param.paperType}
            <if test="param.versionId != null">
                AND bc.version_id = #{param.versionId}
            </if>
            group by btp.book_paper_id,btp.chapter_id,chapter_name,tp.paper_id,tp.paper_title,tp.question_quantity,tp.total_score,btp.page_number
        ) a where 1=1
        <if test="param.chapterId != null and param.chapterId != ''">
            AND a.chapter_id = #{param.chapterId}
        </if>
        <if test="param.state == 1">
            AND a.state = 1
        </if>
        <if test="param.state == 0">
            AND a.state = 0
        </if>
    </select>
</mapper>