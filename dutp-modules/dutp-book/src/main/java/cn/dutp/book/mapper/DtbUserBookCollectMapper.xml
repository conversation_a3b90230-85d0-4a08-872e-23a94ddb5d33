<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookCollectMapper">
    
    <resultMap type="DtbUserBookCollectVo" id="DtbUserBookCollectResult">
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="bookName"    column="book_name"    />
        <result property="cover"    column="cover"    />
        <result property="isbn"    column="isbn"    />
    </resultMap>

    <sql id="selectDtbUserBookCollectVo">
        SELECT
            dubc.book_id,
            dubc.user_id,
            dubc.create_by,
            dubc.create_time,
            dubc.update_by,
            dubc.update_time,
            db.book_name,
            db.cover,
            db.issn,
            db.isbn
        FROM
            dtb_user_book_collect dubc LEFT JOIN dtb_book db
            ON dubc.book_id = db.book_id
    </sql>

    <select id="selectDtbUserBookCollectList" parameterType="DtbUserBookCollect" resultMap="DtbUserBookCollectResult">
        <include refid="selectDtbUserBookCollectVo"/>
        <where>
            <if test="bookId != null "> and dubc.book_id = #{bookId}</if>
            <if test="userId != null "> and dubc.user_id = #{userId}</if>
        </where>
        order by dubc.create_time desc
    </select>
    
    <select id="selectDtbUserBookCollectByUserId" parameterType="Long" resultMap="DtbUserBookCollectResult">
        <include refid="selectDtbUserBookCollectVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertDtbUserBookCollect" parameterType="DtbUserBookCollect" useGeneratedKeys="true" keyProperty="userId">
        insert into dtb_user_book_collect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBookCollect" parameterType="DtbUserBookCollect">
        update dtb_user_book_collect
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteDtbUserBookCollectByUserId" parameterType="Long">
        delete from dtb_user_book_collect where user_id = #{userId}
    </delete>

    <delete id="deleteDtbUserBookCollectByUserIds" parameterType="String">
        delete from dtb_user_book_collect where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>