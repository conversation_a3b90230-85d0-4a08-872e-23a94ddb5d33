package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookVersionMapper;
import cn.dutp.book.domain.DtbBookVersion;
import cn.dutp.book.service.IDtbBookVersionService;

/**
 * 电子教材版本Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class DtbBookVersionServiceImpl extends ServiceImpl<DtbBookVersionMapper, DtbBookVersion> implements IDtbBookVersionService
{
    @Autowired
    private DtbBookVersionMapper dtbBookVersionMapper;

    /**
     * 查询电子教材版本
     *
     * @param versionId 电子教材版本主键
     * @return 电子教材版本
     */
    @Override
    public DtbBookVersion selectDtbBookVersionByVersionId(Long versionId)
    {
        return this.getById(versionId);
    }

    /**
     * 查询电子教材版本列表
     *
     * @param dtbBookVersion 电子教材版本
     * @return 电子教材版本
     */
    @Override
    public List<DtbBookVersion> selectDtbBookVersionList(DtbBookVersion dtbBookVersion)
    {
        LambdaQueryWrapper<DtbBookVersion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookVersion.getVersionNo())) {
                lambdaQueryWrapper.eq(DtbBookVersion::getVersionNo
                ,dtbBookVersion.getVersionNo());
            }
                if(ObjectUtil.isNotEmpty(dtbBookVersion.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookVersion::getBookId
                ,dtbBookVersion.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookVersion.getVersionIntroduce())) {
                lambdaQueryWrapper.eq(DtbBookVersion::getVersionIntroduce
                ,dtbBookVersion.getVersionIntroduce());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增电子教材版本
     *
     * @param dtbBookVersion 电子教材版本
     * @return 结果
     */
    @Override
    public boolean insertDtbBookVersion(DtbBookVersion dtbBookVersion)
    {
        return this.save(dtbBookVersion);
    }

    /**
     * 修改电子教材版本
     *
     * @param dtbBookVersion 电子教材版本
     * @return 结果
     */
    @Override
    public boolean updateDtbBookVersion(DtbBookVersion dtbBookVersion)
    {
        return this.updateById(dtbBookVersion);
    }

    /**
     * 批量删除电子教材版本
     *
     * @param versionIds 需要删除的电子教材版本主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookVersionByVersionIds(List<Long> versionIds)
    {
        return this.removeByIds(versionIds);
    }

    @Override
    public List<DtbBookVersion> versionList(DtbBookVersion dtbBookVersion) {
        return dtbBookVersionMapper.versionList(dtbBookVersion);
    }

    @Override
    public List<DtbBookVersion> versionListNotPage(DtbBookVersion dtbBookVersion) {
        LambdaQueryWrapper<DtbBookVersion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbBookVersion.getVersionNo())) {
            lambdaQueryWrapper.eq(DtbBookVersion::getVersionNo
                    ,dtbBookVersion.getVersionNo());
        }
        if(ObjectUtil.isNotEmpty(dtbBookVersion.getBookId())) {
            lambdaQueryWrapper.eq(DtbBookVersion::getBookId
                    ,dtbBookVersion.getBookId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookVersion.getVersionIntroduce())) {
            lambdaQueryWrapper.eq(DtbBookVersion::getVersionIntroduce
                    ,dtbBookVersion.getVersionIntroduce());
        }
        lambdaQueryWrapper.orderByDesc(DtbBookVersion::getCreateTime);
        return this.list(lambdaQueryWrapper);
    }
}
