package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookResourceFolder;
import cn.dutp.book.domain.vo.ResourceVO;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教材资源文件夹Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IDtbBookResourceFolderService extends IService<DtbBookResourceFolder>
{
    /**
     * 查询教材资源文件夹
     *
     * @param folderId 教材资源文件夹主键
     * @return 教材资源文件夹
     */
    public DtbBookResourceFolder selectDtbBookResourceFolderByFolderId(Long folderId);

    /**
     * 查询教材资源文件夹列表
     *
     * @param dtbBookResourceFolder 教材资源文件夹
     * @return 教材资源文件夹集合
     */
    public List<DtbBookResourceFolder> selectDtbBookResourceFolderList(DtbBookResourceFolder dtbBookResourceFolder);

    /**
     * 新增教材资源文件夹
     *
     * @param dtbBookResourceFolder 教材资源文件夹
     * @return 结果
     */
    public boolean insertDtbBookResourceFolder(DtbBookResourceFolder dtbBookResourceFolder);

    /**
     * 修改教材资源文件夹
     *
     * @param dtbBookResourceFolder 教材资源文件夹
     * @return 结果
     */
    public boolean updateDtbBookResourceFolder(DtbBookResourceFolder dtbBookResourceFolder);

    /**
     * 批量删除教材资源文件夹
     *
     * @param folderIds 需要删除的教材资源文件夹主键集合
     * @return 结果
     */
    public boolean deleteDtbBookResourceFolderByFolderIds(List<Long> folderIds);

    List<ResourceVO> listBookResourcefolderResource(ResourceVO dtbBookResourceFolder);
}
