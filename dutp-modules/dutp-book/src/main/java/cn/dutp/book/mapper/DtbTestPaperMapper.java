package cn.dutp.book.mapper;

import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbTestPaper;
import cn.dutp.book.domain.DtbTestPaperQuestionCollection;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试卷Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Repository
public interface DtbTestPaperMapper extends BaseMapper<DtbTestPaper>
{

   

    /**
     * 查询试卷列表
     *
     * @param dtbTestPaper 试卷信息
     * @return 试卷列表
     */
    List<DtbTestPaper> selectDtbTestPaperList(DtbTestPaper dtbTestPaper);

    /**
     * 根据试卷ID查询试卷信息
     *
     * @param paperId 试卷ID
     * @return 试卷信息
     */
    DtbTestPaper selectDtbTestPaperByPaperId(Long paperId);

    /**
     * 查询试卷题型集合列表
     *
     * @param paperId 试卷ID
     * @return 试卷题型集合列表
     */
    List<DtbTestPaperQuestionCollection> selectDtbTestPaperQuestionCollectionList(Long paperId);

    /**
     * 新增试卷
     *
     * @param dtbTestPaper 试卷信息
     * @return 影响行数
     */
    int insertDtbTestPaper(DtbTestPaper dtbTestPaper);

    /**
     * 修改试卷
     *
     * @param dtbTestPaper 试卷信息
     * @return 影响行数
     */
    int updateDtbTestPaper(DtbTestPaper dtbTestPaper);

    /**
     * 删除试卷
     *
     * @param paperId 试卷ID
     * @return 影响行数
     */
    int deleteDtbTestPaperByPaperId(Long paperId);

    /**
     * 批量删除试卷
     *
     * @param paperIds 需要删除的试卷ID数组
     * @return 影响行数
     */
    int deleteDtbTestPaperByPaperIds(Long[] paperIds);

    /**
     * 查询回收站列表
     *
     * @param dtbTestPaper 试卷信息
     * @return 回收站列表
     */
    List<DtbTestPaper> selectRecycleBinList(DtbTestPaper dtbTestPaper);

    int moveToRecycleBin(@Param("paperIds") List<Long> paperIds, @Param("username") String username);
    
    int restoreFromRecycleBin(@Param("paperIds") List<Long> paperIds, @Param("username") String username);

    int deleteBatchByIds(@Param("paperIds") List<Long> paperIds, @Param("userId") Long userId);
}
