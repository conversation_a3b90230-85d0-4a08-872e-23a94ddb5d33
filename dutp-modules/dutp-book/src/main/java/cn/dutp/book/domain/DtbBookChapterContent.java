package cn.dutp.book.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * 数字教材章节内容对象
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Data
public class DtbBookChapterContent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * mongodbID
     */
    @Id
    @Field(value = "_id")
    private String id;

    /**
     * 章节内容
     */
    private String content;

    /**
     * 开始的页码
     */
    private Integer startPageNumber;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
}
