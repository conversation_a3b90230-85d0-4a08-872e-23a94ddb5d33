package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookHistory;
import cn.dutp.book.service.IDtbBookHistoryService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 数字教材保存历史Controller
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@RestController
@RequestMapping("/history")
public class DtbBookHistoryController extends BaseController
{
    @Autowired
    private IDtbBookHistoryService dtbBookHistoryService;

/**
 * 查询数字教材保存历史列表
 */
@GetMapping("/list")
    public TableDataInfo list(DtbBookHistory dtbBookHistory)
    {
        startPage();
        List<DtbBookHistory> list = dtbBookHistoryService.selectDtbBookHistoryList(dtbBookHistory);
        return getDataTable(list);
    }

    /**
     * 导出数字教材保存历史列表
     */
    @RequiresPermissions("book:history:export")
    @Log(title = "数字教材保存历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookHistory dtbBookHistory)
    {
        List<DtbBookHistory> list = dtbBookHistoryService.selectDtbBookHistoryList(dtbBookHistory);
        ExcelUtil<DtbBookHistory> util = new ExcelUtil<DtbBookHistory>(DtbBookHistory.class);
        util.exportExcel(response, list, "数字教材保存历史数据");
    }

    /**
     * 获取数字教材保存历史详细信息
     */
    @RequiresPermissions("book:history:query")
    @GetMapping(value = "/{historyId}")
    public AjaxResult getInfo(@PathVariable("historyId") Long historyId)
    {
        return success(dtbBookHistoryService.selectDtbBookHistoryByHistoryId(historyId));
    }

    /**
     * 新增数字教材保存历史
     */
    @RequiresPermissions("book:history:add")
    @Log(title = "数字教材保存历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookHistory dtbBookHistory)
    {
        return toAjax(dtbBookHistoryService.insertDtbBookHistory(dtbBookHistory));
    }

    /**
     * 修改数字教材保存历史
     */
    @RequiresPermissions("book:history:edit")
    @Log(title = "数字教材保存历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookHistory dtbBookHistory)
    {
        return toAjax(dtbBookHistoryService.updateDtbBookHistory(dtbBookHistory));
    }

    /**
     * 删除数字教材保存历史
     */
    @RequiresPermissions("book:history:remove")
    @Log(title = "数字教材保存历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{historyIds}")
    public AjaxResult remove(@PathVariable Long[] historyIds)
    {
        return toAjax(dtbBookHistoryService.deleteDtbBookHistoryByHistoryIds(Arrays.asList(historyIds)));
    }
}
