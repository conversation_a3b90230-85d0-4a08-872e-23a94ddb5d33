package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookShareComment;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 教材分享点评Service接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface IDtbBookShareCommentService extends IService<DtbBookShareComment>
{
    public AjaxResult selectDtbBookShareCommentList(ReaderCommonForm readerForm);

    /**
     * 新增DUTP-DTB_022点评/标注
     *
     * @param dtbUserBookNote DUTP-DTB_022点评/标注
     * @return 结果
     */
    public AjaxResult insertShareComment(DtbBookShareComment dtbBookShareComment);

    /**
     * 修改DUTP-DTB_022点评/标注
     *
     * @param dtbUserBookNote DUTP-DTB_022点评/标注
     * @return 结果
     */
    public AjaxResult updateShareComment(DtbBookShareComment dtbBookShareComment);

    /**
     * 批量删除DUTP-DTB_022点评/标注
     *
     * @param noteIds 需要删除的DUTP-DTB_022点评/标注主键集合
     * @return 结果
     */
    public boolean deleteShareCommentByCommentIds(List<Long> commentIds);

    void exportShareComment(HttpServletResponse response, DtbBookShareComment dtbBookShareComment);

}
