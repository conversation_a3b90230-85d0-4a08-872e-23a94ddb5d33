package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbUserBookCollect;
import cn.dutp.book.domain.DtbUserBookFootPrint;
import cn.dutp.book.domain.vo.DtbUserBookCollectVo;
import cn.dutp.book.domain.vo.DtbUserBookFootPrintVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB_015收藏教材Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@Repository
public interface DtbUserBookCollectMapper extends BaseMapper<DtbUserBookCollect>
{
    /**
     * 收藏查询
     *
     * @param dtbUserBookCollect
     * @return 结果
     */
    public List<DtbUserBookCollectVo> selectDtbUserBookCollectList(DtbUserBookCollect dtbUserBookCollect);
}
