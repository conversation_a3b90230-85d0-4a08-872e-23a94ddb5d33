package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookTemplateImage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 教材模板图片Service接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public interface IDtbBookTemplateImageService extends IService<DtbBookTemplateImage> {

    /**
     * 查询教材模板图片列表
     *
     * @param dtbBookTemplateImage 教材模板图片
     * @return 教材模板图片集合
     */
    public List<DtbBookTemplateImage> selectDtbBookTemplateImageList(DtbBookTemplateImage dtbBookTemplateImage);

    /**
     * 新增教材模板图片
     *
     * @param dtbBookTemplateImage 教材模板图片
     * @return 结果
     */
    public boolean insertDtbBookTemplateImage(DtbBookTemplateImage dtbBookTemplateImage);

    /**
     * 修改教材模板图片
     *
     * @param dtbBookTemplateImage 教材模板图片
     * @return 结果
     */
    public boolean updateDtbBookTemplateImage(DtbBookTemplateImage dtbBookTemplateImage);

    /**
     * 批量删除教材模板图片
     *
     * @param imageIds 需要删除的教材模板图片主键集合
     * @return 结果
     */
    public boolean deleteDtbBookTemplateImageByImageIds(List<Long> imageIds);

    List<DtbBookTemplateImage> listForAuthor(DtbBookTemplateImage dtbBookTemplateImage);
}
