<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookTemplateTypeMapper">
    
    <resultMap type="DtbBookTemplateType" id="DtbBookTemplateTypeResult">
        <result property="typeId"    column="type_id"    />
        <result property="typeName"    column="type_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbBookTemplateTypeVo">
        select type_id, type_name, del_flag, create_by, create_time, update_by, update_time from dtb_book_template_type
    </sql>

    <select id="selectDtbBookTemplateTypeList" parameterType="DtbBookTemplateType" resultMap="DtbBookTemplateTypeResult">
        <include refid="selectDtbBookTemplateTypeVo"/>
        <where>  
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
        </where>
    </select>
    
    <select id="selectDtbBookTemplateTypeByTypeId" parameterType="Long" resultMap="DtbBookTemplateTypeResult">
        <include refid="selectDtbBookTemplateTypeVo"/>
        where type_id = #{typeId}
    </select>

    <insert id="insertDtbBookTemplateType" parameterType="DtbBookTemplateType" useGeneratedKeys="true" keyProperty="typeId">
        insert into dtb_book_template_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null">type_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null">#{typeName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbBookTemplateType" parameterType="DtbBookTemplateType">
        update dtb_book_template_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null">type_name = #{typeName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where type_id = #{typeId}
    </update>

    <delete id="deleteDtbBookTemplateTypeByTypeId" parameterType="Long">
        delete from dtb_book_template_type where type_id = #{typeId}
    </delete>

    <delete id="deleteDtbBookTemplateTypeByTypeIds" parameterType="String">
        delete from dtb_book_template_type where type_id in 
        <foreach item="typeId" collection="array" open="(" separator="," close=")">
            #{typeId}
        </foreach>
    </delete>
</mapper>