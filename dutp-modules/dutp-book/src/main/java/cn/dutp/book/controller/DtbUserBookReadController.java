package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbUserBookRead;
import cn.dutp.book.service.IDtbUserBookReadService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.utils.SecurityUtils;
/**
 * 用户阅读记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/read")
public class DtbUserBookReadController extends BaseController
{
    @Autowired
    private IDtbUserBookReadService dtbUserBookReadService;

    /**
     * 查询用户阅读记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserBookRead dtbUserBookRead)
    {

        startPage();
        dtbUserBookRead.setUserId(SecurityUtils.getUserId());
        List<DtbUserBookRead> list = dtbUserBookReadService.selectDtbUserBookReadList(dtbUserBookRead);
        return getDataTable(list);
    }

    /**
     * 导出用户阅读记录列表
     */
    @RequiresPermissions("book:read:export")
    @Log(title = "导出用户阅读记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserBookRead dtbUserBookRead)
    {
        List<DtbUserBookRead> list = dtbUserBookReadService.selectDtbUserBookReadList(dtbUserBookRead);
        ExcelUtil<DtbUserBookRead> util = new ExcelUtil<DtbUserBookRead>(DtbUserBookRead.class);
        util.exportExcel(response, list, "用户阅读记录数据");
    }

    /**
     * 获取用户阅读记录详细信息
     */
    @RequiresPermissions("book:read:query")
    @GetMapping(value = "/{readId}")
    public AjaxResult getInfo(@PathVariable("readId") Long readId)
    {
        return success(dtbUserBookReadService.selectDtbUserBookReadByReadId(readId));
    }

    /**
     * 新增用户阅读记录
     */
    @RequiresPermissions("book:read:add")
    @Log(title = "新增用户阅读记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBookRead dtbUserBookRead)
    {
        return dtbUserBookReadService.insertDtbUserBookRead(dtbUserBookRead);
    }

    /**
     * 修改用户阅读记录
     */
    @RequiresPermissions("book:read:edit")
    @Log(title = "修改用户阅读记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBookRead dtbUserBookRead)
    {
        return toAjax(dtbUserBookReadService.updateDtbUserBookRead(dtbUserBookRead));
    }

    /**
     * 删除用户阅读记录
     */
    @RequiresPermissions("book:read:remove")
    @Log(title = "删除用户阅读记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{readIds}")
    public AjaxResult remove(@PathVariable Long[] readIds)
    {
        return toAjax(dtbUserBookReadService.deleteDtbUserBookReadByReadIds(Arrays.asList(readIds)));
    }
}
