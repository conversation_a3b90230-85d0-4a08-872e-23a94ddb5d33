package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookChapterCatalog;
import cn.dutp.book.domain.vo.DtbBookChapterTreeVO;
import cn.dutp.book.domain.vo.DtbBookChapterVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教材章节目录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Repository
public interface DtbBookChapterCatalogMapper extends BaseMapper<DtbBookChapterCatalog> {

    @Select("SELECT catalog_id FROM dtb_book_chapter_catalog WHERE dom_id = #{domId}")
    Long checkIsExist(String domId);

    @Select("SELECT catalog_id, catalog_id as id, title as name, chapter_id, book_id, parent_id, dom_id FROM dtb_book_chapter_catalog WHERE chapter_id = #{chapterId}")
    List<DtbBookChapterTreeVO> queryBookChapterCatalogTreeByChapterId(Long chapterId);

    @Select("SELECT catalog_id, catalog_id as id, title, title as name, chapter_id, dom_id, parent_id, page_number,2 as free FROM dtb_book_chapter_catalog WHERE chapter_id = #{chapterId}")
    List<DtbBookChapterVO> getBookChapterCatalogByReader(Long chapterId);

    List<DtbBookChapterCatalog> queryBookChapterCatalogTree(@Param("chapterId") Long chapterId);
}
