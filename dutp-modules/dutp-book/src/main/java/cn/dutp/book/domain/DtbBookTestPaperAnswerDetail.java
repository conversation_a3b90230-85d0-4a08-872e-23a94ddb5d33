package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 试卷答题明细对象 dtb_book_test_paper_answer_detail
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
@TableName("dtb_book_test_paper_answer_detail")
public class DtbBookTestPaperAnswerDetail extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answerDetailId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperAnswerId;

    /**
     * 题的答题id，dtb_book_question_answer的answer_id
     */
    @Excel(name = "题的答题id，dtb_book_question_answer的answer_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionAnswerId;

    /**
     * 分数0-100
     */
    @Excel(name = "分数0-100")
    private Integer score;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("answerDetailId", getAnswerDetailId())
                .append("paperAnswerId", getPaperAnswerId())
                .append("questionAnswerId", getQuestionAnswerId())
                .append("score", getScore())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
