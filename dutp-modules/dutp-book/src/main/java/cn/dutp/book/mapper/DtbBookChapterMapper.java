package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.vo.DtbBookChapterTreeVO;
import cn.dutp.book.domain.vo.DtbBookChapterVO;
import cn.dutp.domain.DtbBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数字教材章节目录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Repository
public interface DtbBookChapterMapper extends BaseMapper<DtbBookChapter> {

    @Select("select book_id from dtb_book_chapter where chapter_id = #{chapterId} and del_flag = '0'")
    Long queryBookIdByChapterId(Long chapterId);

    List<DtbBookChapterTreeVO> queryBookChapterTreeByBookId(Long bookId);

    List<DtbBookChapterTreeVO> queryBookChapterTreeByBookIdDel(Long bookId);

    List<DtbBookChapter> queryBookChapterListByBookId(Long bookId);

    List<DtbBookChapterTreeVO> adminQueryBookChapterTreeByBookId(Long bookId);

    List<DtbBookChapterTreeVO> adminQueryProcessChapterTreeByBookId(Long bookId);

    List<DtbBookChapterTreeVO> listForRecycle(DtbBookChapter dtbBookChapter);

    @Update("update dtb_book_chapter set del_flag = '0' where chapter_id = #{chapterId}")
    int recycleChapter(Long chapterId);

    List<DtbBookChapter> listForSort(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> dtbBookChapterMapper(@Param("dtbBookChapter") DtbBookChapter dtbBookChapter, @Param("openTree") Boolean openTree);

    List<DtbBookChapter> dtbBookChapterMapperLastVersion(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> listForFinalizedSubmit(DtbBook dtbBook);

    List<DtbBookChapterVO> getBookChapterByReader(@Param("bookId") Long bookId, @Param("versionId") Long versionId);


    List<DtbBookChapterVO> getBookChapterByReaderSimple(@Param("bookId") Long bookId, @Param("versionId") Long versionId);

    List<DtbBookChapterVO> getBookShareChapterByReader(@Param("bookId") Long bookId, @Param("versionId") Long versionId, @Param("shareId") Long shareId);

    @Update("update dtb_book_chapter set del_flag = '2', del_user_id = #{delUserId}, update_time = #{updateTime} where chapter_id = #{chapterId}")
    int delChapter(DtbBookChapter dtbBookChapter);

    @Select("select IFNULL(sum(chapter_total_page),0) startPageNumber from dtb_book_chapter where del_flag = 0 " +
            "and book_id = #{bookId} and sort < (select sort from dtb_book_chapter where chapter_id = #{chapterId})")
    int getBookChapterStartPageNumber(@Param("chapterId") Long chapterId, @Param("bookId") Long bookId);

    List<DtbBookChapter> queryCopyBookChapter(DtbBookChapter dtbBookChapter);

    Long queryChapterId(@Param("bookId") Long bookId, @Param("sort") Integer sort);

    DtbBookChapter chapterInfo(Long chapterId);

    Integer queryMaxSort(@Param("bookId") Long bookId, @Param("parentId") Long parentId);

    int updateChapterTemplate(DtbBookChapter chapter);

    DtbBookChapter queryBookByChapterId(Long chapterId);

    Long queryTemplateIdByBookId(Long bookId);

    @Select("select version_id from dtb_book_chapter where chapter_id = #{chapterId}")
    Long queryVersionIddByChapterId(Long chapterId);

    /**
     * 获取数字教材章节目录列表
     *
     * @param dtbBookChapter
     * @return
     */
    List<DtbBookChapterTreeVO> queryBookChapterDataList(DtbBookChapter dtbBookChapter);

    @Select("select chapter_id, book_id from dtb_book_chapter where chapter_id = #{chapterId}")
    DtbBookChapter queryChapter(Long chapterId);

    List<DtbBookChapter> selectByBookAndVersion(@Param("allBook") List<DtbBook> allBook);

    @Select("select template_id from dtb_book_chapter where chapter_id = #{chapterId}")
    Long queryTemplateIdByChapterId(Long chapterId);

    List<DtbBookChapter> homepageChapterSearchByPage(
            @Param("userId") Long userId,
            @Param("chapterName") String chapterName,
            @Param("pageSize") int pageSize,
            @Param("offset") int offset);

    Long homepageChapterSearchPagetotal(@Param("chapterName") String chapterName);

    @Select("SELECT " +
            "chapter_id, " +
            "chapter_name, " +
            "chapter_object_id, " +
            "book_id, " +
            "sort, " +
            "chapter_total_page, " +
            "version_id, " +
            "free, " +
            "chapter_status, " +
            "back_apply, " +
            "frozen, " +
            "state, " +
            "complete_rate, " +
            "del_user_id, " +
            "template_id, " +
            "del_flag, " +
            "remark " +
            " from dtb_book_chapter where chapter_id = #{chapterId} ")
    DtbBookChapter queryBookChapterById(Long chapterId);

    @Select("WITH RECURSIVE chapter_tree (  chapter_id, parent_id, chapter_name, sort, level, path  ) AS ( " +
            "    SELECT  " +
            "        c.chapter_id, " +
            "        c.parent_id, " +
            "        c.chapter_name, " +
            "        c.sort, " +
            "        1 AS level, " +
            "        CAST(LPAD(10000 - c.sort, 5, '0') AS CHAR(1000)) AS path  " +
            "    FROM dtb_book_chapter c " +
            "    WHERE c.chapter_id = #{chapterId} " +
            "    UNION ALL " +
            "    SELECT  " +
            "        child.chapter_id, " +
            "        child.parent_id, " +
            "        child.chapter_name, " +
            "        child.sort, " +
            "        ct.level + 1, " +
            "        CONCAT(ct.path, '-', LPAD(10000 - child.sort, 5, '0')) AS path " +
            "    FROM dtb_book_chapter child " +
            "    INNER JOIN chapter_tree ct ON child.parent_id = ct.chapter_id " +
            ") " +
            "SELECT chapter_id, parent_id, chapter_name, sort, level, path " +
            "FROM chapter_tree " +
            "ORDER BY path "
    )
    List<DtbBookChapter> queryAllChapterById(@Param("chapterId") Long chapterId);

    Long queryChapterBySortAndBookId(@Param("versionId") Long versionId, @Param("sort") Integer sort, @Param("chapterId") Long chapterId);

    @Select("select IFNULL(parent_id,0) as parent_id from dtb_book_chapter where chapter_id = #{chapterId} and del_flag = 2")
    DtbBookChapter queryChapterOfDel(Long chapterId);

    @Select("select chapter_id from dtb_book_chapter where parent_id = #{chapterId} and del_user_id = #{userId} and del_flag = 2")
    List<DtbBookChapter> queryChapterOfDelList(@Param("chapterId") Long chapterId, @Param("userId") Long userId);

    List<DtbBookChapterTreeVO> queryBookChapterTreeByChapterId(Long chapterId);

    @Select("select IFNULL(parent_id,0) as parent_id from dtb_book_chapter where chapter_id = #{chapterId} and del_flag = 0")
    Long queryParentId(Long chapterId);

    @Select("select chapter_id from dtb_book_chapter where parent_id = #{chapterId} and del_flag = 0")
    List<Long> queryChildChapterIds(Long chapterId);

    void batchUpdateFree(@Param("chapterIds") List<Long> chapterIds, @Param("free") Integer free);

    List<DtbBookChapter> queryAllTreeDataByChapterId(Long chapterId);
}
