<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.MoocPsychologyHealthScaleMapper">

    <select id="getPsychologyHealthFacetById" resultMap="MoocPsychologyHealthScaleFacetResult">
        select
        mf.facet_id,
        mf.facet_name,
        mf.sort as facet_sort,
        mf.scale_id,
        mq.question_id,
        mq.question_content,
        mq.sort as question_sort1,
        mq.question_sort,
        mqo.option_id,
        mqo.option_content,
        mqo.sort as option_sort,
        mqo.score,
        mqo.jump_question_id
        from
        mooc_psychology_health_scale_facet mf
        left join
        mooc_psychology_health_scale_question mq on mf.scale_id = mq.scale_id and mq.facet_id = mf.facet_id and mq.del_flag = 0
        left join
        mooc_psychology_health_scale_question_option mqo on mq.question_id = mqo.question_id and mqo.del_flag = 0
        where
        mf.del_flag = 0 and mf.scale_id = #{scaleId}
        <if test="showSortType == 1">
            order by mf.sort,mq.sort, mqo.sort
        </if>
        <if test="showSortType == 2">
            order by mq.question_sort, mqo.sort
        </if>
    </select>

    <resultMap type="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestion" id="MoocPsychologyHealthScaleResult">
        <result property="questionId" column="question_id"/>
        <result property="questionContent" column="question_content"/>
        <result property="sort" column="question_sort"/>
        <result property="chooseOptionId" column="chooseOptionId"/>
        <collection property="moocPsychologyHealthScaleQuestionOption" ofType="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestionOption">
            <result property="optionId" column="option_id"/>
            <result property="optionContent" column="option_content"/>
            <result property="sort" column="option_sort"/>
            <result property="jumpId" column="jump_question_id"/>
            <result property="jumpQuestionId" column="jump_question_id"/>
            <result property="score" column="score"/>
            <result property="mbtiCode" column="mbti_code"/>
        </collection>
    </resultMap>

    <resultMap type="cn.dutp.book.domain.MoocPsychologyHealthScaleFacet" id="MoocPsychologyHealthScaleFacetResult">
        <result property="facetId"    column="facet_id"    />
        <result property="scaleId"    column="scale_id"    />
        <result property="facetName"    column="facet_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sort"    column="facet_sort"    />
        <collection property="moocPsychologyHealthScaleQuestion" ofType="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestion">
            <result property="questionId" column="question_id"/>
            <result property="questionContent" column="question_content"/>
            <result property="sort" column="question_sort1"/>
            <result property="questionSort" column="question_sort"/>
            <result property="chooseOptionId" column="chooseOptionId"/>
            <collection property="moocPsychologyHealthScaleQuestionOption" ofType="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestionOption">
                <result property="optionId" column="option_id"/>
                <result property="optionContent" column="option_content"/>
                <result property="sort" column="option_sort"/>
                <result property="score" column="score"/>
            </collection>
        </collection>
    </resultMap>

    <select id="selectMoocPsychologyHealthScaleList" parameterType="cn.dutp.book.domain.MoocPsychologyHealthScale" resultType="cn.dutp.book.domain.MoocPsychologyHealthScale">
        select
            mp.scale_id,
            mp.scale_name,
            mp.scan_question,
            mp.evaluation_method,
            mp.evaluate_reference,
            mp.scale_type,
            mp.question_sort,
            mp.show_sort_type,
            mp.status,
            mp.create_time,
            GROUP_CONCAT(distinct mb.book_name ORDER BY mb.book_name SEPARATOR ', ')  as bookName,
            (
                case when (
                    select count(0) from mooc_psychology_health_chapter hc
                    left join dtb_book tk on tk.book_id = hc.book_id and tk.del_flag = 0
                    and tk.publish_status = 2 where hc.del_flag = 0 and hc.scale_id = mp.scale_id) > 0 then 0 else 1 end
            ) as isDelFlag,
            (select count(user_id) from mooc_psychology_health_user_result where scale_id = mp.scale_id and del_flag = 0) as testCount
        from
            mooc_psychology_health_scale mp
        left join
            mooc_psychology_health_chapter mphc on mp.scale_id = mphc.scale_id and mphc.del_flag = 0
        left join
            dtb_book mb on mphc.book_id = mb.book_id and mb.del_flag = 0
        where
            mp.del_flag = 0 and mp.user_id = #{userId}
            <if test="scaleName != null and scaleName != ''">
                and mp.scale_name like concat('%', #{scaleName}, '%')
            </if>
            <if test="status != null and status != ''">
                and mp.status = #{status}
            </if>
            <if test="scaleId != null">
                and mp.scale_id = #{scaleId}
            </if>
        group by
            mp.scale_id,mp.scale_name,mp.create_time
        order by
            mp.create_time desc
    </select>

    <select id="listForEditor" parameterType="cn.dutp.book.domain.MoocPsychologyHealthScale" resultType="cn.dutp.book.domain.MoocPsychologyHealthScale">
        select
            mp.scale_id,
            mp.scale_name,
            mp.create_time,
            mp.scale_type,
            mp.question_sort,
            mp.show_sort_type,
            (select count(0) from mooc_psychology_health_scale_question where del_flag = 0 and scale_id = mp.scale_id and del_flag = 0) as questionCount
        from
            mooc_psychology_health_scale mp
        where
            mp.del_flag = 0 and mp.user_id = #{userId} and mp.status = 1
            <if test="scaleName!= null and scaleName!= ''">
                and mp.scale_name like concat('%', #{scaleName}, '%')
            </if>
    </select>

    <select id="getPsychologyHealthById" resultMap="MoocPsychologyHealthScaleResult">
        select
            mq.question_id,
            mq.question_content,
            mq.sort as question_sort,
            mqo.option_id,
            mqo.option_content,
            mqo.sort as option_sort,
            mqo.score,
            mqo.mbti_code,
            mqo.jump_question_id
        from
            mooc_psychology_health_scale_question mq
        left join
            mooc_psychology_health_scale_question_option mqo on mq.question_id = mqo.question_id and mqo.del_flag = 0
        where
            mq.del_flag = 0 and mq.scale_id = #{scaleId}
        order by mq.sort,mqo.sort
    </select>

    <select id="selectPsychologyHeathScale" resultType="cn.dutp.book.domain.MoocPsychologyHealthScale">
        select
            mp.scale_id,
            mp.scale_name,
            mp.create_time,
            GROUP_CONCAT(distinct mb.name ORDER BY mb.name SEPARATOR ', ')  as bookName,
            (select count(user_id) from mooc_psychology_health_user_result where scale_id = mp.scale_id and del_flag = 0) as testCount
        from
            mooc_psychology_health_scale mp
        left join
            mooc_psychology_health_chapter mphc on mp.scale_id = mphc.scale_id and mphc.del_flag = 0
        left join
            dtb_book mb on mphc.book_id = mb.book_id and mb.del_flag = 0
        where
            mp.del_flag = 0 and mp.scale_id = #{scaleId}
    </select>

    <select id="getByResultId" resultType="cn.dutp.book.domain.MoocPsychologyHealthScale">
        select
            ur.user_id,
            ur.create_time,
            ur.result_id,
            ur.score,
            hc.scale_id,
            hc.scale_name,
            hc.question_sort,
            hc.show_sort_type,
            hc.evaluation_method,
            hc.evaluate_reference,
            du.real_name,
            du.nick_name,
            du.user_name
        from
            mooc_psychology_health_user_result ur
        left join
            mooc_psychology_health_scale hc on hc.scale_id = ur.scale_id and hc.del_flag = 0
        left join
            dutp_user du on ur.user_id = du.user_id and du.del_flag = 0
        where
            ur.del_flag = 0 and ur.result_id = #{resultId}
    </select>
    <select id="selectScaleNameByUserId" resultType="java.lang.String">
        SELECT scale_name FROM mooc_psychology_health_scale where user_id = #{userId}
    </select>

    <select id="getMbtiDataByScaleId" resultType="java.util.Map">
        select instructions_name,instructions_remark from mooc_psychology_health_instructions where scale_id = #{scaleId}
    </select>

</mapper>