package cn.dutp.book.controller;

import cn.dutp.book.domain.vo.BookChapterCheckParam;
import cn.dutp.book.service.FounderssService;
import cn.dutp.common.core.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 方正审校接口
 */
@Slf4j
@RestController
@RequestMapping("/founderss")
public class FounderssController {


    @Autowired
    private FounderssService founderssService;

    /**
     * 文本审核接口
     */
    @PostMapping("/textCheck")
    public AjaxResult textCheck(@RequestBody BookChapterCheckParam chapterCheckParam) {
        return AjaxResult.success(founderssService.checkText(chapterCheckParam));
    }

    /**
     * 图片审核接口
     */
    @PostMapping("/imgCheck")
    public AjaxResult imgCheck(@RequestBody BookChapterCheckParam chapterCheckParam) {
        return AjaxResult.success(founderssService.checkImg(chapterCheckParam));
    }

    /**
     * 获取图片审核结果
     */
    @GetMapping("/imgCheckResult")
    public AjaxResult imgCheckResult(@RequestParam("taskId") String taskId) {
        return AjaxResult.success(founderssService.getImgCheckResult(taskId));
    }

    /**
     * 音视频接口
     */
    @PostMapping("/videoCheck")
    public AjaxResult videoCheck(@RequestBody BookChapterCheckParam chapterCheckParam) {
        return AjaxResult.success(founderssService.videoCheck(chapterCheckParam));
    }

    /**
     * 获取图片审核结果
     */
    @GetMapping("/videoCheckResult")
    public AjaxResult videoCheckResult(@RequestParam("taskId") String taskId, @RequestParam("type") Integer type) {
        return AjaxResult.success(founderssService.getVideoCheckResult(taskId, type));
    }

    /**
     * 图片回调
     *
     * @param responseStr
     * @return
     */
    @PostMapping("/imgCallback")
    public AjaxResult imgCallback(@RequestBody String responseStr) {
        founderssService.imgCallback(responseStr);
        return AjaxResult.success();
    }

    /**
     * 视频回调
     *
     * @param responseStr
     * @return
     */
    @PostMapping("/videoCallback")
    public AjaxResult videoCallback(@RequestBody String responseStr) {
        founderssService.videoCallback(responseStr);
        return AjaxResult.success();
    }

    /**
     * 音频回调
     *
     * @param responseStr
     * @return
     */
    @PostMapping("/audioCallback")
    public AjaxResult audioCallback(@RequestBody String responseStr) {
        founderssService.audioCallback(responseStr);
        return AjaxResult.success();
    }
}
