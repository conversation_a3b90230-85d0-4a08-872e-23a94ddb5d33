package cn.dutp.book.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.domain.DtbUserBookConfig;
import cn.dutp.book.mapper.DtbUserBookConfigMapper;
import cn.dutp.book.mapper.DtbUserBookMapper;
import cn.dutp.book.service.IDutpAiVoiceService;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookReadMapper;
import cn.dutp.book.domain.DtbUserBookRead;
import cn.dutp.book.service.IDtbUserBookReadService;
import cn.hutool.core.date.DateUtil;

/**
 * 用户阅读记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DtbUserBookReadServiceImpl extends ServiceImpl<DtbUserBookReadMapper, DtbUserBookRead> implements IDtbUserBookReadService
{
    @Autowired
    private DtbUserBookReadMapper dtbUserBookReadMapper;
    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;
    @Autowired
    private DtbUserBookConfigMapper dtbUserBookConfigMapper;

    @Autowired
    IDutpAiVoiceService dutpAiVoiceService;

    @Autowired
    private RedisService redisService;
    /**
     * 查询用户阅读记录
     *
     * @param readId 用户阅读记录主键
     * @return 用户阅读记录
     */
    @Override
    public DtbUserBookRead selectDtbUserBookReadByReadId(Long readId)
    {
        return this.getById(readId);
    }

    /**
     * 查询用户阅读记录列表
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 用户阅读记录
     */
    @Override
    public List<DtbUserBookRead> selectDtbUserBookReadList(DtbUserBookRead dtbUserBookRead)
    {
        LambdaQueryWrapper<DtbUserBookRead> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getUserId
                ,dtbUserBookRead.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getBookId
                ,dtbUserBookRead.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getChapterId())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getChapterId
                ,dtbUserBookRead.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getPageNumber())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getPageNumber
                ,dtbUserBookRead.getPageNumber());
            }
            if(DutpConstant.STR_ZERO.equals(dtbUserBookRead.getWeekFlag())){
                Date beginOfWeek = DateUtil.beginOfWeek(new Date());
                lambdaQueryWrapper.ge(DtbUserBookRead::getCreateTime, beginOfWeek);
            }

        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户阅读记录
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 结果
     */
    @Override
    public AjaxResult insertDtbUserBookRead(DtbUserBookRead dtbUserBookRead)
    {
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isNotEmpty(userId)) {
            // 修改配置表
            LambdaQueryWrapper<DtbUserBookConfig> updateWrapper = new LambdaQueryWrapper<>();
            updateWrapper.eq(DtbUserBookConfig::getBookId, dtbUserBookRead.getBookId())
                    .eq(DtbUserBookConfig::getUserId, userId);
            DtbUserBookConfig updateEntity = new DtbUserBookConfig();
            updateEntity.setReadRate(dtbUserBookRead.getReadRate());
            updateEntity.setLastChapterId(dtbUserBookRead.getChapterId());
            updateEntity.setLastPageNumber(dtbUserBookRead.getPageNumber());
            updateEntity.setLastSeeDate(new Date());
            dtbUserBookConfigMapper.update(updateEntity, updateWrapper);

            // 保存阅读记录
            dtbUserBookRead.setUserId(SecurityUtils.getUserId());
            this.save(dtbUserBookRead);
            // 修改阅读最后时间
            LambdaQueryWrapper<DtbUserBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DtbUserBook::getUserId,userId).eq(DtbUserBook::getBookId,dtbUserBookRead.getBookId());
            DtbUserBook dtbUserBook = dtbUserBookMapper.selectOne(lambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(dtbUserBook)) {
                DtbUserBook param = new DtbUserBook();
                param.setUserBookId(dtbUserBook.getUserBookId());
                param.setLastSeeDate(new Date());
                param.setReadRate(dtbUserBookRead.getReadRate());
                dtbUserBookMapper.updateById(param);
            }
        }
        return AjaxResult.success("保存成功",dtbUserBookRead.getReadId().toString());
    }

    /**
     * 修改用户阅读记录
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookRead(DtbUserBookRead dtbUserBookRead)
    {
        return this.updateById(dtbUserBookRead);
    }

    /**
     * 批量删除用户阅读记录
     *
     * @param readIds 需要删除的用户阅读记录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookReadByReadIds(List<Long> readIds)
    {
        return this.removeByIds(readIds);
    }

    @Override
    public AjaxResult updateUserReadTime(Long readId, Integer readTime) {
        dtbUserBookReadMapper.updateReadTime(readId, readTime);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult getReadRole() {

        return null;
    }

    /**
     * 获取token存入redis 并返回给前端
     */
    @Override
    public AjaxResult getRedisToken(DtbUserBookRead dtbUserBookRead) {
        // 生成随机 token
        String token = UUID.randomUUID().toString().replace("-", "");
        String redisKey = "reader:token:" + token;

        // 存储映射关系 (包含 bookId 和 chapterId)
        String value = dtbUserBookRead.getBookId() + ":" + dtbUserBookRead.getChapterId();
        redisService.setCacheObject(redisKey, value, 24L, TimeUnit.HOURS);
        // 返回给前端
        return AjaxResult.success("成功",token);
    }

    @Override
    public AjaxResult getBookByRedisToken(String redisToken) {
        String redisKey = "reader:token:" + redisToken;
        String value = redisService.getCacheObject(redisKey);
        if (value == null) {
            return AjaxResult.error("链接已过期或无效");
        }
        String[] parts = value.split(":");
        String bookId = parts[0];
        String chapterId = parts[1];
        // 返回数据
        Map<String, String> result = new HashMap<>();
        result.put("bookId", bookId);
        result.put("chapterId", chapterId);
        return AjaxResult.success(result);
    }
}
