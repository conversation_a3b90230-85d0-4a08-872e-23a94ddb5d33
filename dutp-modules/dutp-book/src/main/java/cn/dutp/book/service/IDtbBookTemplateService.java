package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookTemplate;
import cn.dutp.book.domain.vo.DtbBookTemplateVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * DUTP-DTB-029教材模板Service接口
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
public interface IDtbBookTemplateService extends IService<DtbBookTemplate> {
    /**
     * 查询DUTP-DTB-029教材模板
     *
     * @param templateId DUTP-DTB-029教材模板主键
     * @return DUTP-DTB-029教材模板
     */
    public DtbBookTemplate selectDtbBookTemplateByTemplateId(Long templateId);

    public DtbBookTemplateVO selectDtbBookTemplateByChapterId(Long chapterId);

    /**
     * 查询DUTP-DTB-029教材模板列表
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return DUTP-DTB-029教材模板集合
     */
    public List<DtbBookTemplate> selectDtbBookTemplateList(DtbBookTemplate dtbBookTemplate);

    /**
     * 新增DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    public boolean insertDtbBookTemplate(DtbBookTemplate dtbBookTemplate);

    /**
     * 修改DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    public boolean updateDtbBookTemplate(DtbBookTemplate dtbBookTemplate);

    /**
     * 批量删除DUTP-DTB-029教材模板
     *
     * @param templateId 需要删除的DUTP-DTB-029教材模板主键集合
     * @return 结果
     */
    public boolean deleteDtbBookTemplateByTemplateId(Long templateId);


    List<DtbBookTemplate> templateListByBookId(DtbBookTemplate bookTemplate);

    int updateBookTemplate(DtbBookTemplate dtbBookTemplate);

    int editDefault(DtbBookTemplate dtbBookTemplate);

    boolean editIsEnable(DtbBookTemplate dtbBookTemplate);
}
