package cn.dutp.book.service;


import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.DtbBookChapterContent;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 数字教材章节目录Service接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
public interface IDtbBookChapterContentService extends IService<DtbBookChapter> {

    /**
     * 查询章节内容
     * <p>
     * chapterId     * @return 章节内容
     */
    public DtbBookChapterContent getChapterContentInfo(Long chapterId);

    /**
     * 修改数字教材章节目录
     *
     * @param dtbBookChapterContent 数字教材章节目录
     * @return 结果
     */
    public boolean updateChapterContentInfo(DtbBookChapterContent dtbBookChapterContent);

    AjaxResult getChapterContentByChapterId(Long chapterId);

    AjaxResult getChapterContentSimpleByChapterId(Long chapterId,Integer contentVersion);

    AjaxResult getQueryChapterContent(ReaderCommonForm readerCommonForm);

    Map<String, Object> getAllQueryChapterContent(ReaderCommonForm readerCommonForm);

    Boolean checkIsHasContent(Long chapterId);

    Boolean clearChapterContent(Long chapterId);

    int changeChapterContentInfo(DtbBookChapterContent dtbBookChapterContent);

    List<DtbBookChapterContent> queryCurUserSaveVersionHistory(Long chapterId);

    int generateOriginalDraft();
}
