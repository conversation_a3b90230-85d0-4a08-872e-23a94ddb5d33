package cn.dutp.book.controller;

import cn.dutp.book.domain.form.DtbBookFormula;
import cn.dutp.common.core.domain.UploadFileDto;
import cn.dutp.common.core.utils.AliyunOssStsUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.batik.transcoder.TranscoderException;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * DUTP-DTB_002数字教材Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Slf4j
@RestController
@RequestMapping("/formula")
public class DtbBookEditorFormulaController extends BaseController {
    @Autowired
    private AliyunOssStsUtil aliyunOssStsUtil;

    @Value("${formula.latexFormulaUrl}")
    private String latexFormulaUrl;

    @Value("${formula.wirisFormulaUrl}")
    private String wirisFormulaUrl;

    /**
     * 公式请求
     */
    @Log(title = "公式请求", businessType = BusinessType.INSERT)
    @PostMapping("/getFormulaImg")
    public AjaxResult getFormulaImg(@RequestBody DtbBookFormula bookFormula) throws UnsupportedEncodingException {
        if (bookFormula.getLangData() == null) {
            log.error("公式转图片没有传递language Data 数据");
            return error("公式内容没有找到");
        }
        if ("latex".equals(bookFormula.getProviderName())) {
            // 发送请求获取数据流
            String url = latexFormulaUrl + "?" + URLDecoder.decode(bookFormula.getLangData(), "UTF-8");
            String svgStr = HttpRequest.get(url).execute().body();
            try {
                // UploadFileDto uploadFileDto = aliyunOssStsUtil.uploadFile(convertToPngStream(svgStr));
                UploadFileDto uploadFileDto = aliyunOssStsUtil.uploadFile(svgStr.getBytes(StandardCharsets.UTF_8), ".svg");
                return AjaxResult.success("获取公式图片成功", uploadFileDto);
            } catch (Exception e) {
                log.error("上传文件失败", e);
                return error("获取公式图片失败");
            }
        } else {
            // 发送请求获取数据流
            String url = wirisFormulaUrl + bookFormula.getLangData();
            try (InputStream inputStream = HttpRequest.get(url).execute().bodyStream()) {
                UploadFileDto uploadFileDto = aliyunOssStsUtil.uploadFile(inputStream, ".svg");
                return AjaxResult.success("获取公式图片成功", uploadFileDto);
            } catch (Exception e) {
                log.error("上传文件失败", e);
                return error("获取公式图片失败");
            }
        }


    }

    /**
     * 将SVG字符串转换为PNG字节流
     *
     * @param svgContent SVG字符串内容
     * @return PNG图片的字节数组
     */
    public byte[] convertToPngStream(String svgContent) throws Exception {
        try (ByteArrayInputStream svgInputStream = new ByteArrayInputStream(svgContent.getBytes()); ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream()) {

            // 配置转换器
            PNGTranscoder transcoder = new PNGTranscoder();
            transcoder.addTranscodingHint(PNGTranscoder.KEY_FORCE_TRANSPARENT_WHITE, true);
            transcoder.addTranscodingHint(PNGTranscoder.KEY_WIDTH, 800f);
            transcoder.addTranscodingHint(PNGTranscoder.KEY_HEIGHT, 100f);

            // 执行转换
            TranscoderInput input = new TranscoderInput(svgInputStream);
            TranscoderOutput output = new TranscoderOutput(pngOutputStream);
            transcoder.transcode(input, output);

            // 直接返回内存中的PNG字节流
            return pngOutputStream.toByteArray();
        } catch (TranscoderException e) {
            log.error("SVG转PNG失败", e);
            throw new RuntimeException(e);
        }
    }
}
