package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.book.domain.MoocPsychologyHealthTestAnswer;
import cn.dutp.book.mapper.MoocPsychologyHealthTestAnswerMapper;
import cn.dutp.book.service.IMoocPsychologyHealthTestAnswerService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户心理测试答题明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class MoocPsychologyHealthTestAnswerServiceImpl extends ServiceImpl<MoocPsychologyHealthTestAnswerMapper, MoocPsychologyHealthTestAnswer> implements IMoocPsychologyHealthTestAnswerService
{


}
