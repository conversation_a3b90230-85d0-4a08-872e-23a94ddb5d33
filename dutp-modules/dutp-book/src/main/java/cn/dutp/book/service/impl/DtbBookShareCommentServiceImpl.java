package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookShareComment;
import cn.dutp.book.domain.DtbBookShareCommentAttachment;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.DtbBookShareCommentAttachmentVO;
import cn.dutp.book.domain.vo.DtbBookShareCommentVo;
import cn.dutp.book.mapper.DtbBookBookMapper;
import cn.dutp.book.mapper.DtbBookShareCommentAttachmentMapper;
import cn.dutp.book.mapper.DtbBookShareCommentMapper;
import cn.dutp.book.service.IDtbBookShareCommentService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.dutp.common.core.utils.JsonToDocxConverter.insertImage;

/**
 * 教材分享点评Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Slf4j
@Service
public class DtbBookShareCommentServiceImpl extends ServiceImpl<DtbBookShareCommentMapper, DtbBookShareComment> implements IDtbBookShareCommentService
{
    @Autowired
    private DtbBookShareCommentMapper dtbBookShareCommentMapper;

    @Autowired
    private DtbBookShareCommentAttachmentMapper attachmentMapper;

    @Autowired
    private DtbBookBookMapper bookMapper;


    @Override
    public AjaxResult selectDtbBookShareCommentList(ReaderCommonForm readerForm) {
        if (readerForm.getNoteType().intValue() != 1) {
            return AjaxResult.error("功能建设中");
        } else {
            List<DtbBookShareCommentVo> noteVos = dtbBookShareCommentMapper.selectBookShareCommentList(readerForm);
            noteVos.forEach(item -> {
                List<DtbBookShareCommentAttachmentVO> attachments = attachmentMapper.selectBookShareCommentAttachmentList(item.getCommentId());
                item.setAttachments(attachments);
            });
            return AjaxResult.success(noteVos);
        }
    }

    /**
     * 新增DUTP-DTB_022点评/标注
     *
     * @param dtbBookShareComment@return 结果
     */
    @Override
    public AjaxResult insertShareComment(DtbBookShareComment dtbBookShareComment) {
        this.save(dtbBookShareComment);
        List<DtbBookShareCommentAttachment> attachments = dtbBookShareComment.getAttachments();
        if (ObjectUtil.isNotEmpty(attachments)) {
            attachments.forEach(item -> {
                item.setCommentId(dtbBookShareComment.getCommentId());
                attachmentMapper.insert(item);
            });
        }
        return AjaxResult.success();
    }

    /**
     * 修改DUTP-DTB_022点评/标注
     *
     * @param dtbBookShareComment@return 结果
     */
    @Override
    public AjaxResult updateShareComment(DtbBookShareComment dtbBookShareComment) {
        LambdaQueryWrapper<DtbBookShareCommentAttachment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbBookShareCommentAttachment::getCommentId, dtbBookShareComment.getCommentId());
        attachmentMapper.delete(lambdaQueryWrapper);
        List<DtbBookShareCommentAttachment> attachments = dtbBookShareComment.getAttachments();
        if (ObjectUtil.isNotEmpty(attachments)) {
            attachments.forEach(item -> {
                item.setCommentId(dtbBookShareComment.getCommentId());
                attachmentMapper.insert(item);
            });
        }
        this.updateById(dtbBookShareComment);
        return AjaxResult.success();
    }

    /**
     * 批量删除DUTP-DTB_022点评/标注
     *
     * @param commentIds@return 结果
     */
    @Override
    public boolean deleteShareCommentByCommentIds(List<Long> commentIds) {
        return this.removeByIds(commentIds);
    }

    @Override
    public void exportShareComment(HttpServletResponse response, DtbBookShareComment dtbBookShareComment) {
        if (dtbBookShareComment.getBookId() == null) {
            throw new ServiceException("bookId不能为空");
        }
        DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookName)
                .eq(DtbBook::getBookId, dtbBookShareComment.getBookId()));
        List<DtbBookShareComment> dtbBookShareComments = dtbBookShareCommentMapper.exportBookShare(dtbBookShareComment);

        XWPFDocument doc = new XWPFDocument();
        // 创建临时工作目录
        Path tempDir = null;
        try {
            tempDir = Files.createTempDirectory("zip-gen-");
        } catch (IOException e) {
            log.info("创建目录失败：", e);
        }
        Path audioDir = tempDir.resolve("评论音频");
        try {
            Files.createDirectories(audioDir);
        } catch (IOException e) {
            log.info("创建目录失败：", e);
        }
        Map<Long, List<DtbBookShareComment>> notesMap = dtbBookShareComments.stream().collect(Collectors.groupingBy(DtbBookShareComment::getChapterId));


        notesMap.forEach((k, v) -> {
            int idx = 0;
            for (DtbBookShareComment bookShareComment : v) {
                idx++;
                if (idx == 1) {
                    XWPFParagraph paragraph = doc.createParagraph();
                    XWPFRun run = paragraph.createRun();
                    run.setText(bookShareComment.getChapterName());
                    run.setBold(true);
                }
                XWPFParagraph paragraph = doc.createParagraph();
                XWPFRun run = paragraph.createRun();
                run.setText(idx + ". " + bookShareComment.getBookContent());
                XWPFParagraph paragraph1 = doc.createParagraph();
                XWPFRun run1 = paragraph1.createRun();
                run1.setText("          评论内容：" + bookShareComment.getCommentContent());

                List<DtbBookShareCommentAttachmentVO> attachments = attachmentMapper.selectBookShareCommentAttachmentList(bookShareComment.getCommentId());

                if (ObjectUtil.isEmpty(attachments)) {
                    XWPFParagraph paragraph8 = doc.createParagraph();
                    XWPFRun run2 = paragraph8.createRun();
                    run2.setText("          时间：" + DateUtil.format(bookShareComment.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    continue;
                }
                // 评论图片
                List<DtbBookShareCommentAttachmentVO> imgList = attachments.stream().filter(o -> o.getAttachmentType() == 1).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(imgList)) {
                    XWPFParagraph paragraph2 = doc.createParagraph();
                    XWPFRun run2 = paragraph2.createRun();
                    run2.setText("          评论图片：");
                    for (DtbBookShareCommentAttachmentVO attachmentVO : imgList) {
                        XWPFParagraph paragraph3 = doc.createParagraph();
                        insertImage(paragraph3, attachmentVO.getAttachmentUrl(), attachmentVO.getAttachmentName(), 20, 20);
                    }
                }
                XWPFParagraph paragraph8 = doc.createParagraph();
                XWPFRun run2 = paragraph8.createRun();
                run2.setText("          时间：" + DateUtil.format(bookShareComment.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                // 评论音频
                List<DtbBookShareCommentAttachmentVO> auditList = attachments.stream().filter(o -> o.getAttachmentType() == 2).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(auditList)) {
                    for (DtbBookShareCommentAttachmentVO attachmentVO : auditList) {
                        try {
                            downloadFile(attachmentVO.getAttachmentUrl(), audioDir);
                        } catch (IOException e) {
                            log.info("下载音频失败：", e);
                        }
                    }

                }
            }
        });

        String username = SecurityUtils.getLoginUser().getUsername();
        if (username.length() > 4) {
            username = username.substring(username.length() - 4);
        }

        response.setContentType("application/octet-stream;charset=utf-8");
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(username + "_" + book.getBookName() + ".zip", StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            log.info("编码失败：", e);
        }

        // 写入ZIP文件
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            // 添加音频文件夹
            addDirectoryToZip(zos, audioDir, "评论音频/");
            // 添加Word文档
            zos.putNextEntry(new ZipEntry("评论.docx"));
            doc.write(zos);
        } catch (IOException e) {
            log.info("导出评论失败，", e);
            throw new ServiceException("导出失败");
        } finally {
            try {
                doc.close();
            } catch (IOException e) {
                log.info("导出评论失败，", e);
            }
        }

    }

    /**
     * 添加文件到压缩包
     *
     * @param zos
     * @param dir
     * @param basePath
     * @throws IOException
     */
    private void addDirectoryToZip(ZipOutputStream zos, Path dir, String basePath) throws IOException {
        Files.walk(dir).forEach(path -> {
            if (!Files.isDirectory(path)) {
                String entryName = basePath + dir.relativize(path);
                try {
                    zos.putNextEntry(new ZipEntry(entryName));
                    Files.copy(path, zos);
                    zos.closeEntry();
                } catch (IOException e) {
                    throw new UncheckedIOException(e);
                }
            }
        });
    }

    /**
     * 通过url下载文件
     *
     * @param url
     * @param targetDir
     * @throws IOException
     */
    private void downloadFile(String url, Path targetDir) throws IOException {
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        Path outputPath = targetDir.resolve(fileName);

        try (InputStream in = HttpRequest.get(url).header("Referer", "https://ebook.dutp.cn").execute().bodyStream()) {
            Files.copy(in, outputPath, StandardCopyOption.REPLACE_EXISTING);
        }
    }
}
