package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 心理健康量人格说明对象 mooc_psychology_health_instructions
 *
 * <AUTHOR>
 * @date 2025-09-24
 */
@Data
@TableName("mooc_psychology_health_instructions")
public class MoocPsychologyHealthInstructions extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键 */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long instructionsId;

    /** 人格名称 */
        @Excel(name = "人格名称")
    private String instructionsName;

    /** 人格说明 */
        @Excel(name = "人格说明")
    private String instructionsRemark;

    /** 量表id */
        @Excel(name = "量表id")
    private Long scaleId;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("instructionsId", getInstructionsId())
            .append("instructionsName", getInstructionsName())
            .append("instructionsRemark", getInstructionsRemark())
            .append("scaleId", getScaleId())
        .toString();
        }
        }
