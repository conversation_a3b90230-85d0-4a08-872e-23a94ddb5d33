package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookQuestionAnswer;
import cn.dutp.book.service.IDtbBookQuestionAnswerService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 问题答案Controller
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RestController
@RequestMapping("/questionAnswer")
public class DtbBookQuestionAnswerController extends BaseController
{
    @Autowired
    private IDtbBookQuestionAnswerService dtbBookQuestionAnswerService;

    /**
     * 查询问题答案列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        startPage();
        List<DtbBookQuestionAnswer> list = dtbBookQuestionAnswerService.selectDtbBookQuestionAnswerList(dtbBookQuestionAnswer);
        return getDataTable(list);
    }

    /**
     * 导出问题答案列表
     */
    @RequiresPermissions("book:questionAnswer:export")
    @Log(title = "导出问题答案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        List<DtbBookQuestionAnswer> list = dtbBookQuestionAnswerService.selectDtbBookQuestionAnswerList(dtbBookQuestionAnswer);
        ExcelUtil<DtbBookQuestionAnswer> util = new ExcelUtil<DtbBookQuestionAnswer>(DtbBookQuestionAnswer.class);
        util.exportExcel(response, list, "问题答案数据");
    }

    /**
     * 获取问题答案详细信息
     */
    @RequiresPermissions("book:questionAnswer:query")
    @GetMapping(value = "/{answerId}")
    public AjaxResult getInfo(@PathVariable("answerId") Long answerId)
    {
        return success(dtbBookQuestionAnswerService.selectDtbBookQuestionAnswerByAnswerId(answerId));
    }

    /**
     * 新增问题答案
     */
    @Operation(
            summary = "新增问题答案",
            description = "阅读器答题结果保存",
            tags = "新增问题答案",
            parameters = {
                    @Parameter(name = "userQuestionId", description = "小题ID", required = true),
                    @Parameter(name = "bookQuestionId", description = "", required = false),
                    @Parameter(name = "chapterId", description = "章节ID", required = false),
                    @Parameter(name = "answerContent", description = "答题结果", required = false),
                    @Parameter(name = "score", description = "100表示完全正确0表示完全错误", required = false),
                    @Parameter(name = "bookId", description = "教材ID", required = false),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "成功"),
            }
    )
    @Log(title = "新增问题答案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        return toAjax(dtbBookQuestionAnswerService.insertDtbBookQuestionAnswer(dtbBookQuestionAnswer));
    }

    /**
     * 修改问题答案
     */
    @Log(title = "修改问题答案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        return toAjax(dtbBookQuestionAnswerService.updateDtbBookQuestionAnswer(dtbBookQuestionAnswer));
    }

    /**
     * 删除问题答案
     */
    @RequiresPermissions("book:questionAnswer:remove")
    @Log(title = "删除问题答案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{answerIds}")
    public AjaxResult remove(@PathVariable Long[] answerIds)
    {
        return toAjax(dtbBookQuestionAnswerService.deleteDtbBookQuestionAnswerByAnswerIds(Arrays.asList(answerIds)));
    }
}
