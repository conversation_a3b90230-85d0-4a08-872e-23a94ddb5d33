package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.book.domain.DtbUserQuestionOption;
import cn.dutp.book.mapper.DtbUserQuestionOptionMapper;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.service.IDtbUserQuestionOptionService;

/**
 * DUTP-DTB_010数字教材选择题选项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DtbUserQuestionOptionServiceImpl extends ServiceImpl<DtbUserQuestionOptionMapper, DtbUserQuestionOption> implements IDtbUserQuestionOptionService
{
    @Autowired
    private DtbUserQuestionOptionMapper dtbUserQuestionOptionMapper;

    /**
     * 查询DUTP-DTB_010数字教材选择题选项
     *
     * @param optionId DUTP-DTB_010数字教材选择题选项主键
     * @return DUTP-DTB_010数字教材选择题选项
     */
    @Override
    public DtbUserQuestionOption selectDtbBookQuestionOptionByOptionId(Long optionId)
    {
        return this.getById(optionId);
    }

    /**
     * 查询DUTP-DTB_010数字教材选择题选项列表
     *
     * @param dtbUserQuestionOption DUTP-DTB_010数字教材选择题选项
     * @return DUTP-DTB_010数字教材选择题选项
     */
    @Override
    public List<DtbUserQuestionOption> selectDtbBookQuestionOptionList(DtbUserQuestionOption dtbUserQuestionOption)
    {
        LambdaQueryWrapper<DtbUserQuestionOption> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserQuestionOption.getOptionContent())) {
                lambdaQueryWrapper.eq(DtbUserQuestionOption::getOptionContent
                , dtbUserQuestionOption.getOptionContent());
            }
                if(ObjectUtil.isNotEmpty(dtbUserQuestionOption.getQuestionId())) {
                lambdaQueryWrapper.eq(DtbUserQuestionOption::getQuestionId
                , dtbUserQuestionOption.getQuestionId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserQuestionOption.getRightFlag())) {
                lambdaQueryWrapper.eq(DtbUserQuestionOption::getRightFlag
                , dtbUserQuestionOption.getRightFlag());
            }
                if(ObjectUtil.isNotEmpty(dtbUserQuestionOption.getOptionPosition())) {
                lambdaQueryWrapper.eq(DtbUserQuestionOption::getOptionPosition
                , dtbUserQuestionOption.getOptionPosition());
            }
                if(ObjectUtil.isNotEmpty(dtbUserQuestionOption.getSort())) {
                lambdaQueryWrapper.eq(DtbUserQuestionOption::getSort
                , dtbUserQuestionOption.getSort());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_010数字教材选择题选项
     *
     * @param dtbUserQuestionOption DUTP-DTB_010数字教材选择题选项
     * @return 结果
     */
    @Override
    public boolean insertDtbBookQuestionOption(DtbUserQuestionOption dtbUserQuestionOption)
    {
        return this.save(dtbUserQuestionOption);
    }

    /**
     * 修改DUTP-DTB_010数字教材选择题选项
     *
     * @param dtbUserQuestionOption DUTP-DTB_010数字教材选择题选项
     * @return 结果
     */
    @Override
    public boolean updateDtbBookQuestionOption(DtbUserQuestionOption dtbUserQuestionOption)
    {
        return this.updateById(dtbUserQuestionOption);
    }

    /**
     * 批量删除DUTP-DTB_010数字教材选择题选项
     *
     * @param optionIds 需要删除的DUTP-DTB_010数字教材选择题选项主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookQuestionOptionByOptionIds(List<Long> optionIds)
    {
        return this.removeByIds(optionIds);
    }

}
