package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbTestPaper;
import cn.dutp.book.domain.DtbTestPaperQuestionCollection;

import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 试卷Service接口
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
public interface IDtbTestPaperService extends IService<DtbTestPaper>
{
    /**
     * 查询试卷
     *
     * @param paperId 试卷主键
     * @return 试卷
     */
    public DtbTestPaper selectDtbTestPaperByPaperId(Long paperId);

    /**
     * 查询试卷列表
     *
     * @param dtbTestPaper 试卷
     * @return 试卷集合
     */
    public List<DtbTestPaper> selectDtbTestPaperList(DtbTestPaper dtbTestPaper);

    /**
     * 新增试卷
     *
     * @param dtbTestPaper 试卷
     * @return 结果
     */
    public boolean insertDtbTestPaper(DtbTestPaper dtbTestPaper);

    /**
     * 修改试卷
     *
     * @param dtbTestPaper 试卷
     * @return 结果
     */
    public boolean updateDtbTestPaper(DtbTestPaper dtbTestPaper);

    /**
     * 批量删除试卷
     *
     * @param paperIds 需要删除的试卷主键集合
     * @return 结果
     */
    public boolean deleteDtbTestPaperByPaperIds(List<Long> paperIds);



    /**
     * 查询试卷的题目组和题目信息
     *
     * @param paperId 试卷ID
     * @return 试卷题目组和题目列表
     */
    public List<DtbTestPaperQuestionCollection> selectQuestionCollectionsByPaperId(Long paperId);

     /**
     * 将试卷移入回收站
     * @param paperIds 试卷ID列表
     * @return 结果
     */
    boolean moveToRecycleBin(List<Long> paperIds);

    /**
     * 从回收站恢复试卷
     * @param paperIds 试卷ID列表
     * @return 结果
     */
    boolean restoreFromRecycleBin(List<Long> paperIds);

    /**
     * 查询回收站试卷列表
     * @param dtbTestPaper 试卷
     * @return 回收站试卷列表
     */
    List<DtbTestPaper> selectRecycleBinList(DtbTestPaper dtbTestPaper);

    /**
     * 复制试卷
     * @param paperId 要复制的试卷ID
     * @return 复制后的试卷
     */
    DtbTestPaper copyTestPaper(Long paperId);

    public boolean checkBeforeEdit(Long paperId);

}

