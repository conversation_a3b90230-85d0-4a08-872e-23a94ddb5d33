package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookChapterCatalogMapper;
import cn.dutp.book.domain.DtbBookChapterCatalog;
import cn.dutp.book.service.IDtbBookChapterCatalogService;

/**
 * 教材章节目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Service
public class DtbBookChapterCatalogServiceImpl extends ServiceImpl<DtbBookChapterCatalogMapper, DtbBookChapterCatalog> implements IDtbBookChapterCatalogService
{
    @Autowired
    private DtbBookChapterCatalogMapper dtbBookChapterCatalogMapper;

    /**
     * 查询教材章节目录
     *
     * @param catalogId 教材章节目录主键
     * @return 教材章节目录
     */
    @Override
    public DtbBookChapterCatalog selectDtbBookChapterCatalogByCatalogId(Long catalogId)
    {
        return this.getById(catalogId);
    }

    /**
     * 查询教材章节目录列表
     *
     * @param dtbBookChapterCatalog 教材章节目录
     * @return 教材章节目录
     */
    @Override
    public List<DtbBookChapterCatalog> selectDtbBookChapterCatalogList(DtbBookChapterCatalog dtbBookChapterCatalog)
    {
        LambdaQueryWrapper<DtbBookChapterCatalog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookChapterCatalog.getTitle())) {
                lambdaQueryWrapper.eq(DtbBookChapterCatalog::getTitle
                ,dtbBookChapterCatalog.getTitle());
            }
                if(ObjectUtil.isNotEmpty(dtbBookChapterCatalog.getChapterId())) {
                lambdaQueryWrapper.eq(DtbBookChapterCatalog::getChapterId
                ,dtbBookChapterCatalog.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookChapterCatalog.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookChapterCatalog::getBookId
                ,dtbBookChapterCatalog.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookChapterCatalog.getParentId())) {
                lambdaQueryWrapper.eq(DtbBookChapterCatalog::getParentId
                ,dtbBookChapterCatalog.getParentId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教材章节目录
     *
     * @param dtbBookChapterCatalog 教材章节目录
     * @return 结果
     */
    @Override
    public boolean insertDtbBookChapterCatalog(DtbBookChapterCatalog dtbBookChapterCatalog)
    {
        return this.save(dtbBookChapterCatalog);
    }

    /**
     * 修改教材章节目录
     *
     * @param dtbBookChapterCatalog 教材章节目录
     * @return 结果
     */
    @Override
    public boolean updateDtbBookChapterCatalog(DtbBookChapterCatalog dtbBookChapterCatalog)
    {
        return this.updateById(dtbBookChapterCatalog);
    }

    /**
     * 批量删除教材章节目录
     *
     * @param catalogIds 需要删除的教材章节目录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookChapterCatalogByCatalogIds(List<Long> catalogIds)
    {
        return this.removeByIds(catalogIds);
    }

}
