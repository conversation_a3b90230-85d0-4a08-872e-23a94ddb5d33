package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbArVrResourceFolder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AR/VR资源库文件夹Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface DtbArVrResourceFolderMapper extends BaseMapper<DtbArVrResourceFolder>
{
    /**
     * 查询AR/VR资源库文件夹
     *
     * @param folderId AR/VR资源库文件夹主键
     * @return AR/VR资源库文件夹
     */
    public DtbArVrResourceFolder selectDtbArVrResourceFolderByFolderId(Long folderId);

    /**
     * 查询AR/VR资源库文件夹列表
     *
     * @param dtbArVrResourceFolder AR/VR资源库文件夹
     * @return AR/VR资源库文件夹集合
     */
    public List<DtbArVrResourceFolder> selectDtbArVrResourceFolderList(DtbArVrResourceFolder dtbArVrResourceFolder);

    /**
     * 新增AR/VR资源库文件夹
     *
     * @param dtbArVrResourceFolder AR/VR资源库文件夹
     * @return 结果
     */
    public int insertDtbArVrResourceFolder(DtbArVrResourceFolder dtbArVrResourceFolder);

    /**
     * 修改AR/VR资源库文件夹
     *
     * @param dtbArVrResourceFolder AR/VR资源库文件夹
     * @return 结果
     */
    public int updateDtbArVrResourceFolder(DtbArVrResourceFolder dtbArVrResourceFolder);

    /**
     * 删除AR/VR资源库文件夹
     *
     * @param folderId AR/VR资源库文件夹主键
     * @return 结果
     */
    public int deleteDtbArVrResourceFolderByFolderId(Long folderId);

    /**
     * 批量删除AR/VR资源库文件夹
     *
     * @param folderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDtbArVrResourceFolderByFolderIds(Long[] folderIds);
} 