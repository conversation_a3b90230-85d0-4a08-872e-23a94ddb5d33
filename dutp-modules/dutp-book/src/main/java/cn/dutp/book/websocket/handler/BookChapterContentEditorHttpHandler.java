package cn.dutp.book.websocket.handler;

import cn.dutp.book.websocket.BookChapterContentEditorWsSessionManager;
import cn.dutp.book.websocket.modal.BookChapterContentEditorMessage;
import cn.dutp.common.core.text.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 教材章节内容编辑器
 */
@Slf4j
@Component
public class BookChapterContentEditorHttpHandler extends TextWebSocketHandler {

    /**
     * socket 建立成功事件
     *
     * @param session
     * @throws Exception
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {

    }

    /**
     * 接收消息事件
     *
     * @param session
     * @param message
     * @throws Exception
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        Object sessionId = session.getAttributes().get("session_id");
        String token = (String) session.getAttributes().get("token");

        // 获得客户端传来的消息
        String bookChapterContentEditorMessageStr = message.getPayload();
        BookChapterContentEditorMessage bookChapterContentEditorMessage = JSONUtil.toBean(bookChapterContentEditorMessageStr, BookChapterContentEditorMessage.class);
        log.debug("接收到消息, session_id:{}, token:{}, message: {}", sessionId, token, bookChapterContentEditorMessage);
        if (ObjectUtil.isEmpty(bookChapterContentEditorMessage)) {
            log.info("接收到消息为空, session_id:{}", sessionId);
            return;
        }

        if (bookChapterContentEditorMessage.getOptCode() == 0) {
            // 心跳
            log.debug("心跳消息, session_id:{}", sessionId);
            if (bookChapterContentEditorMessage.getIsEditing()) {
                String chapterId = bookChapterContentEditorMessage.getChapterId();
                WebSocketSession chapterEditor = BookChapterContentEditorWsSessionManager.getChapterEditor(chapterId);
                if (ObjectUtil.isNotEmpty(chapterEditor)) {
                    String curToken = (String) session.getAttributes().get("token");
                    String occupyToken = (String) chapterEditor.getAttributes().get("token");
                    if (!curToken.equals(occupyToken)) {
                        BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                                .resultMsg("当前章节正在被其他人编辑")
                                .resultCode(2)
                                .build();
                        session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
                        return;
                    }
                }

            }
            BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                    .resultMsg("心跳反应，当前时间：" + LocalDateTime.now())
                    .resultCode(0)
                    .build();
            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));


        } else if (bookChapterContentEditorMessage.getOptCode() == 1) {
            // 申请编写章节内容
            String chapterId = bookChapterContentEditorMessage.getChapterId();

            WebSocketSession chapterEditor = BookChapterContentEditorWsSessionManager.getChapterEditor(chapterId);
            if (ObjectUtil.isNotEmpty(chapterEditor)) {
                String curToken = (String) session.getAttributes().get("token");
                String occupyToken = (String) chapterEditor.getAttributes().get("token");
                if (curToken.equals(occupyToken)) {
                    // 编辑章节session和此次申请是同一个人，直接替换当前session
                    // 进入编辑器
                    BookChapterContentEditorWsSessionManager.addChapterEditor(chapterId, session);
                    BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                            .resultCode(3)
                            .resultMsg("同意申请章节编辑")
                            .build();
                    session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
                    log.info("当前章节编辑器已存在，但申请编辑用户是当前编辑用户，直接替换当前session, chapterId: {}, session_id: {}", chapterId, sessionId);
                } else if ("system".equals(occupyToken)) {
                    // 正在更新题注 通知当前申请人
                    BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                            .resultCode(5)
                            .resultMsg("当前教材正在处于题注更新中，无法进入编辑器")
                            .build();
                    try {
                        session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
                    } catch (IllegalStateException e) {
                        log.info("当前链接已关闭，sessionId: {}", session.getAttributes().get("session_id"));
                    }
                } else {
                    // 当前章节有人正在编辑，通知当前申请编辑用户
                    BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                            .resultCode(1)
                            .resultMsg("当前章节正在被编辑中，请强制进入")
                            .build();
                    try {
                        session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
                    } catch (IllegalStateException e) {
                        log.info("当前链接已关闭，sessionId: {}", session.getAttributes().get("session_id"));
                    }
                }
                return;
            }
            // 当前章节没有人正在编辑
            BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                    .resultCode(3)
                    .resultMsg("同意申请章节编辑")
                    .build();
            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
            BookChapterContentEditorWsSessionManager.addChapterEditor(chapterId, session);
            log.info("同意申请章节编辑，chapterId:{}", chapterId);
        } else if (bookChapterContentEditorMessage.getOptCode() == 2) {
            // 强行申请编写章节内容
            String chapterId = bookChapterContentEditorMessage.getChapterId();

            WebSocketSession chapterEditor = BookChapterContentEditorWsSessionManager.getChapterEditor(chapterId);
            if (ObjectUtil.isNotEmpty(chapterEditor)) {
                log.info("通知当前正在编写的编辑");
                // 当前章节有人正在编辑，通知当前申请编辑用户
                BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                        .resultCode(2)
                        .resultMsg("当前章节正在被其他人编辑")
                        .build();
                try {
                    chapterEditor.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
                } catch (IllegalStateException e) {
                    log.info("当前链接已关闭，sessionId: {}", chapterEditor.getAttributes().get("session_id"));
                    // 当前编辑人session失效，已不在编辑，所以直接进入编辑器，不进行保存操作
                    responseMessage = BookChapterContentEditorMessage.builder()
                            .resultCode(6)
                            .resultMsg("同意申请章节编辑")
                            .build();
                    session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
                }
                // 删除当前链接
                BookChapterContentEditorWsSessionManager.removeChapterEditor(chapterId);
                BookChapterContentEditorWsSessionManager.addChapterEditor(chapterId, session);
            } else {
                // 同意申请章节编辑
                BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                        .resultCode(6)
                        .resultMsg("同意申请章节编辑")
                        .build();
                session.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
            }
        } else if (bookChapterContentEditorMessage.getOptCode() == 3) {
            List<Long> chapterIds = bookChapterContentEditorMessage.getChapterIds();
            if (ObjectUtil.isNotEmpty(chapterIds)) {
                for (Long chapterId : chapterIds) {
                    String chapterIdStr = Convert.toStr(chapterId);
                    WebSocketSession chapterEditor = BookChapterContentEditorWsSessionManager.getChapterEditor(chapterIdStr);
                    if (ObjectUtil.isNotEmpty(chapterEditor)) {
                        // 踢出去
                        log.info("通知当前正在编写的编辑");
                        // 删除当前链接
                        BookChapterContentEditorWsSessionManager.removeChapterEditor(chapterIdStr);
                    } else {
                        // 进入编辑器
                        String curToken = (String) session.getAttributes().get("token");
                        BookChapterContentEditorWsSessionManager.addChapterEditor(chapterIdStr, session);
                    }

                }
            }
        } else if (bookChapterContentEditorMessage.getOptCode() == 4) {
            List<Long> chapterIds = bookChapterContentEditorMessage.getChapterIds();
            if (ObjectUtil.isNotEmpty(chapterIds)) {
                for (Long chapterId : chapterIds) {
                    String chapterIdStr = Convert.toStr(chapterId);
                    WebSocketSession chapterEditor = BookChapterContentEditorWsSessionManager.getChapterEditor(chapterIdStr);
                    // 删除当前链接
                    BookChapterContentEditorWsSessionManager.removeChapterEditor(chapterIdStr);

                }
            }
        } else if (bookChapterContentEditorMessage.getOptCode() == 5) {
            log.info("当前章节数据保存完毕，允许进入编辑器");
            String chapterId = bookChapterContentEditorMessage.getChapterId();
            WebSocketSession chapterEditor = BookChapterContentEditorWsSessionManager.getChapterEditor(chapterId);
            if (ObjectUtil.isNotEmpty(chapterEditor)) {
                // 当前章节有人正在编辑，通知当前申请编辑用户
                BookChapterContentEditorMessage responseMessage = BookChapterContentEditorMessage.builder()
                        .resultCode(6)
                        .resultMsg("当前章节数据保存完成，允许进入编辑器")
                        .build();
                try {
                    chapterEditor.sendMessage(new TextMessage(JSONUtil.toJsonStr(responseMessage)));
                } catch (IllegalStateException e) {
                    log.info("当前链接已关闭，sessionId: {}", chapterEditor.getAttributes().get("session_id"));
                }
                BookChapterContentEditorWsSessionManager.removeChapterEditor(chapterId);
            }
        }

    }

    /**
     * socket 断开连接时
     *
     * @param session
     * @param status
     * @throws Exception
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        Object sessionId = session.getAttributes().get("session_id");
        log.info("用户：{}，断开链接", sessionId);
        if (sessionId != null) {
            // 退出编辑状态
            BookChapterContentEditorWsSessionManager.removeBySessionId(sessionId.toString());
        }
    }
}