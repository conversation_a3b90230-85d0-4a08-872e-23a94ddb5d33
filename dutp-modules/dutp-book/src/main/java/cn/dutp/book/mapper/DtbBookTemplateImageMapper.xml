<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookTemplateImageMapper">

    <select id="listForAuthor" resultType="cn.dutp.book.domain.DtbBookTemplateImage">
        SELECT
            i.image_id,
            i.url,
            i.belong_to
        FROM
            dtb_book_template_image i
        <if test="type == 1 and fromTo == 2">
            inner join dtb_book_template t on i.template_id = t.template_id
                    and i.type = 1 and i.template_id != #{templateId} and t.theme_color = #{themeColor}
        </if>

        <if test="type == 1 and fromTo == 1">
        WHERE
            i.type = 1
            AND i.template_id = #{templateId}
        </if>
        <if test="type == 2">
        WHERE
            i.type = 2
            AND i.user_id = #{userId}
        </if>
    </select>
</mapper>