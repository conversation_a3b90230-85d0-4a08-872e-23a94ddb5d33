package cn.dutp.book.service.impl;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookResourceLogMapper;
import cn.dutp.book.domain.DtbUserBookResourceLog;
import cn.dutp.book.service.IDtbUserBookResourceLogService;

/**
 * DUTP-DTB_017学生观看数字教材资源记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DtbUserBookResourceLogServiceImpl extends ServiceImpl<DtbUserBookResourceLogMapper, DtbUserBookResourceLog> implements IDtbUserBookResourceLogService
{
    @Autowired
    private DtbUserBookResourceLogMapper dtbUserBookResourceLogMapper;

    /**
     * 查询DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param logId DUTP-DTB_017学生观看数字教材资源记录主键
     * @return DUTP-DTB_017学生观看数字教材资源记录
     */
    @Override
    public DtbUserBookResourceLog selectDtbUserBookResourceLogByLogId(Long logId)
    {
        return this.getById(logId);
    }

    /**
     * 查询DUTP-DTB_017学生观看数字教材资源记录列表
     *
     * @param dtbUserBookResourceLog DUTP-DTB_017学生观看数字教材资源记录
     * @return DUTP-DTB_017学生观看数字教材资源记录
     */
    @Override
    public List<DtbUserBookResourceLog> selectDtbUserBookResourceLogList(DtbUserBookResourceLog dtbUserBookResourceLog)
    {
        LambdaQueryWrapper<DtbUserBookResourceLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookResourceLog.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserBookResourceLog::getUserId
                ,dtbUserBookResourceLog.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookResourceLog.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserBookResourceLog::getBookId
                ,dtbUserBookResourceLog.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookResourceLog.getResourceId())) {
                lambdaQueryWrapper.eq(DtbUserBookResourceLog::getResourceId
                ,dtbUserBookResourceLog.getResourceId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookResourceLog.getFileUrl())) {
                lambdaQueryWrapper.eq(DtbUserBookResourceLog::getFileUrl
                ,dtbUserBookResourceLog.getFileUrl());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookResourceLog.getLastTime())) {
                lambdaQueryWrapper.eq(DtbUserBookResourceLog::getLastTime
                ,dtbUserBookResourceLog.getLastTime());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookResourceLog.getChapterId())) {
                lambdaQueryWrapper.eq(DtbUserBookResourceLog::getChapterId
                ,dtbUserBookResourceLog.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookResourceLog.getProgressRate())) {
                lambdaQueryWrapper.eq(DtbUserBookResourceLog::getProgressRate
                ,dtbUserBookResourceLog.getProgressRate());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param dtbUserBookResourceLog DUTP-DTB_017学生观看数字教材资源记录
     * @return 结果
     */
    @Override
    public boolean insertDtbUserBookResourceLog(DtbUserBookResourceLog dtbUserBookResourceLog) {
        Long bookId = dtbUserBookResourceLog.getBookId();
        Long userId = dtbUserBookResourceLog.getUserId();
        Long chapterId = dtbUserBookResourceLog.getChapterId();
        LambdaQueryWrapper<DtbUserBookResourceLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbUserBookResourceLog::getBookId, bookId)
                .eq(DtbUserBookResourceLog::getUserId, userId)
                .eq(DtbUserBookResourceLog::getChapterId, chapterId);
        DtbUserBookResourceLog oldDtbUserBookResourceLog = this.getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(oldDtbUserBookResourceLog)) {
            // 视频
            if (oldDtbUserBookResourceLog.getFileType() == 3){
                Long existingLastTimeMinute = oldDtbUserBookResourceLog.getLastTime();
                Long existingLastTimeSecond = existingLastTimeMinute * 60; // 转成秒
                // 前台传入的时间
                Long newLastTimeSecond = dtbUserBookResourceLog.getLastTime();
                // 累加
                Long totalTimeSecond = existingLastTimeSecond + newLastTimeSecond;
                // 转为分钟
                Long totalTimeMinute = newLastTimeSecond / 60;

                // 更新记录
                oldDtbUserBookResourceLog.setLastTime(totalTimeMinute);
            }
            return this.updateById(oldDtbUserBookResourceLog);
        }else {

            // 视频
            if (dtbUserBookResourceLog.getFileType() == 3){
                // 转为分钟
                Long totalTimeMinute = dtbUserBookResourceLog.getLastTime() / 60;
                dtbUserBookResourceLog.setLastTime(totalTimeMinute);
            }
            // 新增
            return this.save(dtbUserBookResourceLog);
        }

    }

    /**
     * 修改DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param dtbUserBookResourceLog DUTP-DTB_017学生观看数字教材资源记录
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookResourceLog(DtbUserBookResourceLog dtbUserBookResourceLog)
    {
        return this.updateById(dtbUserBookResourceLog);
    }

    /**
     * 批量删除DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param logIds 需要删除的DUTP-DTB_017学生观看数字教材资源记录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookResourceLogByLogIds(List<Long> logIds)
    {
        return this.removeByIds(logIds);
    }

}
