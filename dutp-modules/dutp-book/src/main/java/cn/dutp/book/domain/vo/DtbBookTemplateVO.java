package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class DtbBookTemplateVO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private String modal;

    private String imgUrl;
    private String headerUrl;
    private String contentUrl;
    private String footerUrl;
    private String chapterHeaderUrl;
    private String chapterFontColor;
    private String chapterHeaderHeight;
    private String jointHeaderUrl;
    private String jointFontColor;
    private String jointHeight;
    private String orderTemplateColor;
    private String orderTemplateBgUrl;
    private String orderTemplateMarginLeft;

    /**
     * 主题 light dark
     */
    private String theme;

    private String pagesFontColor;
    private String pagesAlign;
    private String pagesPosition;
}
