package cn.dutp.book.service.impl;

import cn.dutp.book.domain.MoocPsychologyHealthInstructions;
import cn.dutp.book.mapper.MoocPsychologyHealthInstructionsMapper;
import cn.dutp.book.service.IMoocPsychologyHealthInstructionsService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 心理健康量人格说明Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-24
 */
@Service
public class MoocPsychologyHealthInstructionsServiceImpl extends ServiceImpl<MoocPsychologyHealthInstructionsMapper, MoocPsychologyHealthInstructions> implements IMoocPsychologyHealthInstructionsService
{
    @Autowired
    private MoocPsychologyHealthInstructionsMapper moocPsychologyHealthInstructionsMapper;

    /**
     * 查询心理健康量人格说明
     *
     * @param instructionsId 心理健康量人格说明主键
     * @return 心理健康量人格说明
     */
    @Override
    public MoocPsychologyHealthInstructions selectMoocPsychologyHealthInstructionsByInstructionsId(Long instructionsId)
    {
        return this.getById(instructionsId);
    }

    /**
     * 查询心理健康量人格说明列表
     *
     * @param moocPsychologyHealthInstructions 心理健康量人格说明
     * @return 心理健康量人格说明
     */
    @Override
    public List<MoocPsychologyHealthInstructions> selectMoocPsychologyHealthInstructionsList(MoocPsychologyHealthInstructions moocPsychologyHealthInstructions)
    {
        LambdaQueryWrapper<MoocPsychologyHealthInstructions> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocPsychologyHealthInstructions.getInstructionsName())) {
                lambdaQueryWrapper.like(MoocPsychologyHealthInstructions::getInstructionsName
                ,moocPsychologyHealthInstructions.getInstructionsName());
            }
                if(ObjectUtil.isNotEmpty(moocPsychologyHealthInstructions.getInstructionsRemark())) {
                lambdaQueryWrapper.eq(MoocPsychologyHealthInstructions::getInstructionsRemark
                ,moocPsychologyHealthInstructions.getInstructionsRemark());
            }
                if(ObjectUtil.isNotEmpty(moocPsychologyHealthInstructions.getScaleId())) {
                lambdaQueryWrapper.eq(MoocPsychologyHealthInstructions::getScaleId
                ,moocPsychologyHealthInstructions.getScaleId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增心理健康量人格说明
     *
     * @param moocPsychologyHealthInstructions 心理健康量人格说明
     * @return 结果
     */
    @Override
    public boolean insertMoocPsychologyHealthInstructions(MoocPsychologyHealthInstructions moocPsychologyHealthInstructions)
    {
        return this.save(moocPsychologyHealthInstructions);
    }

    /**
     * 修改心理健康量人格说明
     *
     * @param moocPsychologyHealthInstructions 心理健康量人格说明
     * @return 结果
     */
    @Override
    public boolean updateMoocPsychologyHealthInstructions(MoocPsychologyHealthInstructions moocPsychologyHealthInstructions)
    {
        return this.updateById(moocPsychologyHealthInstructions);
    }

    /**
     * 批量删除心理健康量人格说明
     *
     * @param instructionsIds 需要删除的心理健康量人格说明主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocPsychologyHealthInstructionsByInstructionsIds(List<Long> instructionsIds)
    {
        return this.removeByIds(instructionsIds);
    }

}
