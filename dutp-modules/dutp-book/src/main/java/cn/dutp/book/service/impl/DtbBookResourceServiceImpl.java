package cn.dutp.book.service.impl;

import cn.dutp.book.domain.*;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.DtbBookResourceTreeVO;
import cn.dutp.book.domain.vo.DtbBookResourceVO;
import cn.dutp.book.domain.vo.DtbUserQuestionVO;
import cn.dutp.book.domain.vo.ResourceVO;
import cn.dutp.book.mapper.*;
import cn.dutp.book.service.IDtbBookResourceService;
import cn.dutp.book.service.IDtbUserBookService;
import cn.dutp.book.service.IDtbUserResourceService;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教材资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Slf4j
@Service
public class DtbBookResourceServiceImpl extends ServiceImpl<DtbBookResourceMapper, DtbBookResource> implements IDtbBookResourceService {
    @Autowired
    private DtbBookResourceMapper dtbBookResourceMapper;

    @Autowired
    private IDtbUserResourceService resourceService;

    @Autowired
    private DtbBookBookMapper bookMapper;

    @Autowired
    private DtbUserResourceFolderMapper userResourceFolderMapper;

    @Autowired
    private DtbBookResourceFolderMapper resourceFolderMapper;

    @Autowired
    private IDtbUserBookService dtbUserBookService;

    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;

    /**
     * 查询教材资源
     *
     * @param bookResourceId 教材资源主键
     * @return 教材资源
     */
    @Override
    public DtbBookResource selectDtbBookResourceByBookResourceId(Long bookResourceId) {
        return this.getById(bookResourceId);
    }

    /**
     * 查询教材资源列表
     *
     * @param dtbBookResource 教材资源
     * @return 教材资源
     */
    @Override
    public List<DtbBookResource> selectDtbBookResourceList(DtbBookResource dtbBookResource) {
        LambdaQueryWrapper<DtbBookResource> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbBookResource.getBookId())) {
            lambdaQueryWrapper.eq(DtbBookResource::getBookId
                    , dtbBookResource.getBookId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookResource.getResourceId())) {
            lambdaQueryWrapper.eq(DtbBookResource::getResourceId
                    , dtbBookResource.getResourceId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookResource.getOwnerId())) {
            lambdaQueryWrapper.eq(DtbBookResource::getOwnerId
                    , dtbBookResource.getOwnerId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookResource.getFolderId())) {
            lambdaQueryWrapper.eq(DtbBookResource::getFolderId
                    , dtbBookResource.getFolderId());
        }
        if (ObjectUtil.isNotEmpty(dtbBookResource.getInUse())) {
            lambdaQueryWrapper.eq(DtbBookResource::getInUse
                    , dtbBookResource.getInUse());
        }
        List<DtbBookResource> bookResources = this.list(lambdaQueryWrapper);
        // 获取所有资源ID
        List<Long> resourceIds = bookResources.stream()
                .map(DtbBookResource::getResourceId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(resourceIds)) {
            return bookResources;
        }

        // 批量查询用户资源
        List<DtbUserResource> userResources = resourceService.listByIds(resourceIds);
        Map<Long, DtbUserResource> resourceMap = userResources.stream()
                .collect(Collectors.toMap(DtbUserResource::getResourceId, resource -> resource));

        // 设置用户资源
        bookResources.forEach(bookResource ->
                bookResource.setUserResource(resourceMap.get(bookResource.getResourceId())));
        return bookResources;
    }

    /**
     * 新增教材资源
     *
     * @param dtbBookResource 教材资源
     * @return 结果
     */
    @Override
    public boolean insertDtbBookResource(DtbBookResource dtbBookResource) {
        return this.save(dtbBookResource);
    }

    /**
     * 修改教材资源
     *
     * @param dtbBookResource 教材资源
     * @return 结果
     */
    @Override
    public boolean updateDtbBookResource(DtbBookResource dtbBookResource) {
        return this.updateById(dtbBookResource);
    }

    /**
     * 批量删除教材资源
     *
     * @param bookResourceIds 需要删除的教材资源主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookResourceByBookResourceIds(List<Long> bookResourceIds) {
        return this.removeByIds(bookResourceIds);
    }

    /**
     * 阅读器获取资源
     *
     * @param readerCommonForm
     * @return
     */
    @Override
    public List<DtbBookResourceVO> getReaderBookResource(ReaderCommonForm readerCommonForm) {
        List<DtbBookResourceVO> vos = dtbBookResourceMapper.getReaderBookResource(readerCommonForm);
        return vos;
    }

    @Override
    public AjaxResult getReaderBookResourceByChapter(ReaderCommonForm readerCommonForm) {
        // 查询书籍是否已购买
        Long bookId = readerCommonForm.getBookId();
        Long userId = SecurityUtils.getUserId();
        DtbBook dtbBook = bookMapper.selectById(bookId);

        LambdaQueryWrapper<DtbUserBook> dtbUserBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 如果是副教材 查询关联的主教材是否购买
        if (dtbBook.getMasterFlag() == 3) {
            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId, dtbBook.getMasterBookId())
                    .eq(DtbUserBook::getUserId, userId)
                    .gt(DtbUserBook::getExpireDate, new Date());
        } else {
            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId, bookId)
                    .eq(DtbUserBook::getUserId, userId)
                    .gt(DtbUserBook::getExpireDate, new Date());
        }
        DtbUserBook dtbUserBook = dtbUserBookMapper.selectOne(dtbUserBookLambdaQueryWrapper);
        Long versionId;
        // 没买
        if (ObjectUtil.isNotEmpty(userId) && userId.longValue() != 0l && ObjectUtil.isNotNull(dtbUserBook)) {
            if (dtbBook.getMasterFlag() == 3) {
                versionId = dtbBook.getCurrentVersionId();
            } else {
                versionId = dtbUserBook.getVersionId();
            }
            readerCommonForm.setIsFree(0);
        } else {
            versionId = dtbBook.getCurrentVersionId();
            readerCommonForm.setIsFree(2);
        }
        readerCommonForm.setVersionId(versionId);

        List<DtbBookChapterResource> resourceList = dtbBookResourceMapper.getReaderBookResourceByChapter(readerCommonForm);
        // 按 chapterId 分组
        Map<Long, List<DtbBookChapterResource>> grouped = resourceList.stream()
                .collect(Collectors.groupingBy(DtbBookChapterResource::getChapterId));

        // 转换为 VO 列表
        List<DtbBookResourceTreeVO> voList = grouped.entrySet().stream()
                .map(entry -> {
                    Long chapterId = entry.getKey();
                    List<DtbBookChapterResource> groupDtos = entry.getValue();
                    DtbBookResourceTreeVO vo = new DtbBookResourceTreeVO();
                    vo.setChapterId(chapterId);
                    vo.setChapterName(groupDtos.get(0).getChapterName()); // 同一章节名称相同

                    // 转换为 DtbBookTestPaper 列表
                    List<DtbBookChapterResource> resources = groupDtos.stream()
                            .map(dto -> {
                                DtbBookChapterResource resource = new DtbBookChapterResource();
                                // 复制字段，例如使用 BeanUtils 或手动设置
                                BeanUtils.copyProperties(dto, resource);
                                return resource;
                            })
                            .collect(Collectors.toList());

                    vo.setBookResourcesList(resources);
                    return vo;
                })
                .collect(Collectors.toList());

        return AjaxResult.success(voList);
    }

    /**
     * 阅读器获取小题
     *
     * @param readerCommonForm
     * @return
     */
    @Override
    public List<DtbUserQuestionVO> getReaderBookQuestion(ReaderCommonForm readerCommonForm) {
        List<DtbUserQuestionVO> vos = dtbBookResourceMapper.getReaderBookQuestion(readerCommonForm);
        return vos;
    }

    @Override
    public boolean uploadResource(ResourceVO resourceVO) {
        Long chapterId = resourceVO.getChapterId();
        Long resourceId = resourceVO.getResourceId();
        Long userId = resourceVO.getUserId();
        if (ObjectUtil.isEmpty(userId)) {
            userId = SecurityUtils.getUserId();
        }
        if (ObjectUtil.isEmpty(chapterId)) {
            log.info("保存资源失败，没有chapterId, userId: {}", userId);
            return true;
        }
        DtbBook book = bookMapper.queryBookByChapterId(chapterId);
        if (ObjectUtil.isEmpty(book)) {
            log.info("保存资源失败，教材不存在,chapterId:{}, userId: {}", chapterId, userId);
            return true;
        }
        Long bookId = book.getBookId();
        // 判断文件类型
        int fileType = getFileType(resourceVO.getFileName());
        if (ObjectUtil.isEmpty(resourceId)) {
            Long userFolderId = resourceVO.getFolderId();
            if (ObjectUtil.isEmpty(userFolderId)) {
                // 查找默认文件夹ID
                DtbUserResourceFolder userResourceFolder = userResourceFolderMapper.selectOne(new LambdaQueryWrapper<DtbUserResourceFolder>()
                        .select(DtbUserResourceFolder::getUserFolderId)
                        .eq(DtbUserResourceFolder::getDefaultType, "1")
                        .eq(DtbUserResourceFolder::getUserId, userId)
                        .last("limit 1"));
                if (ObjectUtil.isEmpty(userResourceFolder)) {
                    // 不存在则新建
                    userResourceFolder = new DtbUserResourceFolder();
                    userResourceFolder.setFolderName("默认文件夹");
                    userResourceFolder.setParentId(0L);
                    userResourceFolder.setUserId(userId);
                    userResourceFolder.setDefaultType(String.valueOf(1));
                    userResourceFolderMapper.insert(userResourceFolder);
                }
                userFolderId = userResourceFolder.getUserFolderId();
            }

            // 保存资源到资源库
            DtbUserResource dtbUserResource = new DtbUserResource();
            dtbUserResource.setUserId(userId);
            dtbUserResource.setFileName(resourceVO.getFileName());
            dtbUserResource.setFileUrl(resourceVO.getFileUrl());
            dtbUserResource.setFileType(String.valueOf(fileType));
            dtbUserResource.setFileSize(resourceVO.getFileSize());
            dtbUserResource.setHash(resourceVO.getHash());
            dtbUserResource.setFolderId(userFolderId);
            resourceService.save(dtbUserResource);
            resourceId = dtbUserResource.getResourceId();
        }

        Long folderId = resourceVO.getFolderId();
        // 保存到教材资源文件夹
        // if (ObjectUtil.isEmpty(folderId)) {
        // 查找对于文件夹类型ID
        DtbBookResourceFolder bookResourceFolder = resourceFolderMapper.selectOne(new LambdaQueryWrapper<DtbBookResourceFolder>()
                .select(DtbBookResourceFolder::getFolderId)
                .eq(DtbBookResourceFolder::getDefaultType, String.valueOf(fileType))
                .eq(DtbBookResourceFolder::getBookId, bookId));
        folderId = bookResourceFolder.getFolderId();
        // }

        DtbBookResource dtbBookResource = new DtbBookResource();
        dtbBookResource.setBookId(bookId);
        dtbBookResource.setResourceId(resourceId);
        dtbBookResource.setOwnerId(userId);
        dtbBookResource.setFolderId(folderId);
        dtbBookResource.setInUse(1);
        dtbBookResourceMapper.insert(dtbBookResource);
        return true;
    }

    @Override
    public boolean batchUploadResource(List<ResourceVO> resourceVOList, Long userId, Long chapterId) {
        DtbBook book = bookMapper.queryBookByChapterId(chapterId);
        if (ObjectUtil.isEmpty(book)) {
            log.info("保存资源失败，教材不存在,chapterId:{}, userId: {}", chapterId, userId);
            return true;
        }
        Long bookId = book.getBookId();
        for (ResourceVO resourceVO : resourceVOList) {
            // 判断文件类型
            String fileType = resourceVO.getFileType();
            if (ObjectUtil.isEmpty(fileType)) {
                fileType = String.valueOf(getFileType(resourceVO.getFileName()));
            }
            // 查找默认文件夹ID
            DtbUserResourceFolder userResourceFolder = userResourceFolderMapper.selectOne(new LambdaQueryWrapper<DtbUserResourceFolder>()
                    .select(DtbUserResourceFolder::getUserFolderId)
                    .eq(DtbUserResourceFolder::getDefaultType, "1")
                    .eq(DtbUserResourceFolder::getUserId, userId)
                    .last("limit 1"));
            if (ObjectUtil.isEmpty(userResourceFolder)) {
                // 不存在则新建
                userResourceFolder = new DtbUserResourceFolder();
                userResourceFolder.setFolderName("默认文件夹");
                userResourceFolder.setParentId(0L);
                userResourceFolder.setUserId(userId);
                userResourceFolder.setDefaultType(String.valueOf(1));
                userResourceFolderMapper.insert(userResourceFolder);
            }
            // 保存资源到资源库
            DtbUserResource dtbUserResource = new DtbUserResource();
            dtbUserResource.setUserId(userId);
            dtbUserResource.setFileName(resourceVO.getFileName());
            dtbUserResource.setFileUrl(resourceVO.getFileUrl());
            dtbUserResource.setFileType(fileType);
            dtbUserResource.setFileSize(resourceVO.getFileSize());
            dtbUserResource.setFolderId(userResourceFolder.getUserFolderId());
            resourceService.save(dtbUserResource);

            // 保存到教材资源文件夹
            // 查找对于文件夹类型ID
            DtbBookResourceFolder bookResourceFolder = resourceFolderMapper.selectOne(new LambdaQueryWrapper<DtbBookResourceFolder>()
                    .select(DtbBookResourceFolder::getFolderId)
                    .eq(DtbBookResourceFolder::getDefaultType, String.valueOf(fileType))
                    .eq(DtbBookResourceFolder::getBookId, bookId));
            DtbBookResource dtbBookResource = new DtbBookResource();
            dtbBookResource.setBookId(bookId);
            dtbBookResource.setResourceId(dtbUserResource.getResourceId());
            dtbBookResource.setOwnerId(userId);
            dtbBookResource.setFolderId(bookResourceFolder.getFolderId());
            dtbBookResource.setInUse(1);
            dtbBookResourceMapper.insert(dtbBookResource);
        }

        return true;
    }

    /**
     * 根据文件名称判断文件类型
     *
     * @param fileName 文件名称
     * @return 文件类型代码（1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，8=课件）
     */
    public int getFileType(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            // 表示无法识别文件类型 默认图片
            return 1;
        }

        String extension = getFileExtension(fileName).toLowerCase();

        // 图片类型
        if (extension.matches("^(jpg|jpeg|png|gif|bmp|tiff)$")) {
            return 1;
        }
        // 音频类型
        else if (extension.matches("^(mp3|wav|ogg|flac|aac)$")) {
            return 2;
        }
        // 视频类型
        else if (extension.matches("^(mp4|avi|mov|mkv|flv|wmv)$")) {
            return 3;
        }
        // 虚拟仿真类型，这里假设特定的仿真文件扩展名，可根据实际情况调整
        else if (extension.matches("^(sim|vrs)$")) {
            return 4;
        }
        // AR/VR 类型，这里假设特定的 AR/VR 文件扩展名，可根据实际情况调整
        else if (extension.matches("^(vrx|ar3)$")) {
            return 5;
        }
        // 3D 模型类型
        else if (extension.matches("^(obj|fbx|3ds|dae|stl)$")) {
            return 6;
        }
        // 课件类型
        else if (extension.matches("^(ppt|pptx|pdf|doc|docx|xlsx)$")) {
            return 8;
        }

        // 表示无法识别文件类型 默认图片
        return 1;
    }

    /**
     * 获取文件的扩展名
     *
     * @param fileName 文件名称
     * @return 文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastIndex = fileName.lastIndexOf('.');
        if (lastIndex == -1) {
            return "";
        }
        return fileName.substring(lastIndex + 1);
    }
}
