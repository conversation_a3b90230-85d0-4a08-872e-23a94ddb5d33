<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookNoteMapper">
    
    <resultMap type="DtbUserBookNote" id="DtbUserBookNoteResult">
        <result property="noteId"    column="note_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="pageNumber"    column="page_number"    />
        <result property="nodeContent"    column="node_content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbUserBookNoteVo">
        select note_id, user_id, book_id, chapter_id, page_number, node_content, create_by, create_time, update_by, update_time, del_flag from dtb_user_book_note
    </sql>

    <select id="selectDtbUserBookNoteList" parameterType="DtbUserBookNote" resultMap="DtbUserBookNoteResult">
        <include refid="selectDtbUserBookNoteVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="pageNumber != null "> and page_number = #{pageNumber}</if>
            <if test="nodeContent != null  and nodeContent != ''"> and node_content = #{nodeContent}</if>
        </where>
    </select>
    
    <select id="selectDtbUserBookNoteByNoteId" parameterType="Long" resultMap="DtbUserBookNoteResult">
        <include refid="selectDtbUserBookNoteVo"/>
        where note_id = #{noteId}
    </select>

    <insert id="insertDtbUserBookNote" parameterType="DtbUserBookNote" useGeneratedKeys="true" keyProperty="noteId">
        insert into dtb_user_book_note
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="pageNumber != null">page_number,</if>
            <if test="nodeContent != null">node_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="pageNumber != null">#{pageNumber},</if>
            <if test="nodeContent != null">#{nodeContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBookNote" parameterType="DtbUserBookNote">
        update dtb_user_book_note
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="pageNumber != null">page_number = #{pageNumber},</if>
            <if test="nodeContent != null">node_content = #{nodeContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where note_id = #{noteId}
    </update>

    <delete id="deleteDtbUserBookNoteByNoteId" parameterType="Long">
        delete from dtb_user_book_note where note_id = #{noteId}
    </delete>

    <delete id="deleteDtbUserBookNoteByNoteIds" parameterType="String">
        delete from dtb_user_book_note where note_id in 
        <foreach item="noteId" collection="array" open="(" separator="," close=")">
            #{noteId}
        </foreach>
    </delete>
    <select id="selectReaderUserBookNoteList" resultType="cn.dutp.book.domain.vo.DtbUserBookNoteVo">
        SELECT
            note.note_id,
            note.chapter_id,
            note.page_number,
            note.node_content,
            note.from_word_id,
            note.end_word_id,
            note.create_time,
            note.share_flag,
            note.book_content,
            c.chapter_name
        FROM
            dtb_user_book_note note
        INNER JOIN dtb_book_chapter c ON note.chapter_id = c.chapter_id
        WHERE
            1 = 1
        AND note.del_flag = 0
        AND note.book_id = #{bookId}
        AND user_id = #{userId}
        <if test="chapterId !=null">
            AND note.chapter_id = #{chapterId}
        </if>
        <if test="sort == 1">
            ORDER BY
            note.page_number ASC
        </if>
        <if test="sort == 2">
            ORDER BY
            note.create_time DESC
        </if>
    </select>
    <select id="exportBookNote" resultType="cn.dutp.book.domain.DtbUserBookNote">
        SELECT
        n.note_id,
        n.chapter_id,
        n.node_content,
        n.create_time,
        n.book_content,
        c.chapter_name
        FROM
        dtb_user_book_note n
        INNER JOIN dtb_book_chapter c ON n.chapter_id = c.chapter_id
        WHERE
        n.del_flag = 0
        AND n.book_id = #{bookId}
        AND n.user_id = #{userId}
        <if test = "chapterId !=null" >
            AND n.chapter_id = #{chapterId}
        </if >
        order by c.sort
    </select>
</mapper>