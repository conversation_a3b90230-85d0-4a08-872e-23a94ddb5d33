<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.system.mapper.DutpUserDeviceMapper">

    <resultMap type="DutpUserDevice" id="DutpUserDeviceResult">
        <result property="deviceId" column="device_id"/>
        <result property="deviceType" column="device_type"/>
        <result property="deviceUniqueId" column="device_unique_id"/>
        <result property="deviceName" column="device_name"/>
        <result property="lastLoginDate" column="last_login_date"/>
        <result property="userId" column="user_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="DutpUserDeviceVo" id="DutpUserDeviceVoResult">
        <result property="deviceId" column="device_id"/>
        <result property="deviceType" column="device_type"/>
        <result property="deviceUniqueId" column="device_unique_id"/>
        <result property="deviceName" column="device_name"/>
        <result property="lastLoginDate" column="last_login_date"/>
        <result property="userId" column="user_id"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectDutpUserDeviceVo">
        select device_id,
               device_type,
               device_unique_id,
               device_name,
               last_login_date,
               user_id,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from dutp_user_device
    </sql>

    <select id="getDutpUserDeviceList" parameterType="DutpUserDevice" resultMap="DutpUserDeviceVoResult">
        <include refid="selectDutpUserDeviceVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="deviceUniqueId != null">and device_unique_id = #{deviceUniqueId}</if>
            <if test="deviceType != null ">and device_type = #{deviceType}</if>
            <if test="deviceName != null">and device_name like concat('%',)</if>
            <if test="delFlag != null">and del_flag = #{delFlag}</if>
        </where>
    </select>

    <select id="selectDutpUserDeviceList" parameterType="DutpUserDevice" resultMap="DutpUserDeviceResult">
        <include refid="selectDutpUserDeviceVo"/>
        <where>
            <if test="deviceType != null ">and device_type = #{deviceType}</if>
            <if test="deviceUniqueId != null  and deviceUniqueId != ''">and device_unique_id = #{deviceUniqueId}</if>
            <if test="deviceName != null  and deviceName != ''">and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="lastLoginDate != null ">and last_login_date = #{lastLoginDate}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectDutpUserDeviceByDeviceId" parameterType="Long" resultMap="DutpUserDeviceResult">
        <include refid="selectDutpUserDeviceVo"/>
        where device_id = #{deviceId}
    </select>

    <insert id="insertDutpUserDevice" parameterType="DutpUserDevice" useGeneratedKeys="true" keyProperty="deviceId">
        insert into dutp_user_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceType != null">device_type,</if>
            <if test="deviceUniqueId != null">device_unique_id,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="lastLoginDate != null">last_login_date,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceType != null">#{deviceType},</if>
            <if test="deviceUniqueId != null">#{deviceUniqueId},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="lastLoginDate != null">#{lastLoginDate},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDutpUserDevice" parameterType="DutpUserDevice">
        update dutp_user_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceUniqueId != null">device_unique_id = #{deviceUniqueId},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="lastLoginDate != null">last_login_date = #{lastLoginDate},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <delete id="deleteDutpUserDeviceByDeviceId" parameterType="Long">
        delete
        from dutp_user_device
        where device_id = #{deviceId}
    </delete>

    <delete id="deleteDutpUserDeviceByDeviceIds" parameterType="String">
        delete from dutp_user_device where device_id in
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>
</mapper>