package cn.dutp.system.mapper;

import java.util.List;

import cn.dutp.system.domain.vo.DutpUserDeviceVo;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
import cn.dutp.system.domain.DutpUserDevice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * DUTP-BASE-019用户设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Repository
public interface DutpUserDeviceMapper extends BaseMapper<DutpUserDevice> {

    @Update("update dutp_user_device set del_flag = #{delFlag} where user_id = #{userId} and device_unique_id = #{deviceUniqueId} ")
    boolean updateDutpUserDeviceDelFlag(DutpUserDevice dutpUserDevice);

    @Select("SELECT count(*) FROM dutp_user_device where user_id = #{userId} and device_unique_id = #{deviceUniqueId}")
    int getDutpUserDeviceNum(DutpUserDevice dutpUserDevice);


    List<DutpUserDeviceVo> getDutpUserDeviceList(DutpUserDevice dutpUserDevice);


}
