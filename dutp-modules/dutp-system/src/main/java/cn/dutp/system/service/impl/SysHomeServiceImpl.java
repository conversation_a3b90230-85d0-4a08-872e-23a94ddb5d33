package cn.dutp.system.service.impl;

import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.SysUser;
import cn.dutp.system.domain.SysUserShortcut;
import cn.dutp.system.domain.vo.*;
import cn.dutp.system.mapper.ISysUserShortcutMapper;
import cn.dutp.system.mapper.SysHomeMapper;
import cn.dutp.system.service.SysHomeService;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dtb_book(DUTP-DTB_002数字教材)】的数据库操作Service实现
* @createDate 2025-01-16 17:07:31
*/
@Service
public class SysHomeServiceImpl extends ServiceImpl<SysHomeMapper, BookManagementVo>
        implements SysHomeService {

    @Autowired
    private SysHomeMapper sysHomeMapper;

    @Autowired
    private ISysUserShortcutMapper sysUserShortcutMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 我审核的教材
     *
     * @return 审核数据
     */
    @Override
    public BookManagementMyReviewVo myReviewTextbook() {
        Long userId = SecurityUtils.getUserId();
        // 获取系统管理员
        R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == adminIdList.getCode())
        {
            throw new ServiceException(adminIdList.getMsg());
        }
        boolean isAdmin = false;
        if (!CollectionUtils.isEmpty(adminIdList.getData())) {
            // 查询userId是否包含在adminIdList.getuserId中 如果在的话证明是管理员 isAdmin = true
            if (adminIdList.getData().stream().anyMatch(admin -> admin.getUserId().equals(userId))) {
                isAdmin = true;
            }
        }
        List<ResVo> res = sysHomeMapper.myReviewTextbook(userId, isAdmin);
        BookManagementMyReviewVo bookManagementMyReviewVo = new BookManagementMyReviewVo();
        if (!res.isEmpty()) {
            // 我审核过得教材总数
            bookManagementMyReviewVo.setMyReviewBookNumber(res.size());
            // 公开教材数量
            bookManagementMyReviewVo.setOpenTextbooksNumber((int) res.stream().filter(item -> item.getBookOrganize() == 1).count());
            // 校本教材数量
            bookManagementMyReviewVo.setSchoolBasedTextbooksNumber((int) res.stream().filter(item -> item.getBookOrganize() == 2).count());
        }
        return bookManagementMyReviewVo;
    }

    /**
     * 查询处理过的教材数量
     *
     * @return 处理过的教材数量
     */
    @Override
    public List<ResVo> textbookProcessing() {
        return sysHomeMapper.textbookProcessing();
    }

    /**
     * 查询待处理的教材，以及对应分类数量
     *
     * @return 待处理的教材
     */
    @Override
    public BookManagementVo textbooksToBeDoneNumber() {
        return sysHomeMapper.textbooksToBeDoneNumber();
    }

    /**
     * 我审核的订单
     *
     * @param userId 用户id
     * @return 审核的订单数据
     */
    @Override
    public BookOrderManagementMyReviewVo myReviewBookOrder(Long userId) {
        return sysHomeMapper.myReviewBookOrder(userId);
    }

    /**
     * 查询订单的处理完成数和待办数
     *
     * @return 处理完成数和待办数
     */
    @Override
    public BookOrderManagementVo bookOrderManagement() {
        return sysHomeMapper.bookOrderManagement(SecurityUtils.getUserId());
    }

    /**
     * 查询订单支付待确认数据，教务采购和其他采购两个维度
     *
     * @return 订单支付待确认数据
     */
    @Override
    public BookOrderPaymentVo bookOrderPayment() {
        return sysHomeMapper.bookOrderPayment();
    }


    /**
     * 根据用户id，查询我审核的教务订单类型的 成功退款订单数，成功退款商品数，成功退款金额
     *
     * @param auditUserId 用户id
     * @return 成功退款订单数，成功退款商品数，成功退款金额
     */
    @Override
    public BookOrderRefundManagementMyReviewVo myReviewAcademicBookRefundOrder(Long auditUserId) {
        return sysHomeMapper.myReviewAcademicBookRefundOrder(auditUserId);
    }

    /**
     * 根据用户id查询教务订单类型我驳回的售后订单数量
     *
     * @param auditUserId 用户id
     * @return 驳回的售后订单数量
     */
    @Override
    public BookOrderRefundManagementMyReviewVo academicRefundRejectOrder(Long auditUserId) {
        return sysHomeMapper.academicRefundRejectOrder(auditUserId);
    }

    /**
     * 根据用户id，查询我审核的零售订单类型的 成功退款订单数，成功退款商品数，成功退款金额
     *
     * @param auditUserId 用户id
     * @return 成功退款订单数，成功退款商品数，成功退款金额
     */
    @Override
    public BookOrderRefundManagementMyReviewVo myReviewRetailBookRefundOrder(Long auditUserId) {
        return sysHomeMapper.myReviewRetailBookRefundOrder(auditUserId);
    }

    /**
     * 根据用户id查询零售订单类型我驳回的售后订单数量
     *
     * @param auditUserId 用户id
     * @return 驳回的售后订单数量
     */
    @Override
    public BookOrderRefundManagementMyReviewVo retailRefundRejectOrder(Long auditUserId) {
        return sysHomeMapper.retailRefundRejectOrder(auditUserId);
    }

    /**
     * 查询待处理的售后订单---待办数
     *
     * @return 待办数
     */
    @Override
    public BookOrderRefundManagementVo pendingRefundOrder() {
        return sysHomeMapper.pendingRefundOrder();
    }

    /**
     * 查询处理完成的售后订单---处理数
     *
     * @return 处理数
     */
    @Override
    public BookOrderRefundManagementVo processedCompletedRefundOrder() {
        return sysHomeMapper.processedCompletedRefundOrder(SecurityUtils.getUserId());
    }

    /**
     * 查询教务类型的订单的发票待上传数量和处理完成的数量
     *
     * @return 教务类型的订单的发票待上传数量和处理完成的数量
     */
    @Override
    public InvoiceApplyManagementVo selectAcademicInvoice() {
        return sysHomeMapper.selectAcademicInvoice(SecurityUtils.getUserId());
    }

    /**
     * 查询零售类型的订单的发票待上传数量和处理完成的数量
     *
     * @return 售类型的订单的发票待上传数量和处理完成的数量
     */
    @Override
    public InvoiceApplyManagementVo selectRetailInvoice() {
        return sysHomeMapper.selectRetailInvoice();
    }

    /**
     * 我审核的教务类型的成功上传发票和作废发票数量
     *
     * @param dealUserId 用户id
     * @return 上传发票和作废发票数量
     */
    @Override
    public InvoiceApplyManagementMyReviewVo selectmyReviewAcademicInvoice(Long dealUserId) {
        return sysHomeMapper.selectmyReviewAcademicInvoice(dealUserId);
    }

    /**
     * 我审核的零售类型的成功上传发票和作废发票数量
     *
     * @param dealUserId 用户id
     * @return 上传发票和作废发票数量
     */
    @Override
    public InvoiceApplyManagementMyReviewVo selectmyReviewRetailInvoice(Long dealUserId) {
        return sysHomeMapper.selectmyReviewRetailInvoice(dealUserId);
    }

    /**
     * 查询教师申请的试用教材待审核数量
     *
     * @return 教师申请的试用教材待审核数量
     */
    @Override
    public DtbUserTrialApplyManagementVo selectTrialApply() {
        return sysHomeMapper.selectTrialApply(SecurityUtils.getUserId());
    }

    /**
     * 根据用户id 查询我审核的教师申请的试用教材数量
     *
     * @param auditUserId 用户id
     * @return 我审核的教师申请的试用教材数量
     */
    @Override
    public DtbUserTrialApplyManagementVo selectMyReviewTrialApply(Long auditUserId) {
        return sysHomeMapper.selectMyReviewTrialApply(auditUserId);
    }

    /**
     * 根据用户id 查询我处理过的用户反馈数量
     *
     * @param processUserId 用户id
     * @return 我处理过的用户反馈数量
     */
    @Override
    public DutpUserWorkOrderManagementVo selectMyReviewUserWorkOrderNumber(Long processUserId) {
        return sysHomeMapper.selectMyReviewUserWorkOrderNumber(processUserId);
    }

    /**
     * 待处理的用户反馈数
     *
     * @return 用户反馈数
     */
    @Override
    public DutpUserWorkOrderManagementVo selectUserWorkOrderNumber() {
        return sysHomeMapper.selectUserWorkOrderNumber();
    }

    /**
     * 客服回复——会话待开启数量
     *
     * @return 话待开启数量
     */
    @Override
    public DutpCustomerChatManagementVo selectCustomerChatNumber() {
        return sysHomeMapper.selectCustomerChatNumber();
    }

    /**
     * 发行库存预警：dtb_book_purchase_code状态state<8的教材。
     *
     * @return 教材库存信息
     */
    @Override
    public List<DtbBookPurchaseCodeManagementVo> selectBookPurchaseCode() {
        return sysHomeMapper.selectBookPurchaseCode();
    }

    @Override
    public boolean saveShortcutMenu(List<Long> menuList) {
        sysUserShortcutMapper.deleteByUserId(SecurityUtils.getUserId());
        for (Long menuId : menuList) {
            SysUserShortcut userShortcut = new SysUserShortcut();
            userShortcut.setUserId(SecurityUtils.getUserId());
            userShortcut.setCreateTime(new Date());
            userShortcut.setMenuId(menuId);
            sysUserShortcutMapper.insert(userShortcut);
        }
        return true;
    }
}




