package cn.dutp.system.mapper;

import cn.dutp.system.domain.DataCenterForm;
import cn.dutp.system.domain.SysNotice;
import cn.dutp.system.domain.vo.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据中心MAPPER
 * 
 * <AUTHOR>
 */
@Repository
public interface DataCenterMapper
{
    BookDataVo getBookData(DataCenterForm dataCenterForm);

    List<BookStepDataVo> getBookStepData(DataCenterForm dataCenterForm);

    List<BookRankingVo> getBookRankingData(DataCenterForm dataCenterForm);

    OrderDataVo getOrderData(DataCenterForm dataCenterForm);

    List<StepDataVo> getSubjectBookData(DataCenterForm dataCenterForm);

    List<OrderDataVo> getOrderDateDataForChart(DataCenterForm dataCenterForm);

    List<OrderDataVo> getOrderRefundDataForChart(DataCenterForm dataCenterForm);

    List<OrderDataVo> getOrderMonthDataForChart(DataCenterForm dataCenterForm);

    List<OrderDataVo> getOrderMonthRefundDataForChart(DataCenterForm dataCenterForm);

    Integer getRefundTotalOrderQuantity(DataCenterForm dataCenterForm);
}