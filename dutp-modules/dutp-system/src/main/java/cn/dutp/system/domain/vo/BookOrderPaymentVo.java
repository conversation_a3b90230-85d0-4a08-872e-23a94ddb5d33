package cn.dutp.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookOrderPaymentVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 教务订单支付待确认
     */
    private Integer educationalPaymentOrderNumber;
    /**
     * 其他订单支付待确认
     */
    private Integer otherPaymentOrderNumber;


}