package cn.dutp.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 用户黑名单对象 dutp_user_black_list
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@TableName("dutp_user_black_list")
public class DutpUserBlackList extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**  */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long blackListId;

    /**  */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 1不当言论2违规图片3兑换码超过次数3次 */
    @Excel(name = "1不当言论2违规图片3兑换码超过次数3次")
    private Integer frozenType;

    /** 封停范围1发言发图2禁止登录3禁止兑换 */
    @Excel(name = "封停范围1发言发图2禁止登录3禁止兑换")
    private Integer frozenScope;

    /** 封停原因 */
    @Excel(name = "封停原因")
    private String frozenReason;

    /** 1生效2失效 */
    @Excel(name = "1生效2失效")
    private Integer state;

    /** 封停结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "封停结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("blackListId", getBlackListId())
                .append("userId", getUserId())
                .append("frozenType", getFrozenType())
                .append("frozenScope", getFrozenScope())
                .append("frozenReason", getFrozenReason())
                .append("state", getState())
                .append("endDate", getEndDate())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
