package cn.dutp.system.mapper;

import cn.dutp.system.domain.DutpUserBlackList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
/**
 * 用户黑名单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Repository
public interface DutpUserBlackListMapper extends BaseMapper<DutpUserBlackList>
{
    /**
     * 获取禁止兑换黑名单详细信息
     *
     * @param userId 用户ID
     * @return 用户黑名单
     */
    public DutpUserBlackList getProhibitionOfExchange(Long userId);
}
