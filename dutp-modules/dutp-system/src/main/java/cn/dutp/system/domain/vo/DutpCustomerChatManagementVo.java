package cn.dutp.system.domain.vo;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * DUTP-BASE-011客服对话窗口
 *
 * @TableName dutp_customer_chat
 */
@TableName(value = "dutp_customer_chat")
@Data
public class DutpCustomerChatManagementVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 客服回复——会话待开启数量
     */
    private Integer customerChatNumber;
}