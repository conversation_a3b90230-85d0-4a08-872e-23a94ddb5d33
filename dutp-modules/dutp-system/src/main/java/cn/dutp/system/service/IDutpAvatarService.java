package cn.dutp.system.service;

import java.util.List;
import cn.dutp.system.domain.DutpAvatar;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 头像Service接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IDutpAvatarService extends IService<DutpAvatar>
{
    /**
     * 查询头像
     *
     * @param avatarId 头像主键
     * @return 头像
     */
    public DutpAvatar selectDutpAvatarByAvatarId(Long avatarId);

    /**
     * 查询头像列表
     *
     * @param dutpAvatar 头像
     * @return 头像集合
     */
    public List<DutpAvatar> selectDutpAvatarList(DutpAvatar dutpAvatar);

    /**
     * 新增头像
     *
     * @param dutpAvatar 头像
     * @return 结果
     */
    public boolean insertDutpAvatar(DutpAvatar dutpAvatar);

    /**
     * 修改头像
     *
     * @param dutpAvatar 头像
     * @return 结果
     */
    public boolean updateDutpAvatar(DutpAvatar dutpAvatar);

    /**
     * 批量删除头像
     *
     * @param avatarIds 需要删除的头像主键集合
     * @return 结果
     */
    public boolean deleteDutpAvatarByAvatarIds(List<Long> avatarIds);

}
