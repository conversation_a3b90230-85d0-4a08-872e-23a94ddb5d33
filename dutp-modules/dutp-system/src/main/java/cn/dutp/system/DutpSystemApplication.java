package cn.dutp.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * 系统模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = {"cn.dutp.api", "cn.dutp.system"})
public class DutpSystemApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpSystemApplication.class, args);
        System.out.println("系统模块启动成功");
    }
}
