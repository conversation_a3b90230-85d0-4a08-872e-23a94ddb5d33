package cn.dutp.system.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.system.domain.DutpUserCommonFile;
import cn.dutp.system.mapper.DutpUserCommonFileMapper;
import cn.dutp.system.service.IDutpUserCommonFileService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class DutpUserCommonFileServiceImpl extends ServiceImpl<DutpUserCommonFileMapper, DutpUserCommonFile> implements IDutpUserCommonFileService
{
    @Autowired
    private DutpUserCommonFileMapper dutpUserCommonFileMapper;


    /**
     * 查询【请填写功能名称】
     *
     * @param fileId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public DutpUserCommonFile selectDutpUserCommonFileByFileId(Long fileId)
    {
        return this.getById(fileId);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dutpUserCommonFile 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<DutpUserCommonFile> selectDutpUserCommonFileList(DutpUserCommonFile dutpUserCommonFile)
    {
        LambdaQueryWrapper<DutpUserCommonFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileName())) {
            lambdaQueryWrapper.like(DutpUserCommonFile::getFileName
                    ,dutpUserCommonFile.getFileName());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileUrl())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getFileUrl
                    ,dutpUserCommonFile.getFileUrl());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileType())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getFileType
                    ,dutpUserCommonFile.getFileType());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileSize())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getFileSize
                    ,dutpUserCommonFile.getFileSize());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getBusinessId())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getBusinessId
                    ,dutpUserCommonFile.getBusinessId());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param dutpUserCommonFiles 【请填写功能名称】
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDutpUserCommonFile(List<DutpUserCommonFile> dutpUserCommonFiles)
    {
        DutpUserCommonFile dutpUserCommonFile = new DutpUserCommonFile();
        dutpUserCommonFile.setBusinessId(dutpUserCommonFiles.get(0).getBusinessId());
        deleteDutpUserCommonFile(dutpUserCommonFile);
        return this.saveBatch(dutpUserCommonFiles);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param dutpUserCommonFile 【请填写功能名称】
     * @return 结果
     */
    @Override
    public boolean updateDutpUserCommonFile(DutpUserCommonFile dutpUserCommonFile)
    {
        return this.updateById(dutpUserCommonFile);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param fileIds 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpUserCommonFileByFileIds(List<Long> fileIds)
    {
        return this.removeByIds(fileIds);
    }


    /**
     * 学生教师端通过ID查询列表
     *
     * @param fileIds 主键
     * @return 结果
     */
    @Override
    public List<DutpUserCommonFile> getListByIds(List<Long> fileIds)
    {
        return baseMapper.selectBatchIds(fileIds);
    }
    /**
     * 批量删除【请填写功能名称】
     *
     * @param dutpUserCommonFile 对象
     * @return 结果
     */
    @Override
    public boolean deleteDutpUserCommonFile(DutpUserCommonFile dutpUserCommonFile)
    {
        LambdaQueryWrapper<DutpUserCommonFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileName())) {
            lambdaQueryWrapper.like(DutpUserCommonFile::getFileName
                    ,dutpUserCommonFile.getFileName());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileUrl())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getFileUrl
                    ,dutpUserCommonFile.getFileUrl());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileType())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getFileType
                    ,dutpUserCommonFile.getFileType());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getFileSize())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getFileSize
                    ,dutpUserCommonFile.getFileSize());
        }
        if(ObjectUtil.isNotEmpty(dutpUserCommonFile.getBusinessId())) {
            lambdaQueryWrapper.eq(DutpUserCommonFile::getBusinessId
                    ,dutpUserCommonFile.getBusinessId());
        }
        // 获取 SQL 语句（需先初始化条件）
        String sql = lambdaQueryWrapper.getSqlSegment();
        System.out.println("Generated SQL: DELETE FROM dutp_user_common_file WHERE " + sql);
        return baseMapper.delete(lambdaQueryWrapper) > 0;
    }
}
