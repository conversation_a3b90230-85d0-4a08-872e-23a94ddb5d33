package cn.dutp.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookOrderRefundManagementMyReviewVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 我审核过得教材总数
     */
    private Integer myReviewOrderRefundNumber;
    /**
     * 教务退款订单数
     */
    private Integer academicOrderRefundNumber;
    /**
     * 教务退款商品数
     */
    private Integer academicRefundGoodsNumber;
    /**
     * 教务退款金额
     */
    private BigDecimal academicRefundAmount;
    /**
     * 教务驳回订单数
     */
    private Integer academicRefundRejectNumber;

    /**
     * 零售退款订单数
     */
    private Integer retailOrderRefundNumber;
    /**
     * 零售退款商品数
     */
    private Integer retailRefundGoodsNumber;
    /**
     * 零售退款金额
     */
    private BigDecimal retailRefundAmount;
    /**
     * 零售驳回订单数
     */
    private Integer retailRefundRejectNumber;


}