package cn.dutp.system.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-BASE-019用户设备对象 dutp_user_device
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@TableName("dutp_user_device")
public class DutpUserDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deviceId;

    /**
     * 1-PC,2-ANDROID,3-IOS,4其他
     */
    @Excel(name = "1-PC,2-ANDROID,3-IOS,4其他")
    private Integer deviceType;

    /**
     * 设备唯一ID，PC设备的MAC地址，移动端的deviceId等
     */
    @Excel(name = "设备唯一ID，PC设备的MAC地址，移动端的deviceId等")
    private String deviceUniqueId;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastLoginDate;

    /**
     * 用户ID dutp_user里userid
     */
    @Excel(name = "用户ID dutp_user里userid")
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("deviceId", getDeviceId())
                .append("deviceType", getDeviceType())
                .append("deviceUniqueId", getDeviceUniqueId())
                .append("deviceName", getDeviceName())
                .append("lastLoginDate", getLastLoginDate())
                .append("userId", getUserId())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
