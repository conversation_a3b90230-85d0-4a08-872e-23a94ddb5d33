package cn.dutp.modules.monitor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import de.codecentric.boot.admin.server.config.EnableAdminServer;

/**
 * 监控中心
 * 
 * <AUTHOR>
 */
@EnableAdminServer
@SpringBootApplication
public class DutpMonitorApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpMonitorApplication.class, args);
        System.out.println("监控中心启动成功");
    }
}
