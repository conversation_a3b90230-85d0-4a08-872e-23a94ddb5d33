
# 复制项目的文件到对应docker路径，便于一键生成镜像。

# copy jar
echo "begin copy dutp-gateway "
copy ..\dutp-gateway\target\dutp-gateway.jar .\dutp\gateway\jar

echo "begin copy dutp-auth "
copy ..\dutp-auth\target\dutp-auth.jar .\dutp\auth\jar

echo "begin copy dutp-visual "
copy ..\dutp-visual\dutp-monitor\target\dutp-visual-monitor.jar  .\dutp\visual\monitor\jar

echo "begin copy dutp-basic "
copy ..\dutp-modules\dutp-basic\target\dutp-basic.jar .\dutp\modules\basic\jar

echo "begin copy dutp-book "
copy ..\dutp-modules\dutp-book\target\dutp-book.jar .\dutp\modules\book\jar

echo "begin copy dutp-cms "
copy ..\dutp-modules\dutp-cms\target\dutp-cms.jar .\dutp\modules\cms\jar

echo "begin copy dutp-edu "
copy ..\dutp-modules\dutp-edu\target\dutp-edu.jar .\dutp\modules\edu\jar

echo "begin copy dutp-modules-file "
copy ..\dutp-modules\dutp-file\target\dutp-file.jar .\dutp\modules\file\jar

echo "begin copy dutp-im "
copy ..\dutp-modules\dutp-im\target\dutp-im.jar .\dutp\modules\im\jar

echo "begin copy dutp-message "
copy ..\dutp-modules\dutp-message\target\dutp-message.jar .\dutp\modules\message\jar

echo "begin copy dutp-qrcode "
copy ..\dutp-modules\dutp-qrcode\target\dutp-qrcode.jar .\dutp\modules\qrcode\jar

echo "begin copy dutp-shop "
copy ..\dutp-modules\dutp-shop\target\dutp-shop.jar .\dutp\modules\shop\jar

echo "begin copy dutp-modules-system "
copy ..\dutp-modules\dutp-system\target\dutp-system.jar .\dutp\modules\system\jar

echo "begin copy dutp-modules-job "
copy ..\dutp-modules\dutp-job\target\dutp-job.jar .\dutp\modules\job\jar




