# 基础镜像
FROM  registry.cn-beijing.aliyuncs.com/belllab/java:1.0.0
# author
MAINTAINER dutp

# 挂载目录
VOLUME /home/<USER>
# 创建目录
RUN mkdir -p /home/<USER>
# 指定路径
WORKDIR /home/<USER>
# 复制jar文件到路径
COPY ../jar/dutp-gateway.jar /home/<USER>/dutp-gateway.jar
# 启动认证服务
ENTRYPOINT ["java","-jar","dutp-gateway.jar"]
# ENTRYPOINT java -Xms512m -Xmx2048m -jar /home/<USER>/dutp-gateway.jar --spring.profiles.active=prod