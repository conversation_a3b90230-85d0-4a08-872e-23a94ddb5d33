version : '3.8'
services:
  dutp-nacos:
    container_name: dutp-nacos
    image: nacos/nacos-server
    build:
      context: ./nacos
    environment:
      - MODE=standalone
    volumes:
      - ./nacos/logs/:/home/<USER>/logs
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      - dutp-mysql
  dutp-mysql:
    container_name: dutp-mysql
    image: mysql:5.7
    build:
      context: ./mysql
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/logs
      - ./mysql/data:/var/lib/mysql
    command: [
          'mysqld',
          '--innodb-buffer-pool-size=80M',
          '--character-set-server=utf8mb4',
          '--collation-server=utf8mb4_unicode_ci',
          '--default-time-zone=+8:00',
          '--lower-case-table-names=1'
        ]
    environment:
      MYSQL_DATABASE: 'dutp-cloud'
      MYSQL_ROOT_PASSWORD: password
  dutp-redis:
    container_name: dutp-redis
    image: redis
    build:
      context: ./redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis/conf/redis.conf:/home/<USER>/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /home/<USER>/redis/redis.conf
  dutp-nginx:
    container_name: dutp-nginx
    image: nginx
    build:
      context: ./nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/html/dist:/home/<USER>/projects/dutp-ui
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - dutp-gateway
    links:
      - dutp-gateway
  dutp-gateway:
    container_name: dutp-gateway
    build:
      context: ./dutp/gateway
      dockerfile: dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - dutp-redis
    links:
      - dutp-redis
  dutp-auth:
    container_name: dutp-auth
    build:
      context: ./dutp/auth
      dockerfile: dockerfile
    ports:
      - "9200:9200"
    depends_on:
      - dutp-redis
    links:
      - dutp-redis
  dutp-modules-system:
    container_name: dutp-modules-system
    build:
      context: ./dutp/modules/system
      dockerfile: dockerfile
    ports:
      - "9201:9201"
    depends_on:
      - dutp-redis
      - dutp-mysql
    links:
      - dutp-redis
      - dutp-mysql
  dutp-modules-gen:
    container_name: dutp-modules-gen
    build:
      context: ./dutp/modules/gen
      dockerfile: dockerfile
    ports:
      - "9202:9202"
    depends_on:
      - dutp-mysql
    links:
      - dutp-mysql
  dutp-modules-job:
    container_name: dutp-modules-job
    build:
      context: ./dutp/modules/job
      dockerfile: dockerfile
    ports:
      - "9203:9203"
    depends_on:
      - dutp-mysql
    links:
      - dutp-mysql
  dutp-modules-file:
    container_name: dutp-modules-file
    build:
      context: ./dutp/modules/file
      dockerfile: dockerfile
    ports:
      - "9300:9300"
    volumes:
    - ./dutp/uploadPath:/home/<USER>/uploadPath
  dutp-visual-monitor:
    container_name: dutp-visual-monitor
    build:
      context: ./dutp/visual/monitor
      dockerfile: dockerfile
    ports:
      - "9100:9100"
